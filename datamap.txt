graph LR
    %% User Roles
    subgraph "👥 User Roles & Permissions"
        Admin[👑 Administrator<br/>Full Access]
        Support[🛡️ Support Role<br/>ID: 1376500798896214108]
        Staff[🛡️ Staff Role<br/>ID: 1376884726232514620]
        Player[🎮 Player Role<br/>ID: 1376905574519799900]
        Customer[👤 Customer<br/>Regular Users]
    end

    %% Command Access
    subgraph "⚡ Command Permissions"
        FinancialCmds[💰 Financial Commands<br/>/bill, /donate, /luong, /show, /resetluong]
        ProfileCmds[👤 Profile Commands<br/>/tluong, /bangluong]
        ESCmds[🌟 ES Commands<br/>es_*, profile management]
        PersonalCmds[📱 Personal Commands<br/>luong (DM only)]
    end

    %% Data Flow for Transactions
    subgraph "💳 Transaction Flow"
        NewBill[📝 New Bill Created<br/>/bill command]
        NewDonate[🎁 New Donation<br/>/donate command]
        
        NewBill --> UpdatePlayer[Update Player Data]
        NewBill --> UpdateServer[Update Server Stats]
        NewBill --> UpdateAddOn[Update Add-on Data]
        
        NewDonate --> UpdateDonate[Update Donate Data]
        NewDonate --> UpdateServerDonate[Update Server Stats]
        
        UpdatePlayer --> PlayerHistoryDB[(playerHistoryDB)]
        UpdateServer --> ServerStatsDB[(serverStatsDB)]
        UpdateAddOn --> AddOnDB[(addOnDB)]
        UpdateDonate --> DonateHistoryDB[(donateHistoryDB)]
        UpdateDonate --> PlayerDonateSalaryDB[(playerDonateSalaryDB)]
        UpdateServerDonate --> ServerStatsDB
    end

    %% Salary Calculation
    subgraph "💰 Salary System"
        Bills[📋 Bills<br/>Book/Game/Oncam<br/>80% to player]
        Donates[🎁 Donates<br/>90% to player]
        BonusSalary[➕ Bonus Salary<br/>100% to player]
        DeductSalary[➖ Deduct Salary<br/>100% from player]
        
        Bills --> TotalSalary[💵 Total Player Salary]
        Donates --> TotalSalary
        BonusSalary --> TotalSalary
        DeductSalary --> TotalSalary
        
        Bills --> ServerProfit[🏦 Server Profit<br/>20% + 10% + Add-ons]
        Donates --> ServerProfit
        AddOns[🔧 Add-ons<br/>SVR + Extra People<br/>100% to server] --> ServerProfit
    end

    %% Reset Impact
    subgraph "🔄 Reset System Impact"
        ResetPlayer[/resetluong<br/>Reset Player Salary]
        
        ResetPlayer -.->|Clears| PlayerHistoryDB
        ResetPlayer -.->|Clears| PlayerDonateSalaryDB
        ResetPlayer -.->|Clears| PlayerBonusSalaryDB
        ResetPlayer -.->|Clears| DonateHistoryDB
        
        ResetPlayer -.->|Does NOT affect| ServerStatsDB
        ResetPlayer -.->|Does NOT affect| AddOnDB
        
        ShowCommand[/show<br/>Server Statistics] -.->|Reads from| ServerStatsDB
        ShowCommand -.->|Reads from| AddOnDB
    end

    %% Permission Mapping
    Admin --> FinancialCmds
    Admin --> ProfileCmds
    Admin --> ESCmds
    
    Support --> FinancialCmds
    Support --> ProfileCmds
    Support --> ESCmds
    
    Staff --> FinancialCmds
    Staff --> ProfileCmds
    Staff --> ESCmds
    
    Player --> ProfileCmds
    Player --> PersonalCmds
    
    Customer --> PersonalCmds

    %% Styling
    classDef admin fill:#ffebee,stroke:#c62828,stroke-width:3px
    classDef support fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef player fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    classDef database fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef flow fill:#f1f8e9,stroke:#558b2f,stroke-width:1px

    class Admin admin
    class Support,Staff support
    class Player,Customer player
    class PlayerHistoryDB,ServerStatsDB,AddOnDB,DonateHistoryDB,PlayerDonateSalaryDB database
    class NewBill,NewDonate,UpdatePlayer,UpdateServer,UpdateAddOn,UpdateDonate,UpdateServerDonate flow