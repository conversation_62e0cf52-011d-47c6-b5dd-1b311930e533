graph TB
    %% Main Bot Structure
    Bot[🤖 Discord Bot<br/>The Interstellar Booking] --> Events[📡 Events]
    Bot --> Commands[⚡ Commands]
    Bot --> Data[🗄️ Data Layer]
    Bot --> Utils[🛠️ Utils]

    %% Events
    Events --> Ready[ready.js<br/>Bot startup]
    Events --> InteractionCreate[interactionCreate.js<br/>Slash commands]
    Events --> MessageCommands[messageCommands.js<br/>Prefix commands]
    Events --> AutoRespond[autorespond.js<br/>Auto profile response]

    %% Commands Structure
    Commands --> SlashCmds[💫 Slash Commands]
    Commands --> PrefixCmds[📝 Prefix Commands]

    %% Slash Commands
    SlashCmds --> Financial[💰 Financial System]
    SlashCmds --> Profile[👤 Profile System]
    SlashCmds --> Management[⚙️ Management]

    %% Financial System
    Financial --> Bill[/bill<br/>Booking management]
    Financial --> Donate[/donate<br/>Donation system]
    Financial --> Luong[/luong<br/>Salary adjustment]
    Financial --> Show[/show<br/>Revenue statistics]
    Financial --> Export[/exportluong<br/>Excel export]
    Financial --> Reset[/resetluong<br/>Reset player salary]

    %% Profile System
    Profile --> TLuong[/tluong<br/>Personal salary view]
    Profile --> BangLuong[/bangluong<br/>Salary overview]
    Profile --> ReactProfile[/react profile<br/>Profile reactions]

    %% Management
    Management --> BankTrans[/banktransaction<br/>Bank integration]

    %% Prefix Commands
    PrefixCmds --> ESCommands[🌟 Elite Squad (ES)]
    PrefixCmds --> PersonalCmds[👤 Personal Commands]

    %% ES Commands
    ESCommands --> ESDesc[es_description<br/>Create profiles]
    ESCommands --> ESEdit[es_editprofile<br/>Edit profiles]
    ESCommands --> ESSet[es_set<br/>Assign profiles]
    ESCommands --> ESImage[es_setimage<br/>Set profile images]
    ESCommands --> ESRepost[es_repostprofiles<br/>Repost profiles]
    ESCommands --> ESDel[es_delprofile<br/>Delete profiles]

    %% Personal Commands
    PersonalCmds --> Luong2[luong<br/>Personal salary DM]

    %% Data Layer
    Data --> PlayerData[👥 Player Data]
    Data --> TransactionData[💳 Transaction Data]
    Data --> ProfileData[📋 Profile Data]
    Data --> ServerData[🖥️ Server Data]

    %% Player Data
    PlayerData --> PlayerHistory[playerHistoryDB.js<br/>Booking history]
    PlayerData --> PlayerDonate[playerDonateSalaryDB.js<br/>Donate salary]
    PlayerData --> PlayerBonus[playerBonusSalaryDB.js<br/>Bonus salary]

    %% Transaction Data
    TransactionData --> DonateHistory[donateHistoryDB.js<br/>Donation history]
    TransactionData --> UserBooking[userBookingHistoryDB.js<br/>User booking history]
    TransactionData --> AddOn[addOnDB.js<br/>Add-on fees (SVR, extra)]
    TransactionData --> Cash[cashDB.js<br/>User balance]

    %% Profile Data
    ProfileData --> ProfileDB[profileDB.js<br/>Elite Squad profiles]
    ProfileData --> ReactBill[reactBillDB.js<br/>React bill data]

    %% Server Data
    ServerData --> ServerStats[serverStatsDB.js<br/>Server statistics]
    ServerData --> Giveaway[giveawayDB.js<br/>Giveaway system]

    %% Utils
    Utils --> Format[formatUtils.js<br/>Number formatting]
    Utils --> ServiceInfo[sendServiceInfo.js<br/>Service information]
    Utils --> ShopRings[sendShoprings.js<br/>Shop rings info]

    %% Data Flow
    Bill -.->|Updates| PlayerHistory
    Bill -.->|Updates| ServerStats
    Bill -.->|Updates| AddOn
    Donate -.->|Updates| DonateHistory
    Donate -.->|Updates| PlayerDonate
    Donate -.->|Updates| ServerStats
    Luong -.->|Updates| PlayerBonus
    Luong -.->|Updates| DonateHistory
    Reset -.->|Clears| PlayerHistory
    Reset -.->|Clears| PlayerDonate
    Reset -.->|Clears| PlayerBonus
    Show -.->|Reads| ServerStats
    Show -.->|Reads| AddOn

    %% Styling
    classDef financial fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef profile fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef data fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef management fill:#fff3e0,stroke:#e65100,stroke-width:2px

    class Financial,Bill,Donate,Luong,Show,Export,Reset financial
    class Profile,TLuong,BangLuong,ReactProfile,ESCommands,ESDesc,ESEdit,ESSet,ESImage,ESRepost,ESDel profile
    class Data,PlayerData,TransactionData,ProfileData,ServerData,PlayerHistory,PlayerDonate,PlayerBonus,DonateHistory,UserBooking,AddOn,Cash,ProfileDB,ReactBill,ServerStats,Giveaway data
    class Management,BankTrans management