const { REST, Routes } = require('discord.js');
const { clientId, guildId, token } = require('./config.json');

const rest = new REST().setToken(token);

(async () => {
  try {
    // <PERSON>óa tất cả lệnh slash ở server (guild)
    await rest.put(
      Routes.applicationGuildCommands(clientId, guildId),
      { body: [] },
    );
    console.log('Đã xóa tất cả lệnh slash trong guild!');
  } catch (error) {
    console.error(error);
  }
})();