const fs = require('fs');
const path = require('path');
const axios = require('axios');
const crypto = require('crypto');
require('dotenv').config();
const keywordsPath = path.join(__dirname, '../data/keywords.json');
const { PermissionFlagsBits, EmbedBuilder } = require('discord.js');

// Hàm kiểm tra link ảnh
function isImageUrl(url) {
  return typeof url === 'string'
    && url.startsWith('http')
    && (
      url.match(/\.(jpg|jpeg|png|gif|webp)(\?.*)?$/i)
      || url.includes('cdn.discordapp.com')
      || url.includes('media.discordapp.net')
    );
}

const imagesDir = path.join(__dirname, '../images');
if (!fs.existsSync(imagesDir)) fs.mkdirSync(imagesDir, { recursive: true });

module.exports = {
  name: 'ar',
  async execute(client, message, args) {
    try {

      // Ki<PERSON><PERSON> tra quyền: Administrator hoặc role cụ thể
      const hasAdminPermission = message.member.permissions.has(PermissionFlagsBits.Administrator);
      const hasSpecialRole = message.member.roles.cache.has('1376500798896214108') ||
                            message.member.roles.cache.has('1376884726232514620');

      if (!hasAdminPermission && !hasSpecialRole) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      // Kiểm tra subcommands
      const subcommand = args[0]?.toLowerCase();

      if (subcommand === 'iurl') {
        return this.setImageUrl(client, message, args.slice(1));
      }

      if (subcommand === 'turl') {
        return this.setThumbnailUrl(client, message, args.slice(1));
      }

      if (subcommand === 'preview') {
        return this.previewAutoresponse(client, message, args.slice(1));
      }

      if (subcommand === 'content') {
        return this.editContent(client, message, args.slice(1));
      }

      if (subcommand === 'del' || subcommand === 'delete') {
        return this.deleteAutoresponse(client, message, args.slice(1));
      }

      if (subcommand === 'help') {
        return this.showDetailedHelp(client, message);
      }
      
    const input = args.join(' ').split('|').map(s => s.trim());
    if (input.length < 1) {
      const failEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
        .setDescription('**Cú pháp tạo mới:** `ar <từ khóa>|<phản hồi>|<link ảnh (tùy chọn)>`\n\n**Các subcommands phổ biến:**\n`ar iurl <keyword>` + đính kèm ảnh - Gán image\n`ar content <keyword>|<nội dung mới>` - Sửa nội dung\n`ar del <keyword>` - Xóa autoresponse\n`ar help` - Xem hướng dẫn chi tiết\n\n**Biến có sẵn:** `{user}` - Mention user\n\n**Ví dụ:** `ar hello|{user} Xin chào!`\n\n💡 **Sử dụng `ar help` để xem hướng dẫn đầy đủ!**')
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      return message.reply({ embeds: [failEmbed] });
    }
    const [keyword, response = '', image, embedType] = input;

    let keywords = [];
    if (fs.existsSync(keywordsPath)) {
      keywords = JSON.parse(fs.readFileSync(keywordsPath, 'utf8'));
    }
    if (keywords.some(item => item.keyword === keyword)) {
      const failEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
        .setDescription('<:error:1383005371542798346> | Từ khóa này đã tồn tại!')
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      return message.reply({ embeds: [failEmbed] });
    }

    // Ưu tiên: đính kèm > image > response (nếu là link ảnh)
    let imageUrl = '';
    if (message.attachments && message.attachments.size > 0) {
      // Luôn lấy url file đính kèm đầu tiên (Discord chỉ cho gửi ảnh hoặc file)
      const attachment = message.attachments.first();
      imageUrl = attachment.url; // Không cần kiểm tra đuôi file
    }

    if (!imageUrl) {
      if (image && isImageUrl(image)) {
        imageUrl = image;
      } else if (response && isImageUrl(response)) {
        imageUrl = response;
      }
    }

    // Nếu không có response, không có image, không có embedType và không có file đính kèm thì mới báo lỗi
    if (!response && !image && !embedType && !imageUrl) {
      const failEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
        .setDescription('<:error:1383005371542798346> | Bạn phải nhập nội dung phản hồi, kèm hình ảnh hoặc embedType!')
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      return message.reply({ embeds: [failEmbed] });
    }

    // Nếu response là link ảnh, chuyển sang image, response để rỗng
    let realResponse = response;
    let realImage = image;

    // Nếu có ảnh (đính kèm hoặc link), tải về máy chủ
    if (imageUrl) {
      try {
        // Tạo tên file ngẫu nhiên theo hash
        const ext = path.extname(imageUrl.split('?')[0]) || '.jpg';
        const fileName = crypto.randomBytes(8).toString('hex') + ext;
        const filePath = path.join(imagesDir, fileName);

        // Tải ảnh về
        const res = await axios.get(imageUrl, { responseType: 'stream' });
        await new Promise((resolve, reject) => {
          const stream = res.data.pipe(fs.createWriteStream(filePath));
          stream.on('finish', resolve);
          stream.on('error', reject);
        });

        realImage = `images/${fileName}`; // Lưu đường dẫn local
        if (isImageUrl(response) && !image) realResponse = '';
      } catch (err) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Không thể tải ảnh về máy chủ!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }
    } else if (isImageUrl(response) && !image) {
      realImage = response;
      realResponse = '';
    }

    // Lưu vào keywords.json
    keywords.push({
      keyword,
      response: realResponse,
      image: realImage || '',
      embedType: embedType || null
    });
    fs.writeFileSync(keywordsPath, JSON.stringify(keywords, null, 2));;

    const successEmbed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION SUCCESSFUL', iconURL: client.user.displayAvatarURL() })
      .setDescription(
        `<:done:1383009630581424250> | Đã thêm từ khóa auto respond: "${keyword}"`
      )
      .setColor(client.embedColor || '#0099ff')
      .setTimestamp();
    message.reply({ embeds: [successEmbed] });
    } catch (error) {
      console.error('Lỗi khi thêm từ khóa:', error);
      
      // Xử lý lỗi an toàn hơn
      try {
        if (message.channel && typeof message.channel.send === 'function') {
          const failEmbed = new EmbedBuilder()
            .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
            .setDescription(`<:error:1383005371542798346> | Đã xảy ra lỗi: ${error.message}`)
            .setColor(client.embedColor || '#0099ff')
            .setTimestamp();
          return message.channel.send({ embeds: [failEmbed] });
        }
      } catch (sendError) {
        console.error('Không thể gửi thông báo lỗi:', sendError);
      }
    }
  },

  // Phương thức gán image cho autoresponse đã có
  async setImageUrl(client, message, args) {
    try {
      if (args.length === 0) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Vui lòng nhập keyword!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      const keyword = args[0];

      // Kiểm tra có file đính kèm không
      if (!message.attachments || message.attachments.size === 0) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Vui lòng đính kèm hình ảnh!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      // Đọc keywords
      let keywords = [];
      if (fs.existsSync(keywordsPath)) {
        keywords = JSON.parse(fs.readFileSync(keywordsPath, 'utf8'));
      }

      // Tìm keyword
      const keywordIndex = keywords.findIndex(item => item.keyword === keyword);
      if (keywordIndex === -1) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription(`<:error:1383005371542798346> | Không tìm thấy keyword: "${keyword}"`)
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      // Lấy file đính kèm
      const attachment = message.attachments.first();
      const imageUrl = attachment.url;

      // Tải ảnh về máy chủ
      try {
        const ext = path.extname(imageUrl.split('?')[0]) || '.jpg';
        const fileName = crypto.randomBytes(8).toString('hex') + ext;
        const filePath = path.join(imagesDir, fileName);

        const res = await axios.get(imageUrl, { responseType: 'stream' });
        await new Promise((resolve, reject) => {
          const stream = res.data.pipe(fs.createWriteStream(filePath));
          stream.on('finish', resolve);
          stream.on('error', reject);
        });

        // Xóa ảnh cũ nếu có
        const oldImage = keywords[keywordIndex].image;
        if (oldImage && oldImage.startsWith('images/')) {
          const oldFilePath = path.join(__dirname, '../', oldImage);
          if (fs.existsSync(oldFilePath)) {
            fs.unlinkSync(oldFilePath);
          }
        }

        // Cập nhật image
        keywords[keywordIndex].image = `images/${fileName}`;
        fs.writeFileSync(keywordsPath, JSON.stringify(keywords, null, 2));

        const successEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION SUCCESSFUL', iconURL: client.user.displayAvatarURL() })
          .setDescription(`<:done:1383009630581424250> | Đã gán image cho keyword: "${keyword}"`)
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [successEmbed] });

      } catch (err) {
        console.error('Lỗi khi tải ảnh:', err);
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Không thể tải ảnh về máy chủ!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

    } catch (error) {
      console.error('Lỗi trong setImageUrl:', error);
      const failEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
        .setDescription(`<:error:1383005371542798346> | Đã xảy ra lỗi: ${error.message}`)
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      return message.reply({ embeds: [failEmbed] });
    }
  },

  // Phương thức gán thumbnail cho autoresponse đã có
  async setThumbnailUrl(client, message, args) {
    try {
      if (args.length === 0) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Vui lòng nhập keyword!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      const keyword = args[0];

      // Kiểm tra có file đính kèm không
      if (!message.attachments || message.attachments.size === 0) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Vui lòng đính kèm hình ảnh!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      // Đọc keywords
      let keywords = [];
      if (fs.existsSync(keywordsPath)) {
        keywords = JSON.parse(fs.readFileSync(keywordsPath, 'utf8'));
      }

      // Tìm keyword
      const keywordIndex = keywords.findIndex(item => item.keyword === keyword);
      if (keywordIndex === -1) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription(`<:error:1383005371542798346> | Không tìm thấy keyword: "${keyword}"`)
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      // Lấy file đính kèm
      const attachment = message.attachments.first();
      const imageUrl = attachment.url;

      // Tải ảnh về máy chủ
      try {
        const ext = path.extname(imageUrl.split('?')[0]) || '.jpg';
        const fileName = crypto.randomBytes(8).toString('hex') + ext;
        const filePath = path.join(imagesDir, fileName);

        const res = await axios.get(imageUrl, { responseType: 'stream' });
        await new Promise((resolve, reject) => {
          const stream = res.data.pipe(fs.createWriteStream(filePath));
          stream.on('finish', resolve);
          stream.on('error', reject);
        });

        // Xóa thumbnail cũ nếu có
        const oldThumbnail = keywords[keywordIndex].thumbnail;
        if (oldThumbnail && oldThumbnail.startsWith('images/')) {
          const oldFilePath = path.join(__dirname, '../', oldThumbnail);
          if (fs.existsSync(oldFilePath)) {
            fs.unlinkSync(oldFilePath);
          }
        }

        // Cập nhật thumbnail
        keywords[keywordIndex].thumbnail = `images/${fileName}`;
        fs.writeFileSync(keywordsPath, JSON.stringify(keywords, null, 2));

        const successEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION SUCCESSFUL', iconURL: client.user.displayAvatarURL() })
          .setDescription(`<:done:1383009630581424250> | Đã gán thumbnail cho keyword: "${keyword}"`)
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [successEmbed] });

      } catch (err) {
        console.error('Lỗi khi tải ảnh:', err);
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Không thể tải ảnh về máy chủ!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

    } catch (error) {
      console.error('Lỗi trong setThumbnailUrl:', error);
      const failEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
        .setDescription(`<:error:1383005371542798346> | Đã xảy ra lỗi: ${error.message}`)
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      return message.reply({ embeds: [failEmbed] });
    }
  },

  // Phương thức preview autoresponse với biến {user}
  async previewAutoresponse(client, message, args) {
    try {
      if (args.length === 0) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Vui lòng nhập keyword!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      const keyword = args[0];

      // Đọc keywords
      let keywords = [];
      if (fs.existsSync(keywordsPath)) {
        keywords = JSON.parse(fs.readFileSync(keywordsPath, 'utf8'));
      }

      // Tìm keyword
      const item = keywords.find(k => k.keyword === keyword);
      if (!item) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription(`<:error:1383005371542798346> | Không tìm thấy keyword: "${keyword}"`)
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      // Thay thế biến {user} để preview
      let previewResponse = item.response || '';
      if (previewResponse.includes('{user}')) {
        previewResponse = previewResponse.replace(/{user}/g, `<@${message.author.id}>`);
      }

      // Tạo preview embed
      const previewEmbed = new EmbedBuilder()
        .setAuthor({ name: 'AUTORESPONSE PREVIEW', iconURL: client.user.displayAvatarURL() })
        .setTitle(`🔍 Preview keyword: "${keyword}"`)
        .setDescription(
          `**Response gốc:**\n\`\`\`${item.response || 'Không có'}\`\`\`\n\n` +
          `**Response sau khi xử lý:**\n${previewResponse || 'Không có'}\n\n` +
          `**Image:** ${item.image ? 'Có' : 'Không'}\n` +
          `**Thumbnail:** ${item.thumbnail ? 'Có' : 'Không'}`
        )
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp()
        .setFooter({ text: 'Biến {user} sẽ được thay thế bằng mention của user gọi autoresponse' });

      // Thêm image và thumbnail nếu có
      if (item.image && typeof item.image === 'string') {
        if (item.image.startsWith('http')) {
          previewEmbed.setImage(item.image);
        } else if (fs.existsSync(path.join(__dirname, '../', item.image))) {
          previewEmbed.setImage('attachment://' + path.basename(item.image));
        }
      }

      if (item.thumbnail && typeof item.thumbnail === 'string') {
        if (item.thumbnail.startsWith('http')) {
          previewEmbed.setThumbnail(item.thumbnail);
        } else if (fs.existsSync(path.join(__dirname, '../', item.thumbnail))) {
          previewEmbed.setThumbnail('attachment://' + path.basename(item.thumbnail));
        }
      }

      // Gửi preview
      const files = [];
      if (item.image && !item.image.startsWith('http') && fs.existsSync(path.join(__dirname, '../', item.image))) {
        files.push(path.join(__dirname, '../', item.image));
      }
      if (item.thumbnail && !item.thumbnail.startsWith('http') && fs.existsSync(path.join(__dirname, '../', item.thumbnail))) {
        files.push(path.join(__dirname, '../', item.thumbnail));
      }

      return message.reply({
        embeds: [previewEmbed],
        files: files.length > 0 ? files : undefined
      });

    } catch (error) {
      console.error('Lỗi trong previewAutoresponse:', error);
      const failEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
        .setDescription(`<:error:1383005371542798346> | Đã xảy ra lỗi: ${error.message}`)
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      return message.reply({ embeds: [failEmbed] });
    }
  },

  // Phương thức chỉnh sửa nội dung autoresponse
  async editContent(client, message, args) {
    try {
      const input = args.join(' ').split('|').map(s => s.trim());

      if (input.length < 2) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Vui lòng nhập đầy đủ thông tin!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      const [keyword, newContent] = input;

      if (!keyword || !newContent) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Keyword và nội dung không được để trống!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      // Đọc keywords
      let keywords = [];
      if (fs.existsSync(keywordsPath)) {
        keywords = JSON.parse(fs.readFileSync(keywordsPath, 'utf8'));
      }

      // Tìm keyword
      const keywordIndex = keywords.findIndex(item => item.keyword === keyword);
      if (keywordIndex === -1) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription(`<:error:1383005371542798346> | Không tìm thấy keyword: "${keyword}"`)
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      // Lưu nội dung cũ để hiển thị
      const oldContent = keywords[keywordIndex].response || 'Không có';

      // Cập nhật nội dung
      keywords[keywordIndex].response = newContent;
      fs.writeFileSync(keywordsPath, JSON.stringify(keywords, null, 2));

      const successEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION SUCCESSFUL', iconURL: client.user.displayAvatarURL() })
        .setTitle(`<:done:1383009630581424250> | Đã cập nhật nội dung keyword: "${keyword}"`)
        .setDescription(
          `**Nội dung cũ:**\n\`\`\`${oldContent}\`\`\`\n\n` +
          `**Nội dung mới:**\n\`\`\`${newContent}\`\`\``
        )
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp()
        .setFooter({ text: 'Content đã được cập nhật thành công!' });

      return message.reply({ embeds: [successEmbed] });

    } catch (error) {
      console.error('Lỗi trong editContent:', error);
      const failEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
        .setDescription(`<:error:1383005371542798346> | Đã xảy ra lỗi: ${error.message}`)
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      return message.reply({ embeds: [failEmbed] });
    }
  },

  // Phương thức xóa autoresponse
  async deleteAutoresponse(client, message, args) {
    try {
      if (args.length === 0) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Vui lòng nhập keyword cần xóa')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      const keyword = args.join(' ').trim();

      if (!keyword) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Keyword không được để trống!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      // Đọc keywords
      let keywords = [];
      if (fs.existsSync(keywordsPath)) {
        keywords = JSON.parse(fs.readFileSync(keywordsPath, 'utf8'));
      }

      // Tìm keyword
      const keywordIndex = keywords.findIndex(item => item.keyword === keyword);
      if (keywordIndex === -1) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription(`<:error:1383005371542798346> | Không tìm thấy keyword: "${keyword}"`)
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      const item = keywords[keywordIndex];

      // Lưu thông tin để hiển thị
      const deletedInfo = {
        keyword: item.keyword,
        response: item.response || 'Không có',
        hasImage: !!item.image,
        hasThumbnail: !!item.thumbnail,
        hasUserVariable: item.response ? item.response.includes('{user}') : false
      };

      // Xóa file image nếu có (file local)
      if (item.image && typeof item.image === 'string' && !item.image.startsWith('http')) {
        const imgPath = path.join(__dirname, '../', item.image);
        if (fs.existsSync(imgPath)) {
          try {
            fs.unlinkSync(imgPath);
            console.log(`✅ Đã xóa file image: ${imgPath}`);
          } catch (e) {
            console.error('❌ Không thể xóa file image:', e);
          }
        }
      }

      // Xóa file thumbnail nếu có (file local)
      if (item.thumbnail && typeof item.thumbnail === 'string' && !item.thumbnail.startsWith('http')) {
        const thumbPath = path.join(__dirname, '../', item.thumbnail);
        if (fs.existsSync(thumbPath)) {
          try {
            fs.unlinkSync(thumbPath);
            console.log(`✅ Đã xóa file thumbnail: ${thumbPath}`);
          } catch (e) {
            console.error('❌ Không thể xóa file thumbnail:', e);
          }
        }
      }

      // Xóa keyword khỏi array
      keywords.splice(keywordIndex, 1);
      fs.writeFileSync(keywordsPath, JSON.stringify(keywords, null, 2));

      const successEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION SUCCESSFUL', iconURL: client.user.displayAvatarURL() })
        .setDescription(
          `<:done:1383009630581424250> | Đã xóa thành công từ khóa \`${keyword}\`` 
        )
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp()
        .setFooter({ text: 'Autoresponse đã được xóa thành công!' });

      return message.reply({ embeds: [successEmbed] });

    } catch (error) {
      console.error('Lỗi trong deleteAutoresponse:', error);

      // Xử lý lỗi an toàn
      try {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription(`<:error:1383005371542798346> | Đã xảy ra lỗi: ${error.message}`)
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      } catch (sendError) {
        console.error('Không thể gửi thông báo lỗi:', sendError);
      }
    }
  },

  // Phương thức hiển thị help chi tiết
  async showDetailedHelp(client, message) {
    try {
      const helpEmbed = new EmbedBuilder()
        .setAuthor({ name: 'AUTORESPONSE SYSTEM HELP', iconURL: client.user.displayAvatarURL() })
        .setTitle('Autoresponse - Hướng dẫn chi tiết')
        .addFields(
          {
            name: 'Tạo Autoresponse Mới',
            value: '```ar <keyword>|<response>|<image_url>```\n' +
                   '**Ví dụ:**\n' +
                   '`ar hello|{user} Xin chào!`\n' +
                   '`ar fiin|{user} đã gọi Fiin!|https://image.com/fiin.jpg`',
            inline: false
          },
          {
            name: 'Quản lý Hình ảnh',
            value: '```ar iurl <keyword>``` + đính kèm ảnh - Gán image chính\n' +
                   '```ar turl <keyword>``` + đính kèm ảnh - Gán thumbnail\n\n' +
                   '**Lưu ý:** Đính kèm ảnh vào tin nhắn khi sử dụng lệnh',
            inline: false
          },
          {
            name: 'Chỉnh sửa Nội dung',
            value: '```ar content <keyword>|<nội_dung_mới>```\n' +
                   '**Ví dụ:**\n' +
                   '`ar content hello|{user} Chào bạn nhé!`\n' +
                   '`ar content fiin|{user} đã gọi Fiin ra đây!`',
            inline: false
          },
          {
            name: 'Xem trước & Quản lý',
            value: '```ar preview <keyword>``` - Xem trước autoresponse\n' +
                   '```ar del <keyword>``` - Xóa autoresponse\n' +
                   '```ar help``` - Hiển thị hướng dẫn này',
            inline: false
          },
          {
            name: 'Variables',
            value: '**`{user}`** - Tự động mention user đã gọi autoresponse\n\n' +
                   '**Ví dụ sử dụng:**\n' +
                   '• `{user} Xin chào!` → `@username Xin chào!`\n' +
                   '• `Chào mừng {user}!` → `Chào mừng @username!`',
            inline: false
          },
          {
            name: 'Workflow Khuyến nghị',
            value: '**1.** Tạo autoresponse cơ bản: `ar hello|{user} Xin chào!`\n' +
                   '**2.** Thêm image: `ar iurl hello` + đính kèm ảnh\n' +
                   '**3.** Thêm thumbnail: `ar turl hello` + đính kèm ảnh\n' +
                   '**4.** Xem trước: `ar preview hello`\n' +
                   '**5.** Chỉnh sửa nếu cần: `ar content hello|{user} Chào bạn!`',
            inline: false
          }
        )
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp()
        .setFooter({
          text: 'Autoresponse System | Chỉ Admin/Staff có thể sử dụng',
          iconURL: message.guild.iconURL()
        });

      return message.reply({ embeds: [helpEmbed] });

    } catch (error) {
      console.error('Lỗi trong showDetailedHelp:', error);
      const failEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
        .setDescription(`<:error:1383005371542798346> | Đã xảy ra lỗi: ${error.message}`)
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      return message.reply({ embeds: [failEmbed] });
    }
  }
};