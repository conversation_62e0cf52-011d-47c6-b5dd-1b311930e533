const { EmbedBuilder } = require('discord.js');

module.exports = {
  name: 'av',
  async execute(client, message, args) {
    // Nếu có mention user thì lấy user đó, không thì lấy chính người gửi
    const user = message.mentions.users.first() || message.author;
    const avatarUrl = user.displayAvatarURL({ dynamic: true, size: 512 });

    const embed = new EmbedBuilder()
      .setAuthor({ name: user.username, iconURL: user.displayAvatarURL() })
      .setColor(client.embedColor || '#0099ff')
      .setImage(avatarUrl)
      .setFooter({ text: `Hôm nay lúc ${new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}` });

    await message.channel.send({ embeds: [embed] });
  }
};