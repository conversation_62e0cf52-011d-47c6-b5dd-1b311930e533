const { PermissionFlagsBits, EmbedBuilder } = require('discord.js');

module.exports = {
    name: 'ban',
    description: '<PERSON> (cấm) một thành viên khỏi server',
    usage: 'ban @user [lý do]',
    async execute(client, message, args) {
        // <PERSON><PERSON><PERSON> tra quyền: Administrator hoặc role cụ thể
        const hasAdminPermission = message.member.permissions.has(PermissionFlagsBits.Administrator);
        const hasSpecialRole = message.member.roles.cache.has('1376884726232514620');

        if (!hasAdminPermission && !hasSpecialRole) {
            const failEmbed = new EmbedBuilder()
                .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!')
                .setFooter({ text: `${message.author.username} - <PERSON>ôm nay lúc ${new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}`, iconURL: message.author.displayAvatarURL() })
                .setColor(client.embedColor || '#0099ff');
            return message.reply({ embeds: [failEmbed] });
        }
        
        if (!args[0]) {
            const failEmbed = new EmbedBuilder()
                .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Vui lòng tag thành viên hoặc nhập ID thành viên cần ban!')
                .setFooter({ text: `${message.author.username} - Hôm nay lúc ${new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}`, iconURL: message.author.displayAvatarURL() })
                .setColor(client.embedColor || '#0099ff');
            return message.reply({ embeds: [failEmbed] });
        }
        
        const member = message.mentions.members.first()
            || await message.guild.members.fetch(args[0]).catch(() => null);
        
        if (!member) {
            const failEmbed = new EmbedBuilder()
                .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Không tìm thấy thành viên này!')
                .setFooter({ text: `${message.author.username} - Hôm nay lúc ${new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}`, iconURL: message.author.displayAvatarURL() })
                .setColor(client.embedColor || '#0099ff');
            return message.reply({ embeds: [failEmbed] });
        }
        
        if (!member.bannable) {
            const failEmbed = new EmbedBuilder()
                .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
                .setDescription(`<:error:1383005371542798346> | Không thể ban thành viên **${member.user.tag}**!`)
                .setFooter({ text: `${message.author.username} - Hôm nay lúc ${new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}`, iconURL: message.author.displayAvatarURL() })
                .setColor(client.embedColor || '#0099ff');
            return message.reply({ embeds: [failEmbed] });
        }
        
        const reason = args.slice(1).join(' ') || `Bị ban bởi ${message.author.tag}`;
        
        try {
            await member.ban({ reason });
            
            // Thông báo thành công
            const successEmbed = new EmbedBuilder()
                .setAuthor({ name: 'ACTION SUCCESSFUL', iconURL: client.user.displayAvatarURL() })
                .setDescription(`<:done:1383009630581424250> | Đã ban **${member.user.tag}** khỏi server!${reason ? `\n\n**Lý do:** ${reason}` : ''}`)
                .setFooter({ text: `${message.author.username} - Hôm nay lúc ${new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}`, iconURL: message.author.displayAvatarURL() })
                .setColor(client.embedColor || '#0099ff');
            return message.reply({ embeds: [successEmbed] });
        } catch (err) {
            const failEmbed = new EmbedBuilder()
                .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
                .setDescription(`<:error:1383005371542798346> | Không thể ban thành viên **${member.user.tag}**!\n\n**Lỗi:** ${err.message}`)
                .setFooter({ text: `${message.author.username} - Hôm nay lúc ${new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}`, iconURL: message.author.displayAvatarURL() })
                .setColor(client.embedColor || '#0099ff');
            return message.reply({ embeds: [failEmbed] });
        }
    }
};