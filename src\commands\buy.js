const { EmbedBuilder } = require('discord.js');
const cashDB = require('../data/cashDB');
const purchaseDB = require('../data/purchaseDB');

// Danh sách item shop
const shopItems = [
  { id: '01', emoji: '<:fistdate:1395218976115789925>', name: '**Fist date**', price: 100000 },
  { id: '02', emoji: '<:eternia:1395218967681040527>', name: '**Eternia**', price: 200000 },
  { id: '03', emoji: '<:amora:1395218962815651924>', name: '**Amora**', price: 300000 },
  { id: '04', emoji: '<:serenity:1395218985401716867>', name: '**Serenity**', price: 400000 },
  { id: '05', emoji: '<:unity:1395218993220026418>', name: '**Unity**', price: 500000 },
];

module.exports = {
  name: 'buy',
  description: 'Mua nhẫn từ shoprings',
  usage: '<mã số item> <số lượng>',
  async execute(client, message, args) {
    try {
      if (args.length < 2) {
        const syntaxEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Cú pháp: hbuy <mã số item> <số lượng>')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [syntaxEmbed] });
      }
      
      const itemId = args[0];
      const quantity = parseInt(args[1]);
      
      if (!/^[0-9]{2}$/.test(itemId)) {
        const invalidIdEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Mã số item không hợp lệ.')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [invalidIdEmbed] });
      }
      
      if (isNaN(quantity) || quantity < 1) {
        const invalidQuantityEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Số lượng phải là số nguyên dương.')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [invalidQuantityEmbed] });
      }
      
      const item = shopItems.find(i => i.id === itemId);
      if (!item) {
        const notFoundEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Không tìm thấy vật phẩm với mã số này.')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [notFoundEmbed] });
      }
      
      const totalPrice = item.price * quantity;
      const userId = message.author.id;
      const userCash = await cashDB.getCash(userId);
      console.log(`User ${userId} cash:`, userCash.amount, 'Required:', totalPrice);
      
      if (userCash.amount < totalPrice) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription(`<:error:1383005371542798346> | Bạn không đủ tiền để mua vật phẩm này.`)
          .setFooter({ text: `${message.author.username} - Hôm nay lúc ${new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}`, iconURL: message.author.displayAvatarURL() })
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }
      
      await cashDB.subtractCash(userId, totalPrice);
      await purchaseDB.addPurchase(userId, itemId, quantity);

      // Gửi log về kênh
      const logChannel = await client.channels.fetch('1379553680294019082').catch(() => null);
      if (logChannel) {
        const logEmbed = new EmbedBuilder()
          .setColor(client.embedColor || '#0099ff')
          .setAuthor({ name: 'TRANSACTION HISTORY', iconURL: client.user.displayAvatarURL() })
          .setDescription(`**Loại giao dịch: Mua vật phẩm**\n\nNgười dùng: <@${userId}>\nVật phẩm: ${item.emoji} **${item.name}** (x${quantity})\nSố tiền trừ: **-${totalPrice.toLocaleString('vi-VN')}**đ\nThời gian: <t:${Math.floor(Date.now()/1000)}:F>`)
          .setTimestamp();
        logChannel.send({ embeds: [logEmbed] });
      }
      
      const embed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
        .setDescription(`<:done:1383009630581424250> | Đã mua thành công sản phẩm ${item.emoji} **${item.name}** ( \`x${quantity}\` ).`)
        .setFooter({ text: `${message.author.username} - Hôm nay lúc ${new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}`, iconURL: message.author.displayAvatarURL() })
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      return message.reply({ embeds: [embed] });
      
    } catch (err) {
      console.error('Lỗi trong lệnh buy:', err);
      const errorEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION SUCCESSFUL', iconURL: client.user.displayAvatarURL() })
        .setDescription('<:done:1383009630581424250> | Đã xảy ra lỗi khi mua vật phẩm!')
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      try {
        return message.reply({ embeds: [errorEmbed] });
      } catch (replyError) {
        console.error('Không thể gửi thông báo lỗi:', replyError);
      }
    }
  },
};