const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
  name: 'count',
  description: '<PERSON><PERSON><PERSON> số lượng tin nhắn của users trong kênh và xếp hạng',
  usage: 'count #kênh [số_lượng_tin_nhắn_tối_đa]\nVí dụ: count #general 1000',
  aliases: ['messagecount', 'msgcount'],

  async execute(client, message, args) {
    try {
      // Kiểm tra quyền: Administrator hoặc role cụ thể
      const hasAdminPermission = message.member.permissions.has(PermissionFlagsBits.Administrator);
      const hasSpecialRole = message.member.roles.cache.has('1376500798896214108') ||
                            message.member.roles.cache.has('1376884726232514620');

      if (!hasAdminPermission && !hasSpecialRole) {
        return message.reply('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!');
      }

      // <PERSON>ểm tra tham số
      if (args.length === 0) {
        const helpEmbed = new EmbedBuilder()
          .setColor(client.embedColor || '#0099ff')
          .setAuthor({ name: 'MESSAGE COUNT HELP', iconURL: client.user.displayAvatarURL() })
          .setDescription(
            '**Cách sử dụng:**\n' +
            '`count #kênh [số_tin_nhắn]`\n\n' +
            '**Tham số:**\n' +
            '• `#kênh` - Kênh cần đếm tin nhắn (bắt buộc)\n' +
            '• `số_tin_nhắn` - Số tin nhắn tối đa cần quét (tùy chọn, mặc định: 1000)\n\n' +
            '**Ví dụ:**\n' +
            '`count #general` - Đếm 1000 tin nhắn gần nhất\n' +
            '`count #general 500` - Đếm 500 tin nhắn gần nhất\n' +
            '`count #general 2000` - Đếm 2000 tin nhắn gần nhất\n\n' +
            '**Lưu ý:**\n' +
            '• Chỉ đếm tin nhắn từ users, không đếm bot\n' +
            '• Kết quả sẽ được xếp hạng từ cao đến thấp\n' +
            '• Giới hạn tối đa: 5000 tin nhắn'
          )
          .setTimestamp();

        return message.reply({ embeds: [helpEmbed] });
      }

      // Lấy kênh từ mention hoặc ID
      let targetChannel = null;
      const channelMention = args[0];
      
      if (channelMention.startsWith('<#') && channelMention.endsWith('>')) {
        const channelId = channelMention.slice(2, -1);
        targetChannel = message.guild.channels.cache.get(channelId);
      } else if (/^\d+$/.test(channelMention)) {
        targetChannel = message.guild.channels.cache.get(channelMention);
      }

      if (!targetChannel || targetChannel.type !== 0) { // 0 = GUILD_TEXT
        const errorEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Kênh không hợp lệ! Vui lòng mention kênh text hoặc nhập ID kênh.')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [errorEmbed] });
      }

      // Lấy số lượng tin nhắn cần quét
      let messageLimit = 1000; // Mặc định
      if (args[1]) {
        const inputLimit = parseInt(args[1]);
        if (isNaN(inputLimit) || inputLimit < 1) {
          const errorEmbed = new EmbedBuilder()
            .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
            .setDescription('<:error:1383005371542798346> | Số lượng tin nhắn không hợp lệ! Vui lòng nhập số nguyên dương.')
            .setColor(client.embedColor || '#0099ff')
            .setTimestamp();
          return message.reply({ embeds: [errorEmbed] });
        }
        
        if (inputLimit > 5000) {
          const errorEmbed = new EmbedBuilder()
            .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
            .setDescription('<:error:1383005371542798346> | Số lượng tin nhắn tối đa là 5000!')
            .setColor(client.embedColor || '#0099ff')
            .setTimestamp();
          return message.reply({ embeds: [errorEmbed] });
        }
        
        messageLimit = inputLimit;
      }

      // Gửi tin nhắn đang xử lý
      const processingEmbed = new EmbedBuilder()
        .setAuthor({ name: 'PROCESSING...', iconURL: client.user.displayAvatarURL() })
        .setDescription(`🔄 | Đang quét ${messageLimit.toLocaleString('vi-VN')} tin nhắn trong ${targetChannel}...\n\n⏳ Vui lòng đợi, quá trình này có thể mất vài phút.`)
        .setColor('#ffaa00')
        .setTimestamp();
      
      const processingMsg = await message.reply({ embeds: [processingEmbed] });

      // Đếm tin nhắn
      const userMessageCount = new Map();
      let totalMessages = 0;
      let scannedMessages = 0;
      
      try {
        let lastMessageId = null;
        const batchSize = 100; // Discord API limit
        
        while (scannedMessages < messageLimit) {
          const remainingMessages = messageLimit - scannedMessages;
          const currentBatchSize = Math.min(batchSize, remainingMessages);
          
          const fetchOptions = { limit: currentBatchSize };
          if (lastMessageId) {
            fetchOptions.before = lastMessageId;
          }
          
          const messages = await targetChannel.messages.fetch(fetchOptions);
          
          if (messages.size === 0) {
            break; // Không còn tin nhắn nào
          }
          
          messages.forEach(msg => {
            // Chỉ đếm tin nhắn từ users (không phải bot)
            if (!msg.author.bot) {
              const userId = msg.author.id;
              const currentCount = userMessageCount.get(userId) || 0;
              userMessageCount.set(userId, currentCount + 1);
              totalMessages++;
            }
            scannedMessages++;
          });
          
          // Lấy ID của tin nhắn cuối cùng để tiếp tục fetch
          const lastMessage = messages.last();
          lastMessageId = lastMessage.id;
          
          // Cập nhật progress mỗi 500 tin nhắn
          if (scannedMessages % 500 === 0) {
            const progressEmbed = new EmbedBuilder()
              .setAuthor({ name: 'PROCESSING...', iconURL: client.user.displayAvatarURL() })
              .setDescription(`🔄 | Đã quét ${scannedMessages.toLocaleString('vi-VN')}/${messageLimit.toLocaleString('vi-VN')} tin nhắn...\n\n⏳ Vui lòng đợi, quá trình này có thể mất vài phút.`)
              .setColor('#ffaa00')
              .setTimestamp();
            
            await processingMsg.edit({ embeds: [progressEmbed] });
          }
        }
        
      } catch (error) {
        console.error('Lỗi khi quét tin nhắn:', error);
        const errorEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi quét tin nhắn! Có thể do thiếu quyền truy cập kênh.')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return processingMsg.edit({ embeds: [errorEmbed] });
      }

      // Kiểm tra nếu không có tin nhắn nào
      if (userMessageCount.size === 0) {
        const noDataEmbed = new EmbedBuilder()
          .setAuthor({ name: 'NO DATA', iconURL: client.user.displayAvatarURL() })
          .setDescription(`📭 | Không tìm thấy tin nhắn nào từ users trong ${targetChannel}`)
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return processingMsg.edit({ embeds: [noDataEmbed] });
      }

      // Sắp xếp users theo số lượng tin nhắn (từ cao đến thấp)
      const sortedUsers = Array.from(userMessageCount.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 20); // Chỉ hiển thị top 20

      // Tạo leaderboard
      let leaderboard = '';
      const medals = ['🥇', '🥈', '🥉'];
      
      for (let i = 0; i < sortedUsers.length; i++) {
        const [userId, messageCount] = sortedUsers[i];
        const user = await client.users.fetch(userId).catch(() => null);
        const username = user ? user.username : `User ${userId}`;
        const medal = i < 3 ? medals[i] : `\`${(i + 1).toString().padStart(2, ' ')}.\``;
        
        leaderboard += `${medal} **${username}** - ${messageCount.toLocaleString('vi-VN')} tin nhắn\n`;
      }

      // Tạo embed kết quả
      const resultEmbed = new EmbedBuilder()
        .setColor('#00ff00')
        .setAuthor({
          name: 'MESSAGE COUNT LEADERBOARD',
          iconURL: client.user.displayAvatarURL()
        })
        .setTitle(`📊 Top tin nhắn trong ${targetChannel.name}`)
        .setDescription(leaderboard)
        .addFields(
          { name: '📈 Thống kê', value: `**Tổng tin nhắn:** ${totalMessages.toLocaleString('vi-VN')}\n**Tổng users:** ${userMessageCount.size.toLocaleString('vi-VN')}\n**Tin nhắn quét:** ${scannedMessages.toLocaleString('vi-VN')}`, inline: true },
          { name: '🎯 Kênh', value: `${targetChannel}`, inline: true }
        )
        .setTimestamp()
        .setFooter({
          text: `Thống kê bởi ${message.author.username}`,
          iconURL: message.author.displayAvatarURL()
        });

      await processingMsg.edit({ embeds: [resultEmbed] });

    } catch (error) {
      console.error('Lỗi trong lệnh count:', error);
      const errorEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
        .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi thực hiện lệnh!')
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      return message.reply({ embeds: [errorEmbed] });
    }
  }
};
