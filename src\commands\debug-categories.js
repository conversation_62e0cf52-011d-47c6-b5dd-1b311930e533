const { EmbedBuilder, PermissionFlagsBits, ChannelType } = require('discord.js');

module.exports = {
  name: 'debug-categories',
  aliases: ['debugcat', 'listcat'],
  description: 'Debug - Xem tất cả categories trong server',
  usage: 'debug-categories',
  
  async execute(client, message, args) {
    // Kiểm tra quyền admin
    if (!message.member.permissions.has(PermissionFlagsBits.Administrator)) {
      return message.reply('❌ | Bạn không có quyền sử dụng lệnh này!');
    }

    const guild = message.guild;
    
    // Lấy tất cả categories
    const allCategories = guild.channels.cache.filter(c => c.type === ChannelType.GuildCategory);
    
    console.log('📋 Debug - Tất cả categories:');
    
    let categoryList = '';
    allCategories.forEach(cat => {
      const info = `**${cat.name}** (ID: \`${cat.id}\`)`;
      categoryList += info + '\n';
      console.log(`  - ${cat.name} (ID: ${cat.id})`);
    });

    if (categoryList === '') {
      categoryList = 'Không tìm thấy category nào!';
    }

    const embed = new EmbedBuilder()
      .setColor('#0099ff')
      .setTitle('🔍 Debug - Danh sách Categories')
      .setDescription(categoryList)
      .setFooter({ text: `Tổng cộng: ${allCategories.size} categories` })
      .setTimestamp();

    await message.reply({ embeds: [embed] });
  }
};
