const fs = require('fs');
const path = require('path');
const { PermissionFlagsBits, EmbedBuilder } = require('discord.js');
const keywordsPath = path.join(__dirname, '../data/keywords.json');
const profileDB = require('../data/profileDB');

module.exports = {
  name: 'removeallprf',
  async execute(client, message, args) {
    try {
      // Kiểm tra quyền: Thay vì kiểm tra role cụ thể, kiểm tra quyền Administrator
      if (!message.member.permissions.has(PermissionFlagsBits.Administrator)) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      // Cú pháp
      const [type] = args;
      if (!type || !/^murph|joseph|owner|staff|support$/i.test(type)) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('Cú pháp: tremoveallprf <profile>')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      // Thông báo cho người dùng biết quá trình đang bắt đầu
      const statusEmbed = new EmbedBuilder()
        .setAuthor({ name: 'PROCESSING', iconURL: client.user.displayAvatarURL() })
        .setDescription(`Đang bắt đầu xóa toàn bộ profile của ${type.toUpperCase()}. Quá trình này có thể mất một chút thời gian...`)
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      const statusMessage = await message.reply({ embeds: [statusEmbed] });

      // Xóa toàn bộ profile trong DB
      try {
        await profileDB.deleteAllProfilesByType(type.toUpperCase());
        console.log(`Đã xóa profile ${type.toUpperCase()} trong database`);
        
        const updateEmbed = new EmbedBuilder()
          .setAuthor({ name: 'PROCESSING', iconURL: client.user.displayAvatarURL() })
          .setDescription(`Đã xóa profile ${type.toUpperCase()} trong database. Đang xóa auto respond...`)
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        await statusMessage.edit({ embeds: [updateEmbed] });
      } catch (dbError) {
        console.error('Lỗi khi xóa profile trong database:', dbError);
        const errorEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription(`<:error:1383005371542798346> | Lỗi khi xóa profile trong database: ${dbError.message}`)
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return statusMessage.edit({ embeds: [errorEmbed] });
      }

      // Xóa toàn bộ auto respond liên quan
      try {
        let keywords = [];
        if (fs.existsSync(keywordsPath)) {
          keywords = JSON.parse(fs.readFileSync(keywordsPath, 'utf8'));
        }
        const shortType = /^murph/i.test(type) ? 'mur' : /^joseph/i.test(type) ? 'jos' : /^owner/i.test(type) ? 'own' : /^staff/i.test(type) ? 'stf' : /^support/i.test(type) ? 'sup' : type.toLowerCase();
        const originalLength = keywords.length;
        const newKeywords = keywords.filter(item =>
          !(item.type === type.toUpperCase() && item.keyword.startsWith(shortType))
        );
        fs.writeFileSync(keywordsPath, JSON.stringify(newKeywords, null, 2));
        console.log(`Đã xóa ${originalLength - newKeywords.length} auto respond liên quan đến ${type.toUpperCase()}`);
        
        const updateEmbed2 = new EmbedBuilder()
          .setAuthor({ name: 'PROCESSING', iconURL: client.user.displayAvatarURL() })
          .setDescription(`Đã xóa profile và auto respond của ${type.toUpperCase()}. Đang xóa tin nhắn profile...`)
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        await statusMessage.edit({ embeds: [updateEmbed2] });
      } catch (keywordError) {
        console.error('Lỗi khi xóa auto respond:', keywordError);
        const errorEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription(`<:error:1383005371542798346> | Lỗi khi xóa auto respond: ${keywordError.message}`)
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return statusMessage.edit({ embeds: [errorEmbed] });
      }

      // Xóa tin nhắn profile trong kênh
      let channelId;
      if (/^murph/i.test(type)) {
        channelId = '1341447431107117128';
      } else if (/^joseph/i.test(type)) {
        channelId = '1341447441345548373';
      } else if (/^owner/i.test(type)) {
        channelId = '1385627757643304981';
      } else if (/^staff/i.test(type)) {
        channelId = '1385627757643304981';
      } else if (/^support/i.test(type)) {
        channelId = '1385627757643304981';
      }
      
      if (channelId) {
        try {
          const channel = await client.channels.fetch(channelId);
          if (!channel) {
            console.error(`Không tìm thấy kênh với ID: ${channelId}`);
            const errorEmbed = new EmbedBuilder()
              .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
              .setDescription(`<:error:1383005371542798346> | Đã xóa profile và auto respond, nhưng không tìm thấy kênh với ID: ${channelId}`)
              .setColor(client.embedColor || '#0099ff')
              .setTimestamp();
            return statusMessage.edit({ embeds: [errorEmbed] });
          }
          
          const updateEmbed3 = new EmbedBuilder()
            .setAuthor({ name: 'PROCESSING', iconURL: client.user.displayAvatarURL() })
            .setDescription(`Đang lấy tin nhắn từ kênh ${type.toUpperCase()}...`)
            .setColor(client.embedColor || '#0099ff')
            .setTimestamp();
          await statusMessage.edit({ embeds: [updateEmbed3] });
          
          const messages = await channel.messages.fetch({ limit: 100 });
          console.log(`Đã lấy ${messages.size} tin nhắn từ kênh ${channelId}`);
          
          // Lấy danh sách tin nhắn profile của loại này cần xóa
          const messagesToDelete = messages.filter(msg => 
            msg.author.id === client.user.id && 
            msg.embeds[0] && 
            msg.embeds[0].title?.toLowerCase().startsWith(`✟ ${type.toUpperCase()}`.toLowerCase())
          );
          
          console.log(`Tìm thấy ${messagesToDelete.size} tin nhắn profile cần xóa`);
          const updateEmbed4 = new EmbedBuilder()
            .setAuthor({ name: 'PROCESSING', iconURL: client.user.displayAvatarURL() })
            .setDescription(`Đang xóa ${messagesToDelete.size} tin nhắn profile của ${type.toUpperCase()}...`)
            .setColor(client.embedColor || '#0099ff')
            .setTimestamp();
          await statusMessage.edit({ embeds: [updateEmbed4] });
          
          // Xóa các tin nhắn với độ trễ để tránh rate limit
          let deletedCount = 0;
          const messageArray = Array.from(messagesToDelete.values());
          
          for (let i = 0; i < messageArray.length; i++) {
            try {
              const msg = messageArray[i];
              await msg.delete();
              deletedCount++;
              
              // Cập nhật tiến trình sau mỗi 3 tin nhắn bị xóa hoặc khi hoàn thành
              if (deletedCount % 3 === 0 || deletedCount === messageArray.length) {
                const progressEmbed = new EmbedBuilder()
                  .setAuthor({ name: 'PROCESSING', iconURL: client.user.displayAvatarURL() })
                  .setDescription(`Đang xóa tin nhắn profile: ${deletedCount}/${messageArray.length}`)
                  .setColor(client.embedColor || '#0099ff')
                  .setTimestamp();
                await statusMessage.edit({ embeds: [progressEmbed] });
              }
              
              // Thêm độ trễ dài hơn giữa các lần xóa để tránh rate limit
              await new Promise(resolve => setTimeout(resolve, 2000));
            } catch (deleteError) {
              console.error(`Lỗi khi xóa tin nhắn:`, deleteError);
              // Tăng thời gian chờ nếu gặp lỗi rate limit
              if (deleteError.code === 429) { // Rate limit error
                const waitTime = (deleteError.retry_after || 5) * 1000;
                console.log(`Rate limit hit, đợi ${waitTime/1000} giây...`);
                await new Promise(resolve => setTimeout(resolve, waitTime));
              }
              // Tiếp tục với tin nhắn tiếp theo thay vì dừng lại
            }
          }
          
          console.log(`Đã xóa ${deletedCount} tin nhắn profile`);
          
          // Thông báo kết quả cuối cùng
          const successEmbed = new EmbedBuilder()
            .setAuthor({ name: 'ACTION SUCCESSFUL', iconURL: client.user.displayAvatarURL() })
            .setDescription(`<:done:1383009630581424250> | Hoàn tất: Đã xóa thành công ${deletedCount}/${messageArray.length} tin nhắn profile của ${type.toUpperCase()}.`)
            .setColor(client.embedColor || '#0099ff')
            .setTimestamp();
          return statusMessage.edit({ embeds: [successEmbed] });
        } catch (channelError) {
          console.error('Lỗi khi xử lý kênh Discord:', channelError);
          const errorEmbed = new EmbedBuilder()
            .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
            .setDescription(`<:error:1383005371542798346> | Đã xóa profile và auto respond, nhưng gặp lỗi khi xử lý kênh Discord: ${channelError.message}`)
            .setColor(client.embedColor || '#0099ff')
            .setTimestamp();
          return statusMessage.edit({ embeds: [errorEmbed] });
        }
      }

      // Nếu không có kênh, thông báo hoàn tất
      const finalSuccessEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION SUCCESSFUL', iconURL: client.user.displayAvatarURL() })
        .setDescription(`<:done:1383009630581424250> | Hoàn tất: Đã xóa toàn bộ profile và auto respond của ${type.toUpperCase()}.`)
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      return statusMessage.edit({ embeds: [finalSuccessEmbed] });
    } catch (error) {
      console.error('Lỗi không xác định khi xóa profile:', error);
      const errorEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
        .setDescription(`<:error:1383005371542798346> | Đã xảy ra lỗi không xác định: ${error.message}`)
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      return message.reply({ embeds: [errorEmbed] });
    }
  }
};