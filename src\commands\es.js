
const { PermissionFlagsBits, EmbedBuilder } = require('discord.js');

module.exports = {
  name: 'es',
  async execute(client, message, args) {

    // <PERSON><PERSON><PERSON> tra quyền: Administrator hoặc role cụ thể
    const hasAdminPermission = message.member.permissions.has(PermissionFlagsBits.Administrator);
    const hasSpecialRole = message.member.roles.cache.has('1376884726232514620') ||
                          message.member.roles.cache.has('1376500798896214108');

    if (!hasAdminPermission && !hasSpecialRole) {
      const failEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
        .setDescription('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!')
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      return message.reply({ embeds: [failEmbed] });
    }

    const subCommand = args.shift();
    if (!subCommand) return message.reply('<PERSON><PERSON><PERSON> cần nhập lệnh muốn gọi!');

    // Tìm lệnh con trong collection lệnh
    const command = client.commands.get(`es_${subCommand}`);
    if (!command) return message.reply('Lệnh không tồn tại!');

    // Gọi lệnh con, truyền lại args còn lại
    return command.execute(client, message, args);
  }
};