const fs = require('fs');
const path = require('path');
const { PermissionFlagsBits } = require('discord.js');
const keywordsPath = path.join(__dirname, '../data/keywords.json');
const profileDB = require('../data/profileDB');

// Hàm normalize để so sánh title
function normalizeFont(text) {
  return text
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/[^a-zA-Z0-9\s]/g, '')
    .toLowerCase()
    .trim();
}

module.exports = {
  name: 'es_removeprf',
  async execute(client, message, args) {
    // Ki<PERSON><PERSON> tra quyền: Administrator hoặc role cụ thể
    const hasAdminPermission = message.member.permissions.has(PermissionFlagsBits.Administrator);
    const hasSpecialRole = message.member.roles.cache.has('1376500798896214108') ||
                          message.member.roles.cache.has('1376884726232514620');

    if (!hasAdminPermission && !hasSpecialRole) {
      return message.reply('❌ | Bạn không có quyền sử dụng lệnh này!');
    }

    // Cú pháp
    const [type, number] = args;
    if (!type || !number) {
      return message.reply('Cú pháp: hes removeprf <Oiran/Kabuki/Owner/Admin/Support> <số>');
    }

    const playerType = type.toUpperCase();
    const playerNumber = Number(number);

    // Xóa profile trong DB
    await profileDB.deleteProfile(playerType, playerNumber);

    // Xóa tin nhắn profile trong kênh
    let channelId, titleEmoji;
    if (/^oiran/i.test(type)) {
      channelId = '1379119153977364642';
      titleEmoji = '<a:1794_sparkles:1266324538979913828>';
    } else if (/^kabuki/i.test(type)) {
      channelId = '1379119508303642655';
      titleEmoji = '<a:5306bluesparkles:1266323953215868968>';
    } else if (/^owner/i.test(type)) {
      channelId = '1379123277666189342';
      titleEmoji = '<a:3143woosh:1268552755178373152>'; // Thay thế bằng emoji ID thực tế cho Owner
    } else if (/^admin/i.test(type)) {
      channelId = '1379123277666189342';
      titleEmoji = '<a:3143woosh:1268552755178373152>';
    } else if (/^support/i.test(type)) {
      channelId = '1379123277666189342';
      titleEmoji = '<a:3143woosh:1268552755178373152>';
    }
    
    if (channelId) {
      try {
        const channel = await client.channels.fetch(channelId);
        const messages = await channel.messages.fetch({ limit: 100 });
        
        // Tạo pattern tìm kiếm linh hoạt
        const searchPattern = normalizeFont(`${playerType} ${playerNumber}`);
        
        const toDelete = messages.filter(msg =>
          msg.author.id === client.user.id &&
          msg.embeds[0] &&
          msg.embeds[0].title &&
          normalizeFont(msg.embeds[0].title).includes(searchPattern)
        );
        
        for (const msg of toDelete.values()) {
          await msg.delete().catch(console.error);
        }
      } catch (error) {
        console.error('Lỗi khi xóa tin nhắn trong kênh:', error);
      }
    }

    // Xóa auto respond từ khóa liên quan
    try {
      let keywords = [];
      if (fs.existsSync(keywordsPath)) {
        keywords = JSON.parse(fs.readFileSync(keywordsPath, 'utf8'));
      }
      
      const shortType = /^oiran/i.test(type) ? 'oi' : /^kabuki/i.test(type) ? 'ka' : /^owner/i.test(type) ? 'own' : /^admin/i.test(type) ? 'adm' : /^support/i.test(type) ? 'sup' : type.toLowerCase();
      const profileKey = `${shortType}${number}`;
      
      const newKeywords = keywords.filter(item =>
        !(item.keyword === profileKey && item.type === playerType && item.number === playerNumber)
      );
      
      fs.writeFileSync(keywordsPath, JSON.stringify(newKeywords, null, 2));
    } catch (error) {
      console.error('Lỗi khi xóa keywords:', error);
    }

    message.reply(`Đã xóa profile ${playerType} ${playerNumber} và auto respond liên quan.`);
  }
};