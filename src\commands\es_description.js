const { EmbedBuilder, PermissionsBitField, PermissionFlagsBits } = require('discord.js');
const profileDB = require('../data/profileDB');
const fs = require('fs');
const path = require('path');
const keywordsPath = path.join(__dirname, '../data/keywords.json');

// Hàm kiểm tra và chuẩn hóa URL
function normalizeUrl(url) {
  
  if (!url) return null;
  
  // Loại bỏ các ký tự đặc biệt có thể được thêm vào từ Discord mobile
  url = url.trim().replace(/^<|>$/g, '');
  
  // Nếu URL bắt đầu bằng 📎 hoặc các emoji kh<PERSON>c, loại bỏ chúng
  url = url.replace(/^[\u{1F000}-\u{1FFFF}]+/u, '').trim();
  
  // Kiểm tra nếu URL không bắt đầu bằng http:// hoặc https://, thêm vào
  if (!/^https?:\/\//i.test(url)) {
    url = 'https://' + url;
  }
  
  // Kiểm tra xem URL có hợp lệ không
  try {
    new URL(url);
    return url;
  } catch (e) {
    console.error('URL không hợp lệ:', url, e);
    return null;
  }
}

// Thêm hàm chuẩn hóa Unicode về Latin thường
function normalizeType(str) {
  if (!str) return '';
  // Loại bỏ các ký tự đặc biệt, chuyển về chữ thường không dấu
  return str.normalize('NFKD')
    .replace(/[\u0300-\u036f]/g, '') // bỏ dấu
    .replace(/[^a-zA-Z]/g, '')      // chỉ giữ lại chữ cái Latin
    .toLowerCase();
}

// Thêm hàm chuyển số Unicode về số thường
function normalizeNumber(str) {
  if (!str) return '';
  // Chuyển các số Unicode đặc biệt (bold, italic, v.v) về số thường
  const unicodeNumbers = {
    '𝟬': '0', '𝟭': '1', '𝟮': '2', '𝟯': '3', '𝟰': '4',
    '𝟱': '5', '𝟲': '6', '𝟳': '7', '𝟴': '8', '𝟵': '9',
    '０': '0', '１': '1', '２': '2', '３': '3', '４': '4',
    '５': '5', '６': '6', '７': '7', '８': '8', '９': '9'
  };
  return str.split('').map(ch => unicodeNumbers[ch] || ch).join('');
}

function toClassicFont(str) {
  const classicMap = {
    A: '𝐀', B: '𝐁', C: '𝐂', D: '𝐃', E: '𝐄', F: '𝐅', G: '𝐆', H: '𝐇', I: '𝐈', J: '𝐉', K: '𝐊', L: '𝐋', M: '𝐌',
    N: '𝐍', O: '𝐎', P: '𝐏', Q: '𝐐', R: '𝐑', S: '𝐒', T: '𝐓', U: '𝐔', V: 'V', W: '𝐖', X: '𝐗', Y: '𝐘', Z: '𝐙',
    a: 'a', b: '𝐛', c: '𝐜', d: '𝐝', e: '𝐞', f: '𝐟', g: '𝐠', h: '𝐡', i: '𝐢', j: '𝐣', k: '𝐤', l: '𝐥', m: '𝐦',
    n: '𝐧', o: '𝐨', p: '𝐩', q: '𝐪', r: '𝐫', s: '𝐬', t: '𝐭', u: '𝐮', v: '𝐯', w: '𝐰', x: '𝐱', y: '𝐲', z: '𝐳',
    '0': '𝟎', '1': '𝟏', '2': '𝟐', '3': '𝟑', '4': '𝟒', '5': '𝟓', '6': '𝟔', '7': '𝟕', '8': '𝟖', '9': '𝟗'
  };
  return str.split('').map(ch => classicMap[ch] || ch).join('');
}

module.exports = {
  name: 'es_description',
  execute: async (client, message, args) => {
    // Kiểm tra quyền: Administrator hoặc role cụ thể
    const hasAdminPermission = message.member.permissions.has(PermissionFlagsBits.Administrator);
    const hasSpecialRole = message.member.roles.cache.has('1376500798896214108') ||
                          message.member.roles.cache.has('1376884726232514620');

    if (!hasAdminPermission && !hasSpecialRole) {
      return message.reply('❌ | Bạn không có quyền sử dụng lệnh này!');
    }

    if (!Array.isArray(args)) return message.reply('Lỗi: args không hợp lệ!');
    const desc = args.join(' ');
    const thumbMatch = desc.match(/--thumb=(\S+)/);
    const imageMatch = desc.match(/--img=(\S+)/);
    
    // Chuẩn hóa URL thumbnail và image
    const thumbnail = thumbMatch ? normalizeUrl(thumbMatch[1].trim()) : null;
    const image = imageMatch ? normalizeUrl(imageMatch[1].trim()) : null;
    
    const cleanDesc = desc.replace(/--thumb=\S+/, '').replace(/--img=\S+/, '').trim();

    const [header, ...bodyLines] = cleanDesc.split('|').map(s => s.trim());

    // Tách phần chữ và số (kể cả Unicode) bằng regex, lấy số ở cuối chuỗi
    const headerMatch = header.match(/([^\d𝟬-𝟵０-９]*)([\d𝟬-𝟵０-９]+)/u);
    let playerTypeRaw = '', playerNumberRaw = '';
    if (headerMatch) {
      playerTypeRaw = headerMatch[1].trim();
      playerNumberRaw = headerMatch[2] ? headerMatch[2].trim() : '';
    } else {
      playerTypeRaw = header.trim();
      playerNumberRaw = '';
    }
    const playerType = normalizeType(playerTypeRaw);
    const playerNumber = playerNumberRaw ? parseInt(normalizeNumber(playerNumberRaw), 10) : null;
    const body = bodyLines.join('\n');

    let channelId, color, emoji;
    if (playerType === 'murph') {
      channelId = '1341447431107117128';
      color = ("#BFAEE3");
      emoji = '<:mu:1385677279065280603>'; //thay emoji
    } else if (playerType === 'joseph') {
      channelId = '1341447441345548373';
      color = ("#0C5776");
      emoji = '<:jo:1385684426976923659>';
    } else if (playerType === 'owner') {
      channelId = '1385627757643304981';
      color = ("#c02626"); 
      emoji = '<a:white:1376852619632316517>';
    } else if (playerType === 'staff') {
      channelId = '1385627757643304981';
      color = ("#c02626"); 
      emoji = '<a:white:1376852619632316517>';
    } else if (playerType === 'support') {
      channelId = '1385627757643304981';
      color = ("#c02626"); 
      emoji = '<a:white:1376852619632316517>';
    } else {
      return message.reply('Bạn phải bắt đầu bằng Murph, Joseph, Owner, Staff hoặc Support.');
    }

    // Parse các trường từ body
    let name = '', mention = '', location = '', game = '', cam = '', bio = '';
    const lines = body.split('\n').map(l => l.trim()).filter(Boolean);
    for (const line of lines) {
      if (/^name:/i.test(line)) name = line.replace(/^name:/i, '').trim();
      else if (/^mention:/i.test(line)) mention = line.replace(/^mention:/i, '').trim();
      else if (/^ở:/i.test(line)) location = line.replace(/^ở:/i, '').trim();
      else if (/^game:/i.test(line)) game = line.replace(/^game:/i, '').trim();
      else if (/^cam:/i.test(line)) cam = line.replace(/^cam:/i, '').trim();
      else bio += line + '\n';
    }
    bio = bio.trim();

    // Lưu profile vào DB
    await profileDB.addOrUpdateProfile({
      playerType: playerType.toUpperCase(),
      playerNumber,
      name,
      mention,
      location,
      game,
      cam,
      bio,
      thumbnail,
      image
    });

    // Thêm từ khóa autores
    const shortType = /^murph/i.test(playerType) ? 'mur' : 
                     /^joseph/i.test(playerType) ? 'jos' : 
                     /^owner/i.test(playerType) ? 'own' :
                     /^staff/i.test(playerType) ? 'stf' :
                     /^support/i.test(playerType) ? 'sup' :
                     playerType.toLowerCase();
    const profileKey = `${shortType}${playerNumber}`;
    let keywords = [];
    if (fs.existsSync(keywordsPath)) {
      keywords = JSON.parse(fs.readFileSync(keywordsPath, 'utf8'));
    }
    if (!keywords.some(item => item.keyword === profileKey)) {
      keywords.push({ keyword: profileKey, type: playerType.toUpperCase(), number: playerNumber });
      fs.writeFileSync(keywordsPath, JSON.stringify(keywords, null, 2));
    }

    try {
      // Kiểm tra kênh tồn tại
      const channel = await client.channels.fetch(channelId).catch(err => {
        console.error('Không thể tìm thấy kênh:', err);
        throw new Error('Không thể tìm thấy kênh. Vui lòng kiểm tra ID kênh.');
      });

      // Kiểm tra quyền của bot trong kênh
      const permissions = channel.permissionsFor(client.user);
      if (!permissions.has(PermissionsBitField.Flags.ViewChannel) || 
          !permissions.has(PermissionsBitField.Flags.SendMessages) ||
          !permissions.has(PermissionsBitField.Flags.EmbedLinks)) {
        return message.reply('Bot không có đủ quyền trong kênh. Cần quyền xem kênh, gửi tin nhắn và nhúng liên kết.');
      }

      // Tạo và gửi embed cho profile mới
      let embedDesc = '';
      if (name) embedDesc += `** ## ${emoji} ${toClassicFont(name)}**\n`;

      // Thêm mention player (do user tự setup)
      if (mention) {
        embedDesc += `${emoji} ${mention}\n`;
      }

      if (location) embedDesc += `${emoji} ${location}\n`;
      if (bio) embedDesc += `${emoji} ${bio}\n`;
      if (game) embedDesc += `${emoji} Game: ${game}\n`;
      if (cam) embedDesc += `${emoji} Giá cam: ${cam}\n`;

      // Thêm hàm này sau các hàm normalize khác (khoảng dòng 50)
      function capitalizeFirst(str) {
        if (!str) return '';
        return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
      }

      const modernTitle = `${toClassicFont(capitalizeFirst(playerType))} ${toClassicFont(playerNumber !== null ? playerNumber.toString() : '')}`;
      
      // Sử dụng emoji khác nhau 
      let titleEmoji;
      if (playerType === 'murph') {
        titleEmoji = '<:mur:1378445368689164290>'; 
      } else if (playerType === 'joseph') {
        titleEmoji = '<:jos:1378445302381674547>'; 
      } else if (playerType === 'owner') {
        titleEmoji = '<a:ad:1385687143015383100>'; 
      } else if (playerType === 'staff') {
        titleEmoji = '<a:ad:1385687143015383100>';
      } else if (playerType === 'support') {
        titleEmoji = '<a:ad:1385687143015383100>';
      }
      
      const embed = new EmbedBuilder()
        .setColor(color)
        .setTitle(`${titleEmoji} **${modernTitle}** ${titleEmoji}`)
        .setDescription(embedDesc);

      // Kiểm tra URL hợp lệ trước khi thêm vào embed
      if (thumbnail) {
        try {
          embed.setThumbnail(thumbnail);
        } catch (error) {
          console.error('Lỗi khi đặt thumbnail:', error);
        }
      }
      
      if (image) {
        try {
          embed.setImage(image);
        } catch (error) {
          console.error('Lỗi khi đặt image:', error);
        }
      }

      // Gửi profile mới vào kênh
      await channel.send({ embeds: [embed] });
      
      // Tạo embed phản hồi chi tiết
      const confirmEmbed = new EmbedBuilder()
        .setColor(color)
        .setAuthor({ name: 'ACTION SUCCESSFUL', iconURL: client.user.displayAvatarURL() })
        .setDescription(`<:done:1383009630581424250> | Đã tạo profile ${capitalizeFirst(playerType)} ${playerNumber}`)
        .setFooter({ text: 'Profile đã được gửi đến kênh chỉ định' });
      
      // Kiểm tra URL hợp lệ trước khi thêm vào embed phản hồi
      if (thumbnail) {
        try {
          confirmEmbed.setThumbnail(thumbnail);
        } catch (error) {
          console.error('Lỗi khi đặt thumbnail cho embed phản hồi:', error);
        }
      }
      
      if (image) {
        try {
          confirmEmbed.setImage(image);
        } catch (error) {
          console.error('Lỗi khi đặt image cho embed phản hồi:', error);
        }
      }
      
      // Gửi embed phản hồi
      await message.reply({ embeds: [confirmEmbed] });
      
    } catch (error) {
      console.error('Lỗi khi gửi embed:', error);
      await message.reply(`❌ Đã lưu profile vào database nhưng không thể gửi embed: ${error.message}`);
    }
  }
};