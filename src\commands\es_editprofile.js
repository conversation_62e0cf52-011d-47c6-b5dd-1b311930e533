const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const profileDB = require('../data/profileDB');

// Thêm hàm auto-resolve để nhận diện tên viết tắt
function resolveProfileShorthand(input) {
  const normalized = input.toLowerCase().trim();
  
  // Kiểm tra pattern cho Murph
  const murphMatch = normalized.match(/^mur(\d+)$/);
  if (murphMatch) {
    return {
      playerType: 'MURPH',
      playerNumber: parseInt(murphMatch[1], 10)
    };
  }
  
  // Kiểm tra pattern
  const josephMatch = normalized.match(/^j[os](\d+)$/);
  if (josephMatch) {
    return {
      playerType: 'JOSEPH',
      playerNumber: parseInt(josephMatch[1], 10)
    };
  }
  
  // Kiểm tra pattern cho Owner: ow + số (ví dụ: ow1, ow5)
  const ownerMatch = normalized.match(/^own(\d+)$/);
  if (ownerMatch) {
    return {
      playerType: 'OWNER',
      playerNumber: parseInt(ownerMatch[1], 10)
    };
  }

  // Kiểm tra pattern cho Staff
  const staffMatch = normalized.match(/^stf(\d+)$/);
  if (staffMatch) {
    return {
      playerType: 'STAFF',
      playerNumber: parseInt(staffMatch[1], 10)
    };
  }

  // Kiểm tra pattern cho Support: sup + số (ví dụ: sup1, sup5)
  const supportMatch = normalized.match(/^sup(\d+)$/);
  if (supportMatch) {
    return {
      playerType: 'SUPPORT',
      playerNumber: parseInt(supportMatch[1], 10)
    };
  }
  
  // Nếu không match pattern viết tắt, parse theo cách cũ
  const parts = input.split(' ');
  const playerType = parts[0].toUpperCase();
  const playerNumber = parseInt(parts.slice(1).join(' '), 10);
  
  return {
    playerType,
    playerNumber
  };
}

function toClassicFont(str) {
  const classicMap = {
    A: '𝐀', B: '𝐁', C: '𝐂', D: '𝐃', E: '𝐄', F: '𝐅', G: '𝐆', H: '𝐇', I: '𝐈', J: '𝐉', K: '𝐊', L: '𝐋', M: '𝐌',
    N: '𝐍', O: '𝐎', P: '𝐏', Q: '𝐐', R: '𝐑', S: '𝐒', T: '𝐓', U: '𝐔', V: 'V', W: '𝐖', X: '𝐗', Y: '𝐘', Z: '𝐙',
    a: 'a', b: '𝐛', c: '𝐜', d: '𝐝', e: '𝐞', f: '𝐟', g: '𝐠', h: '𝐡', i: '𝐢', j: '𝐣', k: '𝐤', l: '𝐥', m: '𝐦',
    n: '𝐧', o: '𝐨', p: '𝐩', q: '𝐪', r: '𝐫', s: '𝐬', t: '𝐭', u: '𝐮', v: '𝐯', w: '𝐰', x: '𝐱', y: '𝐲', z: '𝐳',
    '0': '𝟎', '1': '𝟏', '2': '𝟐', '3': '𝟑', '4': '𝟒', '5': '𝟓', '6': '𝟔', '7': '𝟕', '8': '𝟖', '9': '𝟗'
  };
  return str.split('').map(ch => classicMap[ch] || ch).join('');
}

function normalizeFont(str) {
  // Loại bỏ ký tự đặc biệt, chỉ giữ lại chữ cái và số
  return str
    .normalize('NFKD')
    .replace(/[\u0300-\u036f]/g, '') // bỏ dấu
    .replace(/[^a-zA-Z0-9]/g, '')   // chỉ giữ lại chữ cái và số
    .toLowerCase();
}

module.exports = {
  name: 'es_editprf',
  async execute(client, message, args) {
    // Kiểm tra quyền: Administrator hoặc role cụ thể
    const hasAdminPermission = message.member.permissions.has(PermissionFlagsBits.Administrator);
    const hasSpecialRole = message.member.roles.cache.has('1376500798896214108') ||
                          message.member.roles.cache.has('1376884726232514620');

    if (!hasAdminPermission && !hasSpecialRole) {
      return message.reply('❌ | Bạn không có quyền sử dụng lệnh này!');
    }

    if (!Array.isArray(args)) return message.reply('Lỗi: args không hợp lệ!');
    const desc = args.join(' ');
    const thumbMatch = desc.match(/--thumb=(\S+)/);
    const imageMatch = desc.match(/--img=(\S+)/);
    const colorMatch = desc.match(/--color=(\S+)/);
    
    const thumbnail = thumbMatch ? thumbMatch[1].trim() : null;
    const image = imageMatch ? imageMatch[1].trim() : null;
    let color = null;
    
    if (colorMatch) {
      try {
        color = parseInt(colorMatch[1].replace('#', ''), 16);
        if (isNaN(color)) color = null;
      } catch (error) {
        color = null;
      }
    }
    
    const cleanDesc = desc
      .replace(/--thumb=\S+/, '')
      .replace(/--img=\S+/, '')
      .replace(/--color=\S+/, '')
      .trim();

    const [header, ...bodyLines] = cleanDesc.split('|').map(s => s.trim());
    
    // Sử dụng hàm auto-resolve thay vì parse thủ công
    const resolved = resolveProfileShorthand(header);
    const playerType = resolved.playerType;
    const playerNumber = resolved.playerNumber;
    
    // Kiểm tra tính hợp lệ
    if (!playerType || isNaN(playerNumber)) {
    return message.reply('❌ | Định dạng không hợp lệ!');
    }
    
    // Kiểm tra loại player hợp lệ
    if (!['MURPH', 'JOSEPH', 'OWNER', 'STAFF', 'SUPPORT'].includes(playerType)) {
      return message.reply('❌ | Loại profile không hợp lệ!');
    }

    // Kiểm tra số player hợp lệ
    const body = bodyLines.join('\n');

    // Kiểm tra profile tồn tại với nhiều định dạng
    let existingProfile = await profileDB.getProfile(playerType, playerNumber);
    
    // Nếu không tìm thấy với MURPH, thử với các định dạng khác
    if (!existingProfile && playerType === 'MURPH') {
      existingProfile = await profileDB.getProfile('Murph', playerNumber) ||
                       await profileDB.getProfile('murph', playerNumber);
    }
    
    // Nếu không tìm thấy với JOSEPH, thử với các định dạng khác  
    if (!existingProfile && playerType === 'JOSEPH') {
      existingProfile = await profileDB.getProfile('Joseph', playerNumber) ||
                       await profileDB.getProfile('joseph', playerNumber);
    }
    
    // Nếu không tìm thấy với OWNER, thử với các định dạng khác
    if (!existingProfile && playerType === 'OWNER') {
      existingProfile = await profileDB.getProfile('Owner', playerNumber) ||
                       await profileDB.getProfile('Owner', playerNumber);
    }

    if (!existingProfile && playerType === 'STAFF') {
      existingProfile = await profileDB.getProfile('Staff', playerNumber) ||
        await profileDB.getProfile('staff', playerNumber);
    }

    // Nếu không tìm thấy với SUPPORT, thử với các định dạng khác
    if (!existingProfile && playerType === 'SUPPORT') {
      existingProfile = await profileDB.getProfile('Support', playerNumber) ||
        await profileDB.getProfile('support', playerNumber);
    }
    
    // Thêm hàm tìm profile qua autorespond
    async function findProfileByAutorespond(input) {
    // Tìm trong tất cả profiles có tên chứa input
    const allMurphProfiles = await profileDB.getAllProfilesByType('Murph');
    const allJosephProfiles = await profileDB.getAllProfilesByType('Joseph');
    const allOwnerProfiles = await profileDB.getAllProfilesByType('Owner');
    const allStaffProfiles = await profileDB.getAllProfilesByType('Staff');
    const allSupportProfiles = await profileDB.getAllProfilesByType('Support');
    
    const allProfiles = [...allMurphProfiles, ...allJosephProfiles, ...allOwnerProfiles, ...allStaffProfiles, ...allSupportProfiles];
    
    // Tìm profile có name hoặc autorespond match với input
    return allProfiles.find(profile => {
    const normalizedInput = input.toLowerCase();
    const normalizedName = (profile.name || '').toLowerCase();
    return normalizedName.includes(normalizedInput) || 
           normalizedInput.includes(normalizedName);
    });
    }
    
    // Trong hàm execute, thêm logic tìm kiếm:
    if (!existingProfile) {
    // Thử tìm qua autorespond/name
    const foundProfile = await findProfileByAutorespond(header);
    if (foundProfile) {
    existingProfile = foundProfile;
    playerType = foundProfile.playerType;
    playerNumber = foundProfile.playerNumber;
    }
    }
    
    if (!existingProfile) {
      return message.reply(`❌ | Không tìm thấy profile ${playerType} ${playerNumber}!`);
    }

    // Parse các trường từ body
    let name = '', mention = '', location = '', game = '', cam = '', bio = '';
    const lines = body.split('\n').map(l => l.trim()).filter(Boolean);
    for (const line of lines) {
      // Loại bỏ tiền tố
      const cleanLine = line.replace(/^-(murph|joseph):\s*/i, '');

      if (/^name:/i.test(cleanLine)) name = cleanLine.replace(/^name:/i, '').trim();
      else if (/^mention:/i.test(cleanLine)) mention = cleanLine.replace(/^mention:/i, '').trim();
      else if (/^ở:/i.test(cleanLine)) location = cleanLine.replace(/^ở:/i, '').trim();
      else if (/^game:/i.test(cleanLine)) game = cleanLine.replace(/^game:/i, '').trim();
      else if (/^cam:/i.test(cleanLine) || /^giá\s*cam:/i.test(cleanLine))
        cam = cleanLine.replace(/^(cam:|giá\s*cam:)/i, '').trim();
      else bio += cleanLine + '\n';
    }
    bio = bio.trim();

    // Sử dụng giá trị hiện tại nếu không có giá trị mới
    name = name || existingProfile.name;
    mention = mention || existingProfile.mention;
    location = location || existingProfile.location;
    game = game || existingProfile.game;
    cam = cam || existingProfile.cam;
    bio = bio || existingProfile.bio;
    color = color || existingProfile.color;

    // Cập nhật profile vào DB
    await profileDB.addOrUpdateProfile({
      playerType: playerType.toUpperCase(),
      playerNumber,
      name,
      mention,
      location,
      game,
      cam,
      bio,
      thumbnail: thumbnail || existingProfile.thumbnail,
      image: image || existingProfile.image,
      color
    });

    // Xác định kênh và emoji
    let channelId, defaultColor, emoji, titleEmoji;
    if (playerType === 'MURPH') {
      channelId = '1341447431107117128';
      defaultColor = '#BFAEE3';
      emoji = '<:mu:1385677279065280603>';
      titleEmoji = '<:mur:1378445368689164290>';
    } else if (playerType === 'JOSEPH') {
      channelId = '1341447441345548373';
      defaultColor = '#0C5776';
      emoji = '<:jo:1385684426976923659>';
      titleEmoji = '<:jos:1378445302381674547>'; 
    } else if (playerType === 'OWNER') {
      channelId = '1379123277666189342';
      defaultColor = '#c02626'; 
      emoji = '<a:white:1376852619632316517>'; 
      titleEmoji = '<a:ad:1385687143015383100>'; 
    } else if (playerType === 'STAFF') {
      channelId = '1379123277666189342';
      defaultColor = '#c02626';
      emoji = '<a:white:1376852619632316517>';
      titleEmoji = '<a:ad:1385687143015383100>';
    } else if (playerType === 'SUPPORT') {
      channelId = '1379123277666189342';
      defaultColor = '#c02626';
      emoji = '<a:white:1376852619632316517>';
      titleEmoji = '<a:ad:1385687143015383100>';
    }

    // Tìm tin nhắn profile gần nhất của player này trong kênh
    const channel = await client.channels.fetch(channelId);
    const messages = await channel.messages.fetch({ limit: 50 });
    
    // Sử dụng titleEmoji thay vì hardcode sparkles
    // Thêm hàm này sau hàm toClassicFont (khoảng dòng 60)
    function capitalizeFirst(str) {
      if (!str) return '';
      return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
    }

    const targetMsg = messages.find(msg => {
      const embedTitle = msg.embeds[0]?.title || '';
      console.log('Embed title:', embedTitle);
      console.log('Normalized:', normalizeFont(embedTitle));
      console.log('Looking for:', normalizeFont(`${playerType}${playerNumber}`));
      
      // Chuẩn hóa cả hai title về dạng thường để so sánh
      return normalizeFont(embedTitle).includes(normalizeFont(`${playerType}${playerNumber}`));
    });
    
    if (!targetMsg) {
      return message.reply('Không tìm thấy profile để cập nhật!');
    }

    // Tạo và gửi embed cho profile đã cập nhật
    let embedDesc = '';
    if (name) embedDesc += `** ## ${emoji} ${toClassicFont(name)}**\n`;

    // Thêm mention player
    if (mention) {
      embedDesc += `${emoji} ${mention}\n`;
    }

    if (location) embedDesc += `${emoji} ${location}\n`;
    if (bio) embedDesc += `${emoji} ${bio}\n`;
    if (game) embedDesc += `${emoji} Game: ${game}\n`;
    if (cam) embedDesc += `${emoji} Giá cam: ${cam}\n`;

    // Đổi tên biến để tránh conflict
    // Thay đổi dòng 264
    const searchTitle = `${titleEmoji} **${toClassicFont(capitalizeFirst(playerType))} ${toClassicFont(playerNumber.toString())}** ${titleEmoji}`;
    const displayTitle = `${toClassicFont(capitalizeFirst(playerType))} ${toClassicFont(playerNumber.toString())}`;
    const embed = new EmbedBuilder()
      .setColor(color || defaultColor)
      .setTitle(`${titleEmoji} **${displayTitle}** ${titleEmoji}`)
      .setDescription(embedDesc);

    if (thumbnail) embed.setThumbnail(thumbnail);
    else if (existingProfile.thumbnail) embed.setThumbnail(existingProfile.thumbnail);
    
    if (image) embed.setImage(image);
    else if (existingProfile.image) embed.setImage(existingProfile.image);

    // Cập nhật embed
    await targetMsg.edit({ embeds: [embed] });
    
    // Tạo embed phản hồi chi tiết
    const confirmEmbed = new EmbedBuilder()
      .setColor(color || defaultColor)
      .setAuthor({ name: 'ACTION SUCCESSFUL', iconURL: client.user.displayAvatarURL() })
      .setDescription(`<:done:1383009630581424250> | Đã cập nhật profile ${capitalizeFirst(playerType)} ${playerNumber}`)
      .setFooter({ text: 'Profile đã được cập nhật trong kênh chỉ định' });
    
    if (thumbnail) confirmEmbed.setThumbnail(thumbnail);
    else if (existingProfile.thumbnail) confirmEmbed.setThumbnail(existingProfile.thumbnail);
    
    if (image) confirmEmbed.setImage(image);
    else if (existingProfile.image) confirmEmbed.setImage(existingProfile.image);
    
    // Gửi embed phản hồi
    await message.reply({ embeds: [confirmEmbed] });
  }
};
