const { EmbedBuilder } = require('discord.js');

module.exports = {
  name: 'es_help',
  async execute(client, message, args) {
    
    const embed = new EmbedBuilder()
      .setTitle('Hướng dẫn lệnh Profile')
      .setColor(client.embedColor || '#0099ff')
      .setDescription('<PERSON>h sách các lệnh liên quan đến hệ thống Profile')
      .addFields(
        {
          name: '**Quản lý Profile**',
          value: [
            '`es description ( dùng res addprf )` - Tạo profile',
            '`es editprf <tên_profile> | <thông_tin>` - Chỉnh sửa thông tin profile',
            '`es removeprf <tên_profile>` - Xóa profile',
            '`es setimage <tên_profile> --thumb= --img=` - Đặt ảnh cho profile'
          ].join('\n'),
          inline: false
        },
        {
          name: '**Cài đặt & Mapping**',
          value: [
            '`es set <tên_profile> | <user_id>` - <PERSON><PERSON> profile cho user',
            '`es set del <user_id>` - Xóa mapping profile của user'
          ].join('\n'),
          inline: false
        },
        {
          name: '**<PERSON><PERSON><PERSON> thị**',
          value: [
            '`es repostprf <profile_type` - Hiển thị lại tất cả profiles'
          ].join('\n'),
          inline: false
        },
        {
          name: '**Lưu ý**',
          value: [
            '• Tên profile hỗ trợ viết tắt: `mur1`, `jos2`, `own3`',
            '• Mã màu có thể là hex (#ff0000)',
            '• URL ảnh phải là link hợp lệ',
            '• Mention có thể thêm để hiển thị dưới tên profile'
          ].join('\n'),
          inline: false
        }
      )
      .setFooter({ 
        text: 'Profile System', 
        iconURL: client.user.displayAvatarURL() 
      })
      .setTimestamp();

    await message.channel.send({ embeds: [embed] });
  }
};
