const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const profileDB = require('../data/profileDB');

// Thêm hàm toClassicFont
function toClassicFont(str) {
  const classicMap = {
    A: '𝐀', B: '𝐁', C: '𝐂', D: '𝐃', E: '𝐄', F: '𝐅', G: '𝐆', H: '𝐇', I: '𝐈', J: '𝐉', K: '𝐊', L: '𝐋', M: '𝐌',
    N: '𝐍', O: '𝐎', P: '𝐏', Q: '𝐐', R: '𝐑', S: '𝐒', T: '𝐓', U: '𝐔', V: 'V', W: '𝐖', X: '𝐗', Y: '𝐘', Z: '𝐙',
    a: 'a', b: '𝐛', c: '𝐜', d: '𝐝', e: '𝐞', f: '𝐟', g: '𝐠', h: '𝐡', i: '𝐢', j: '𝐣', k: '𝐤', l: '𝐥', m: '𝐦',
    n: '𝐧', o: '𝐨', p: '𝐩', q: '𝐪', r: '𝐫', s: '𝐬', t: '𝐭', u: '𝐮', v: '𝐯', w: '𝐰', x: '𝐱', y: '𝐲', z: '𝐳',
    '0': '𝟎', '1': '𝟏', '2': '𝟐', '3': '𝟑', '4': '𝟒', '5': '𝟓', '6': '𝟔', '7': '𝟕', '8': '𝟖', '9': '𝟗'
  };
  return str.split('').map(ch => classicMap[ch] || ch).join('');
}

// Thêm hàm capitalizeFirst để chỉ viết hoa chữ cái đầu
function capitalizeFirst(str) {
  if (!str) return str;
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

module.exports = {
  name: 'es_repostprf',
  async execute(client, message, args) {
    // Kiểm tra quyền: Administrator hoặc role cụ thể
    const hasAdminPermission = message.member.permissions.has(PermissionFlagsBits.Administrator);
    const hasSpecialRole = message.member.roles.cache.has('1376500798896214108') ||
                          message.member.roles.cache.has('1376884726232514620');

    if (!hasAdminPermission && !hasSpecialRole) {
      return message.reply('❌ | Bạn không có quyền sử dụng lệnh này!');
    }

    // Kiểm tra tham số
    if (!args[0]) {
      return message.reply('Cú pháp: tes repostprf <Murph/Joseph/Owner/Staff/Support>');
    }
    
    const playerType = capitalizeFirst(args[0]); // Thay đổi từ args[0].toUpperCase()
    const playerTypeUpper = playerType.toUpperCase(); // Tạo biến riêng cho việc so sánh
    if (playerTypeUpper !== 'MURPH' && playerTypeUpper !== 'JOSEPH' && playerTypeUpper !== 'OWNER' && playerTypeUpper !== 'STAFF' && playerTypeUpper !== 'SUPPORT') {
      return message.reply('❌ | Loại profile không hợp lệ!');
    }

    // ID kênh đích để gửi lại profile dựa trên loại player
    let targetChannelId;
    if (playerTypeUpper === 'MURPH') {
      targetChannelId = '1341447431107117128';
    } else if (playerTypeUpper === 'JOSEPH') {
      targetChannelId = '1341447441345548373';
    } else if (playerTypeUpper === 'OWNER') {
      targetChannelId = '1385627757643304981';
    } else if (playerTypeUpper === 'STAFF') {
      targetChannelId = '1385627757643304981';
    } else { // SUPPORT
      targetChannelId = '1385627757643304981';
    }
    
    try {
      // Lấy kênh đích
      const targetChannel = await client.channels.fetch(targetChannelId);
      if (!targetChannel) {
        return message.reply(`Không tìm thấy kênh với ID ${targetChannelId}`);
      }

      // Lấy tất cả profile theo loại
      const profiles = await profileDB.getAllProfilesByType(playerTypeUpper); // Sử dụng playerTypeUpper cho DB
      if (!profiles || profiles.length === 0) {
        return message.reply(`Không tìm thấy profile nào cho ${playerType}`);
      }

      // Sắp xếp profile theo số thứ tự
      profiles.sort((a, b) => a.playerNumber - b.playerNumber);

      // Xác định màu và emoji dựa trên loại player
      let defaultColor, emoji, titleEmoji;
      if (playerTypeUpper === 'MURPH') {
        defaultColor = '#BFAEE3';
        emoji = '<:mu:1385677279065280603>';
        titleEmoji = '<:mur:1378445368689164290>';
      } else if (playerTypeUpper === 'JOSEPH') {
        defaultColor = '#0C5776';
        emoji = '<:jo:1385684426976923659>';
        titleEmoji = '<:jos:1378445302381674547>';
      } else if (playerTypeUpper === 'OWNER') {
        defaultColor = '#c02626';
        emoji = '<a:white:1376852619632316517>';
        titleEmoji = '<a:ad:1385687143015383100>';
      } else if (playerTypeUpper === 'STAFF') {
        defaultColor = '#c02626';
        emoji = '<a:white:1376852619632316517>';
        titleEmoji = '<a:ad:1385687143015383100>';
      } else { // SUPPORT
        defaultColor = '#c02626';
        emoji = '<a:white:1376852619632316517>';
        titleEmoji = '<a:ad:1385687143015383100>';
      }

      // Thông báo bắt đầu quá trình
      await message.reply(`Bắt đầu gửi lại ${profiles.length} profile ${playerType} vào kênh tương ứng...`);

      // Gửi từng profile vào kênh mới
      let successCount = 0;
      for (const profile of profiles) {
        try {
          // Tạo embed cho profile
          let embedDesc = '';
          if (profile.name) embedDesc += `** ## ${emoji} ${toClassicFont(profile.name)}**\n`;

          // Thêm mention player
          if (profile.mention) {
            embedDesc += `${emoji} ${profile.mention}\n`;
          }

          if (profile.location) embedDesc += `${emoji} ${profile.location}\n`;
          if (profile.bio) embedDesc += `${emoji} ${profile.bio}\n`;
          if (profile.game) embedDesc += `${emoji} Game: ${profile.game}\n`;
          if (profile.cam) embedDesc += `${emoji} Giá cam: ${profile.cam}\n`;

          const embed = new EmbedBuilder()
            .setColor(profile.color || defaultColor)
            .setTitle(`${titleEmoji} **${toClassicFont(playerType)} ${toClassicFont(profile.playerNumber.toString())}** ${titleEmoji}`) // Sử dụng playerType thay vì playerTypeUpper
            .setDescription(embedDesc);

          if (profile.thumbnail) embed.setThumbnail(profile.thumbnail);
          if (profile.image) embed.setImage(profile.image);

          // Gửi embed vào kênh mới
          await targetChannel.send({ embeds: [embed] });
          successCount++;

          // Tạm dừng một chút giữa các lần gửi để tránh rate limit
          await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (error) {
          console.error(`Lỗi khi gửi profile ${playerType} ${profile.playerNumber}:`, error);
        }
      }

      // Thông báo hoàn thành
      await message.reply(`Đã gửi lại thành công ${successCount}/${profiles.length} profile ${playerType} vào kênh tương ứng.`);
    } catch (error) {
      console.error('Lỗi khi gửi lại profile:', error);
      await message.reply(`Đã xảy ra lỗi khi gửi lại profile: ${error.message}`);
    }
  }
};