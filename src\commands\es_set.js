const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const profileDB = require('../data/profileDB');
const fs = require('fs');
const path = require('path');

// Đường dẫn đến file mapping user-profile
const userProfileMappingPath = path.join(__dirname, '../data/userProfileMapping.json');

/**
 * Đọc file mapping user-profile
 * @returns {Array} Danh sách mapping
 */
function loadUserProfileMapping() {
  try {
    if (fs.existsSync(userProfileMappingPath)) {
      const data = fs.readFileSync(userProfileMappingPath, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error('Lỗi khi đọc file user-profile mapping:', error);
  }
  return [];
}

/**
 * Lưu file mapping user-profile
 * @param {Array} mappings - Danh sách mapping
 */
function saveUserProfileMapping(mappings) {
  try {
    const dataDir = path.dirname(userProfileMappingPath);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    fs.writeFileSync(userProfileMappingPath, JSON.stringify(mappings, null, 2), 'utf8');
  } catch (error) {
    console.error('Lỗi khi lưu file user-profile mapping:', error);
  }
}

/**
 * Thêm hoặc cập nhật mapping user-profile
 * @param {string} userId - ID của user
 * @param {string} playerType - Loại player (MURPH, JOSEPH, etc.)
 * @param {number} playerNumber - Số player
 */
function setUserProfileMapping(userId, playerType, playerNumber) {
  const mappings = loadUserProfileMapping();
  
  // Xóa mapping cũ của user này (nếu có)
  const filteredMappings = mappings.filter(m => m.userId !== userId);
  
  // Thêm mapping mới
  filteredMappings.push({
    userId,
    playerType: playerType.toUpperCase(),
    playerNumber,
    createdAt: Date.now()
  });
  
  saveUserProfileMapping(filteredMappings);
}

/**
 * Lấy profile mapping của user
 * @param {string} userId - ID của user
 * @returns {Object|null} Mapping hoặc null nếu không tìm thấy
 */
function getUserProfileMapping(userId) {
  const mappings = loadUserProfileMapping();
  return mappings.find(m => m.userId === userId) || null;
}

/**
 * Xóa mapping của user
 * @param {string} userId - ID của user
 */
function removeUserProfileMapping(userId) {
  const mappings = loadUserProfileMapping();
  const filteredMappings = mappings.filter(m => m.userId !== userId);
  saveUserProfileMapping(filteredMappings);
}

// Hàm normalize để so sánh player type
function normalizePlayerType(input) {
  const normalized = input.toLowerCase().trim();
  
  if (/^mur(ph)?$/i.test(normalized)) return 'MURPH';
  if (/^jos(eph)?$/i.test(normalized)) return 'JOSEPH';
  if (/^own(er)?$/i.test(normalized)) return 'OWNER';
  if (/^stf|staff$/i.test(normalized)) return 'STAFF';
  if (/^sup(port)?$/i.test(normalized)) return 'SUPPORT';
  
  return normalized.toUpperCase();
}

module.exports = {
  name: 'es_set',
  aliases: ['eset'],
  description: 'Gán profile cho user',
  usage: 'es set <player_type> <player_number> | <@user>\nes set list - Xem danh sách mapping\nes set remove/del <@user> - Xóa mapping\nes set clear - Xóa tất cả mapping',

  async execute(client, message, args) {
    // Kiểm tra quyền: Administrator hoặc role cụ thể
    const hasAdminPermission = message.member.permissions.has(PermissionFlagsBits.Administrator);
    const hasSpecialRole = message.member.roles.cache.has('1376500798896214108') ||
                          message.member.roles.cache.has('1376884726232514620');

    if (!hasAdminPermission && !hasSpecialRole) {
      return message.reply('❌ | Bạn không có quyền sử dụng lệnh này!');
    }

    const subCommand = args[0]?.toLowerCase();

    // Lệnh xem danh sách mapping
    if (subCommand === 'list') {
      const mappings = loadUserProfileMapping();

      if (mappings.length === 0) {
        return message.reply('📋 | Chưa có mapping nào được tạo.');
      }

      const embed = new EmbedBuilder()
        .setColor(client.embedColor || '#0099ff')
        .setTitle('📋 Danh Sách Profile Mapping')
        .setTimestamp();

      let description = '';
      for (const mapping of mappings.slice(0, 20)) { // Giới hạn 20 mapping
        try {
          const user = await client.users.fetch(mapping.userId);
          description += `**${mapping.playerType} ${mapping.playerNumber}** → ${user.toString()}\n`;
        } catch (error) {
          description += `**${mapping.playerType} ${mapping.playerNumber}** → User không tìm thấy (${mapping.userId})\n`;
        }
      }

      if (mappings.length > 20) {
        description += `\n*... và ${mappings.length - 20} mapping khác*`;
      }

      embed.setDescription(description);
      embed.setFooter({ text: `Tổng cộng: ${mappings.length} mapping` });

      return message.reply({ embeds: [embed] });
    }

    // Lệnh xóa mapping
    if (subCommand === 'remove' || subCommand === 'del') {
      const userPart = args.slice(1).join(' ');
      const userMatch = userPart.match(/<@!?(\d+)>/);

      if (!userMatch) {
        return message.reply('❌ | Cú pháp không đúng. Sử dụng: `tes set remove <@user>` hoặc `tes set del <@user>`');
      }

      const userId = userMatch[1];
      const mapping = getUserProfileMapping(userId);

      if (!mapping) {
        return message.reply('❌ | User này chưa được gán profile nào.');
      }

      try {
        const user = await client.users.fetch(userId);
        removeUserProfileMapping(userId);

        const embed = new EmbedBuilder()
          .setColor('#ff0000')
          .setTitle('Xóa Mapping Thành Công')
          .setDescription(`Đã xóa mapping **${mapping.playerType} ${mapping.playerNumber}** của ${user.toString()}`)
          .setTimestamp();

        return message.reply({ embeds: [embed] });
      } catch (error) {
        return message.reply('❌ | Không thể tìm thấy user này.');
      }
    }

    // Lệnh xóa tất cả mapping
    if (subCommand === 'clear') {
      const mappings = loadUserProfileMapping();

      if (mappings.length === 0) {
        return message.reply('📋 | Không có mapping nào để xóa.');
      }

      // Xác nhận trước khi xóa
      const confirmEmbed = new EmbedBuilder()
        .setColor('#ff9900')
        .setTitle('⚠️ Xác Nhận Xóa Tất Cả Mapping')
        .setDescription(
          `Bạn có chắc chắn muốn xóa **${mappings.length} mapping**?\n\n` +
          `⚠️ **Cảnh báo**: Hành động này không thể hoàn tác!\n\n` +
          `React ✅ để xác nhận hoặc ❌ để hủy bỏ.`
        )
        .setTimestamp();

      const confirmMessage = await message.reply({ embeds: [confirmEmbed] });
      await confirmMessage.react('✅');
      await confirmMessage.react('❌');

      // Chờ reaction từ user
      const filter = (reaction, user) => {
        return ['✅', '❌'].includes(reaction.emoji.name) && user.id === message.author.id;
      };

      try {
        const collected = await confirmMessage.awaitReactions({
          filter,
          max: 1,
          time: 30000,
          errors: ['time']
        });

        const reaction = collected.first();

        if (reaction.emoji.name === '✅') {
          // Xóa tất cả mapping
          saveUserProfileMapping([]);

          const successEmbed = new EmbedBuilder()
            .setColor('#ff0000')
            .setTitle('🗑️ Xóa Tất Cả Mapping Thành Công')
            .setDescription(`Đã xóa **${mappings.length} mapping** thành công.`)
            .setTimestamp();

          await confirmMessage.edit({ embeds: [successEmbed] });
          await confirmMessage.reactions.removeAll();

          console.log(`🗑️ ${message.author.tag} đã xóa tất cả ${mappings.length} mapping`);
        } else {
          const cancelEmbed = new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle('✅ Đã Hủy Bỏ')
            .setDescription('Không có mapping nào bị xóa.')
            .setTimestamp();

          await confirmMessage.edit({ embeds: [cancelEmbed] });
          await confirmMessage.reactions.removeAll();
        }
      } catch (error) {
        const timeoutEmbed = new EmbedBuilder()
          .setColor('#999999')
          .setTitle('⏰ Hết Thời Gian')
          .setDescription('Đã hết thời gian xác nhận. Không có mapping nào bị xóa.')
          .setTimestamp();

        await confirmMessage.edit({ embeds: [timeoutEmbed] });
        await confirmMessage.reactions.removeAll();
      }
      return;
    }

    // Parse arguments cho lệnh set
    const content = args.join(' ');
    if (!content.includes('|')) {
      return message.reply('❌ | Cú pháp không đúng. Sử dụng: `tes set <player_type> <player_number> | <@user>`\n' +
                          'Ví dụ: `tes set murph 1 | @fiin`');
    }

    const [profilePart, userPart] = content.split('|').map(part => part.trim());
    
    if (!profilePart || !userPart) {
      return message.reply('❌ | Cú pháp không đúng. Cần có cả thông tin profile và user.');
    }

    // Parse profile info
    const profileArgs = profilePart.split(' ');
    if (profileArgs.length < 2) {
      return message.reply('❌ | Cú pháp không đúng. Cần có player type và number.\n' +
                          'Ví dụ: `tes set murph 1 | @fiin`');
    }

    const playerType = normalizePlayerType(profileArgs[0]);
    const playerNumber = parseInt(profileArgs[1]);

    if (isNaN(playerNumber) || playerNumber < 0) {
      return message.reply('❌ | Số player phải là một số không âm.');
    }

    // Kiểm tra player type hợp lệ
    const validTypes = ['MURPH', 'JOSEPH', 'OWNER', 'STAFF', 'SUPPORT'];
    if (!validTypes.includes(playerType)) {
      return message.reply(`❌ | Player type không hợp lệ. Các loại hợp lệ: ${validTypes.join(', ')}`);
    }

    // Parse user mention
    const userMatch = userPart.match(/<@!?(\d+)>/);
    if (!userMatch) {
      return message.reply('❌ | Không tìm thấy mention user hợp lệ. Sử dụng @username');
    }

    const userId = userMatch[1];
    
    // Fetch user để kiểm tra
    let targetUser;
    try {
      targetUser = await client.users.fetch(userId);
    } catch (error) {
      return message.reply('❌ | Không thể tìm thấy user này.');
    }

    // Kiểm tra profile có tồn tại không
    try {
      const profiles = await profileDB.getAllProfilesByType(playerType);
      const profile = profiles.find(p => p.playerNumber === playerNumber);
      
      if (!profile) {
        return message.reply(`❌ | Không tìm thấy profile ${playerType} ${playerNumber}. Hãy tạo profile trước khi gán.`);
      }

      // Gán mapping
      setUserProfileMapping(userId, playerType, playerNumber);

      // Tạo embed thông báo thành công
      const successEmbed = new EmbedBuilder()
        .setColor(client.embedColor || '#0099ff')
        .setTitle('✅ Gán Profile Thành Công')
        .setDescription(`Đã gán profile **${playerType} ${playerNumber}** cho ${targetUser.toString()}`)
        .addFields(
          { name: 'Profile', value: `${playerType} ${playerNumber}`, inline: true },
          { name: 'User', value: targetUser.toString(), inline: true },
          { name: 'Tên Profile', value: profile.name || 'Chưa có tên', inline: true }
        )
        .setThumbnail(targetUser.displayAvatarURL({ dynamic: true }))
        .setTimestamp()
        .setFooter({ text: `Gán bởi ${message.author.tag}` });

      await message.reply({ embeds: [successEmbed] });

      console.log(`✅ Đã gán profile ${playerType} ${playerNumber} cho user ${targetUser.tag} (${userId})`);

    } catch (error) {
      console.error('Lỗi khi gán profile:', error);
      return message.reply('❌ | Có lỗi xảy ra khi gán profile.');
    }
  },

  // Export các hàm utility để sử dụng ở file khác
  loadUserProfileMapping,
  saveUserProfileMapping,
  setUserProfileMapping,
  getUserProfileMapping,
  removeUserProfileMapping
};
