const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const profileDB = require('../data/profileDB');

module.exports = {
  name: 'es_setcolor',
  async execute(client, message, args) {
    // Kiểm tra quyền: <PERSON><PERSON> vì kiểm tra role cụ thể, kiể<PERSON> tra quyền Administrator
    if (!message.member.permissions.has(PermissionFlagsBits.Administrator)) {
      return message.reply('❌ | Bạn không có quyền sử dụng lệnh này!');
    }
    
    // Lấy loại player, số thứ tự và mã màu
    const playerType = args.shift();
    const playerNumber = args.shift();
    const colorCode = args.shift();
    
    if (!playerType || !playerNumber || !colorCode) {
      return message.reply('Cú pháp: tes setcolor <Murph/Joseph> <số> <mã màu hex>');
    }
    
    // Chuyển đổi mã màu hex thành số
    let color;
    try {
      color = parseInt(colorCode.replace('#', ''), 16);
      if (isNaN(color)) throw new Error('Mã màu không hợp lệ');
    } catch (error) {
      return message.reply('Mã màu không hợp lệ. Vui lòng sử dụng mã màu hex (ví dụ: #FF0000)');
    }
    
    // Xác định kênh gửi
    let channelId, emoji;
    if (/^murph/i.test(playerType)) {
      channelId = '1341447431107117128';
      emoji = '<a:prf:1376968984754258102>';
    } else if (/^joseph/i.test(playerType)) {
      channelId = '1341447441345548373';
      emoji = '<a:prf:1376968984754258102>';
    } else {
      return message.reply('Bạn phải nhập Murph hoặc Joseph.');
    }
    
    // Tìm tin nhắn profile gần nhất của player này trong kênh
    const channel = await client.channels.fetch(channelId);
    const messages = await channel.messages.fetch({ limit: 50 });
    const targetMsg = messages.find(msg =>
      msg.embeds[0]?.title?.toLowerCase() === `✟ ${playerType.toUpperCase()} ${playerNumber} ✟`.toLowerCase()
    );
    
    if (!targetMsg) {
      return message.reply('Không tìm thấy profile để cập nhật!');
    }
    
    // Tạo embed mới dựa trên embed cũ, chỉ thay màu
    const oldEmbed = targetMsg.embeds[0];
    const newEmbed = EmbedBuilder.from(oldEmbed).setColor(color);
    
    // Cập nhật embed và database
    await targetMsg.edit({ embeds: [newEmbed] });
    await profileDB.updateProfileColor(playerType.toUpperCase(), Number(playerNumber), color);
    await message.reply(`Đã cập nhật màu profile thành công! (Mã màu: ${colorCode})`);
  }
};