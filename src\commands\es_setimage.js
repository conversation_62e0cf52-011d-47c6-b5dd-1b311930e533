const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const profileDB = require('../data/profileDB');

function toClassicFont(str) {
  const classicMap = {
    A: '𝐀', B: '𝐁', C: '𝐂', D: '𝐃', E: '𝐄', F: '𝐅', G: '𝐆', H: '𝐇', I: '𝐈', J: '𝐉', K: '𝐊', L: '𝐋', M: '𝐌',
    N: '𝐍', O: '𝐎', P: '𝐏', Q: '𝐐', R: '𝐑', S: '𝐒', T: '𝐓', U: '𝐔', V: 'V', W: '𝐖', X: '𝐗', Y: '𝐘', Z: '𝐙',
    a: 'a', b: '𝐛', c: '𝐜', d: '𝐝', e: '𝐞', f: '𝐟', g: '𝐠', h: '𝐡', i: '𝐢', j: '𝐣', k: '𝐤', l: '𝐥', m: '𝐦',
    n: '𝐧', o: '𝐨', p: '𝐩', q: '𝐪', r: '𝐫', s: '𝐬', t: '𝐭', u: '𝐮', v: '𝐯', w: '𝐰', x: '𝐱', y: '𝐲', z: '𝐳',
    '0': '𝟎', '1': '𝟏', '2': '𝟐', '3': '𝟑', '4': '𝟒', '5': '𝟓', '6': '𝟔', '7': '𝟕', '8': '𝟖', '9': '𝟗'
  };
  return str.split('').map(ch => classicMap[ch] || ch).join('');
}

// Thêm hàm capitalizeFirst để chỉ viết hoa chữ cái đầu
function capitalizeFirst(str) {
  if (!str) return str;
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

// Hàm normalize để so sánh title
function normalizeFont(str) {
  return str
    .normalize('NFKD')
    .replace(/[\u0300-\u036f]/g, '') // bỏ dấu
    .replace(/[^a-zA-Z0-9]/g, '')   // chỉ giữ lại chữ cái và số
    .toLowerCase();
}

module.exports = {
  name: 'es_setimage',
  async execute(client, message, args) {
    // Kiểm tra quyền: Administrator hoặc role cụ thể
    const hasAdminPermission = message.member.permissions.has(PermissionFlagsBits.Administrator);
    const hasSpecialRole = message.member.roles.cache.has('1376500798896214108') ||
                          message.member.roles.cache.has('1376884726232514620');

    if (!hasAdminPermission && !hasSpecialRole) {
      return message.reply('❌ | Bạn không có quyền sử dụng lệnh này!');
    }
    
    // Kiểm tra xem có đủ tham số không
    if (!args[0]) {
      return message.reply('Cú pháp: tes setimage <Profile> --thumb=<url> --img=<url>');
    }

    let playerType, playerNumber;
    
    // Kiểm tra xem tham số đầu tiên có phải là tên viết tắt không
    const shortcutMatch = args[0].match(/^(mur|jos|own|stf|adm)(\d+)$/i);
    
    if (shortcutMatch) {
      // Nếu là tên viết tắt, tách thành loại và số
      const prefix = shortcutMatch[1].toLowerCase();
      playerNumber = parseInt(shortcutMatch[2]);
      
      if (prefix === 'mur') {
        playerType = 'Murph'; // Thay đổi từ 'MURPH'
      } else if (prefix === 'jos') {
        playerType = 'Joseph'; // Thay đổi từ 'JOSEPH'
      } else if (prefix === 'own') {
        playerType = 'Owner'; // Thay đổi từ 'OWNER'
      } else if (prefix === 'stf') {
        playerType = 'Staff'; // Thay đổi từ 'STAFF'
      } else if (prefix === 'sup') {
        playerType = 'Support'; // Thay đổi từ 'SUPPORT'
      }
      // Xóa tham số đầu tiên vì đã xử lý
      args.shift();
    } else {
      // Nếu không phải tên viết tắt, xử lý theo cách cũ
      const rawPlayerType = args.shift();
      playerNumber = parseInt(args.shift());
      
      // Chuyển đổi playerType thành định dạng chuẩn với capitalizeFirst
      if (/^murph/i.test(rawPlayerType)) {
        playerType = 'Murph';
      } else if (/^joseph/i.test(rawPlayerType)) {
        playerType = 'Joseph';
      } else if (/^owner/i.test(rawPlayerType)) {
        playerType = 'Owner';
      } else if (/^staff/i.test(rawPlayerType)) {
        playerType = 'Staff';
      } else if (/^support/i.test(rawPlayerType)) {
        playerType = 'Support';
      } else {
        return message.reply('Bạn phải nhập đúng định dạng!');
      }
    }
    
    // Kiểm tra số thứ tự
    if (isNaN(playerNumber)) {
      return message.reply('Số thứ tự không hợp lệ.');
    }

    // Lấy url ảnh
    const argStr = args.join(' ');
    const thumbMatch = argStr.match(/--thumb=(\S+)/);
    const imageMatch = argStr.match(/--img=(\S+)/);
    const thumbnail = thumbMatch ? thumbMatch[1] : null;
    const image = imageMatch ? imageMatch[1] : null;

    // Xác định kênh, emoji và màu dựa trên loại player
    let channelId, titleEmoji, emoji, defaultColor;
    const playerTypeUpper = playerType.toUpperCase(); // Tạo biến riêng cho việc so sánh
    if (playerTypeUpper === 'MURPH') {
      channelId = '1341447431107117128';
      titleEmoji = '<:mur:1378445368689164290>';
      emoji = '<:mu:1385677279065280603>';
      defaultColor = '#BFAEE3';
    } else if (playerTypeUpper === 'JOSEPH') {
      channelId = '1341447441345548373';
      titleEmoji = '<:jos:1378445302381674547>';
      emoji = '<:jo:1385684426976923659>';
      defaultColor = '#0C5776';
    } else if (playerTypeUpper === 'OWNER') {
      channelId = '1385627757643304981';
      titleEmoji = '<a:ad:1385687143015383100>';
      emoji = '<a:white:1376852619632316517>';
      defaultColor = '#c02626';
    } else if (playerTypeUpper === 'ADMIN' || playerTypeUpper === 'STAFF') {
      channelId = '1385627757643304981';
      titleEmoji = '<a:ad:1385687143015383100>';
      emoji = '<a:white:1376852619632316517>';
      defaultColor = '#c02626';
    } else if (playerTypeUpper === 'SUPPORT') {
      channelId = '1385627757643304981';
      titleEmoji = '<a:ad:1385687143015383100>';
      emoji = '<a:white:1376852619632316517>';
      defaultColor = '#c02626';
    }

    try {
      // Tìm tin nhắn profile gần nhất của player này trong kênh
      const channel = await client.channels.fetch(channelId);
      const messages = await channel.messages.fetch({ limit: 50 });
      
      // Tạo pattern tìm kiếm linh hoạt
      const searchPattern = normalizeFont(`${playerTypeUpper} ${playerNumber}`); // Sử dụng playerTypeUpper cho tìm kiếm
      
      const targetMsg = messages.find(msg => {
        const embedTitle = msg.embeds[0]?.title || '';
        return msg.author.id === client.user.id &&
               msg.embeds[0] &&
               normalizeFont(embedTitle).includes(searchPattern);
      });

      if (!targetMsg) {
        return message.reply('Không tìm thấy profile để cập nhật!');
      }

      // Tạo embed mới dựa trên embed cũ, chỉ thay ảnh
      const oldEmbed = targetMsg.embeds[0];
      const newEmbed = EmbedBuilder.from(oldEmbed);

      // Cập nhật title với emoji đúng
      const displayTitle = `${toClassicFont(playerType)} ${toClassicFont(playerNumber.toString())}`; // Sử dụng playerType thay vì playerTypeUpper
      newEmbed.setTitle(`${titleEmoji} **${displayTitle}** ${titleEmoji}`);

      if (thumbnail && /^https?:\/\/.+\..+/.test(thumbnail)) {
        newEmbed.setThumbnail(thumbnail);
      }
      if (image && /^https?:\/\/.+\..+/.test(image)) {
        newEmbed.setImage(image);
      }

      await targetMsg.edit({ embeds: [newEmbed] });
      await profileDB.updateProfileImage(playerTypeUpper, Number(playerNumber), thumbnail, image); // Sử dụng playerTypeUpper cho DB
      await message.reply('Đã cập nhật ảnh thành công!');
      
    } catch (error) {
      console.error('Lỗi khi cập nhật ảnh profile:', error);
      await message.reply(`Đã xảy ra lỗi khi cập nhật ảnh: ${error.message}`);
    }
  }
};