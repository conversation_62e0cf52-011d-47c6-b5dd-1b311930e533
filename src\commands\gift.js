const { EmbedBuilder } = require('discord.js');
const purchaseDB = require('../data/purchaseDB');
const shopItems = [
  { id: '01', emoji: '<:fistdate:1395218976115789925>', name: '**Fist date**', price: 100000 },
  { id: '02', emoji: '<:eternia:1395218967681040527>', name: '**Eternia**', price: 200000 },
  { id: '03', emoji: '<:amora:1395218962815651924>', name: '**Amora**', price: 300000 },
  { id: '04', emoji: '<:serenity:1395218985401716867>', name: '**Serenity**', price: 400000 },
  { id: '05', emoji: '<:unity:1395218993220026418>', name: '**Unity**', price: 500000 },
];

module.exports = {
  name: 'gift',
  description: 'Tặng nhẫn cho user khác',
  usage: '@user <id nhẫn> <số lượng>',
  async execute(client, message, args) {
    if (args.length < 3) {
      const failEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
        .setDescription('<:error:1383005371542798346> | Cú pháp không đúng!\n\n**Cú pháp đúng:** `tgift @user <id nhẫn> <số lượng>`')
        .setFooter({ text: `${message.author.username} - Hôm nay lúc ${new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}`, iconURL: message.author.displayAvatarURL() })
        .setColor(client.embedColor || '#0099ff');
      return message.reply({ embeds: [failEmbed] });
    }
    
    const mention = message.mentions.users.first();
    if (!mention) {
      const failEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
        .setDescription('<:error:1383005371542798346> | Bạn phải tag người nhận!')
        .setFooter({ text: `${message.author.username} - Hôm nay lúc ${new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}`, iconURL: message.author.displayAvatarURL() })
        .setColor(client.embedColor || '#0099ff');
      return message.reply({ embeds: [failEmbed] });
    }
    
    const receiverId = mention.id;
    const senderId = message.author.id;
    const itemId = args[1];
    const quantity = parseInt(args[2]);
    
    if (!/^[0-9]{2}$/.test(itemId)) {
      const failEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
        .setDescription('<:error:1383005371542798346> | ID nhẫn không hợp lệ!')
        .setFooter({ text: `${message.author.username} - Hôm nay lúc ${new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}`, iconURL: message.author.displayAvatarURL() })
        .setColor(client.embedColor || '#0099ff');
      return message.reply({ embeds: [failEmbed] });
    }
    
    if (isNaN(quantity) || quantity < 1) {
      const failEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
        .setDescription('<:error:1383005371542798346> | Số lượng phải là số nguyên dương!')
        .setFooter({ text: `${message.author.username} - Hôm nay lúc ${new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}`, iconURL: message.author.displayAvatarURL() })
        .setColor(client.embedColor || '#0099ff');
      return message.reply({ embeds: [failEmbed] });
    }
    
    if (receiverId === senderId) {
      const failEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
        .setDescription('<:error:1383005371542798346> | Bạn không thể tự tặng nhẫn cho chính mình!')
        .setFooter({ text: `${message.author.username} - Hôm nay lúc ${new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}`, iconURL: message.author.displayAvatarURL() })
        .setColor(client.embedColor || '#0099ff');
      return message.reply({ embeds: [failEmbed] });
    }
    
    const shopItem = shopItems.find(i => i.id === itemId);
    if (!shopItem) {
      const failEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
        .setDescription('<:error:1383005371542798346> | Không tìm thấy nhẫn với ID này!')
        .setFooter({ text: `${message.author.username} - Hôm nay lúc ${new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}`, iconURL: message.author.displayAvatarURL() })
        .setColor(client.embedColor || '#0099ff');
      return message.reply({ embeds: [failEmbed] });
    }
    
    // Kiểm tra inventory người gửi
    const senderInventory = await purchaseDB.getInventory(senderId);
    const senderItem = senderInventory.find(i => i.itemId === itemId);
    if (!senderItem || senderItem.total < quantity) {
      const failEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
        .setDescription(`<:error:1383005371542798346> | Bạn không đủ số lượng nhẫn để tặng!`)
        .setFooter({ text: `${message.author.username} - Hôm nay lúc ${new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}`, iconURL: message.author.displayAvatarURL() })
        .setColor(client.embedColor || '#0099ff');
      return message.reply({ embeds: [failEmbed] });
    }
    
    // Trừ nhẫn khỏi người gửi
    await purchaseDB.addPurchase(senderId, itemId, -quantity);
    // Thêm nhẫn cho người nhận
    await purchaseDB.addPurchase(receiverId, itemId, quantity);
    
    // Thông báo thành công
    const embed = new EmbedBuilder()
      .setColor(client.embedColor || '#0099ff') 
      .setAuthor({ name: 'GIFT SENT', iconURL: client.user.displayAvatarURL() })
      .setDescription(`<:done:1383009630581424250> | Bạn đã tặng ${shopItem.emoji} **${shopItem.name}** (x\`${quantity}\`) cho <@${receiverId}>!`)
      .setFooter({ text: `${message.author.username} - Hôm nay lúc ${new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}`, iconURL: message.author.displayAvatarURL() });
    return message.reply({ embeds: [embed] });
  },
};