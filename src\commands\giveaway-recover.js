const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const giveawayDB = require('../data/giveawayDB');

module.exports = {
  name: 'giveaway-recover',
  aliases: ['ga-recover', 'tga-recover'],
  description: '<PERSON>h<PERSON><PERSON> phục và kiểm tra giveaway bị lỗi',
  usage: 'giveaway-recover <check|fix|end> [message_id]',
  
  async execute(client, message, args) {
    // Kiểm tra quyền
    const hasPermission = message.member.permissions.has(PermissionFlagsBits.ManageEvents) ||
                         message.member.roles.cache.some(role => 
                           role.name.toLowerCase().includes('staff') || 
                           role.name.toLowerCase().includes('admin')
                         );

    if (!hasPermission) {
      return message.reply('❌ | Bạn không có quyền sử dụng lệnh này.');
    }

    const subCommand = args[0]?.toLowerCase();

    if (subCommand === 'check') {
      // <PERSON><PERSON><PERSON> tra tất cả giveaway đang hoạt động
      const activeGiveaways = giveawayDB.getActiveGiveaways();

      if (activeGiveaways.length === 0) {
        return message.reply('✅ | Không có giveaway nào đang hoạt động trong database.');
      }

      const embed = new EmbedBuilder()
        .setColor(client.embedColor || '#0099ff')
        .setTitle('🔍 Giveaway đang hoạt động')
        .setDescription('Danh sách các giveaway chưa kết thúc:')
        .setTimestamp();

      for (const giveaway of activeGiveaways) {
        const timeLeft = giveaway.endTime - Date.now();
        const timeStatus = timeLeft > 0 ? `Còn ${Math.round(timeLeft / 1000)}s` : '⚠️ ĐÃ QUÁ HẠN';

        embed.addFields({
          name: `🎁 ${giveaway.prize}`,
          value: [
            `**Message ID:** ${giveaway.messageId}`,
            `**Channel:** <#${giveaway.channelId}>`,
            `**Người tạo:** <@${giveaway.createdBy}>`,
            `**Số người thắng:** ${giveaway.winnerCount}`,
            `**Trạng thái:** ${timeStatus}`,
            `**Người tham gia:** ${giveaway.participants.length}`
          ].join('\n'),
          inline: false
        });
      }

      return message.reply({ embeds: [embed] });
    }

    if (subCommand === 'restore') {
      // Khôi phục giveaway từ tin nhắn Discord (không có trong database)
      if (!args[1]) {
        return message.reply('❌ | Vui lòng cung cấp Message ID. Sử dụng: `tga-recover restore <message_id> [channel_id]`');
      }

      const messageId = args[1];
      const channelId = args[2] || message.channel.id;

      // Kiểm tra xem giveaway đã tồn tại trong database chưa
      const existingGiveaway = giveawayDB.findGiveaway(messageId);
      if (existingGiveaway) {
        return message.reply('❌ | Giveaway này đã tồn tại trong database. Sử dụng `fix` hoặc `end` thay vì `restore`.');
      }

      try {
        const guild = message.guild;
        const channel = guild.channels.cache.get(channelId);

        if (!channel) {
          return message.reply('❌ | Không tìm thấy channel. Vui lòng cung cấp Channel ID hợp lệ.');
        }

        const giveawayMessage = await channel.messages.fetch(messageId);

        if (!giveawayMessage || !giveawayMessage.embeds[0]) {
          return message.reply('❌ | Không tìm thấy tin nhắn giveaway hoặc tin nhắn không có embed.');
        }

        const embed = giveawayMessage.embeds[0];
        const description = embed.description || '';

        // Trích xuất thông tin từ embed
        let prize = 'Unknown Prize';
        let winnerCount = 1;
        let createdBy = giveawayMessage.author?.id || message.author.id;

        // Tìm prize từ description hoặc title
        if (embed.title && embed.title.includes('GIVEAWAY')) {
          const titleMatch = embed.title.match(/🎁\s*(.+?)(?:\s*🎁|$)/);
          if (titleMatch) prize = titleMatch[1].trim();
        }

        if (description.includes('**') && description.includes('**')) {
          const prizeMatch = description.match(/\*\*([^*]+)\*\*/);
          if (prizeMatch) prize = prizeMatch[1].trim();
        }

        // Tìm số người thắng
        const winnerMatch = description.match(/(\d+)\s*người\s*thắng|(\d+)\s*winner/i);
        if (winnerMatch) {
          winnerCount = parseInt(winnerMatch[1] || winnerMatch[2]);
        }

        // Tìm người tạo
        const creatorMatch = description.match(/<@(\d+)>/);
        if (creatorMatch) {
          createdBy = creatorMatch[1];
        }

        // Fetch participants từ reactions
        const reaction = giveawayMessage.reactions.cache.get('🎉');
        let participants = [];

        if (reaction) {
          const users = await reaction.users.fetch();
          participants = users
            .filter(user => !user.bot)
            .map(user => user.id);
        }

        // Tạo giveaway object để lưu vào database
        const restoredGiveaway = {
          messageId: messageId,
          channelId: channelId,
          guildId: guild.id,
          prize: prize,
          winnerCount: winnerCount,
          endTime: Date.now(), // Đã hết hạn
          participants: participants,
          ended: false, // Sẽ được đánh dấu ended sau khi restore
          createdBy: createdBy,
          createdAt: Date.now() - 86400000 // Giả định tạo 1 ngày trước
        };

        // Lưu vào database
        giveawayDB.addGiveaway(restoredGiveaway);

        const restoreEmbed = new EmbedBuilder()
          .setColor(client.embedColor || '#0099ff')
          .setTitle('Đã khôi phục giveaway')
          .addFields(
            { name: '🎁 Phần thưởng', value: prize, inline: true },
            { name: '🏆 Số người thắng', value: winnerCount.toString(), inline: true },
            { name: '👥 Người tham gia', value: participants.length.toString(), inline: true },
            { name: '📝 Message ID', value: messageId, inline: true },
            { name: '📍 Channel', value: `<#${channelId}>`, inline: true },
            { name: '👤 Người tạo', value: `<@${createdBy}>`, inline: true },
            { name: '📋 Danh sách tham gia', value: participants.length > 0 ? participants.slice(0, 10).map(id => `<@${id}>`).join(', ') + (participants.length > 10 ? `\n... và ${participants.length - 10} người khác` : '') : 'Không có ai tham gia', inline: false }
          )
          .setFooter({ text: 'Giveaway đã được khôi phục vào database. Sử dụng "end" để kết thúc và chọn người thắng.' });

        return message.reply({ embeds: [restoreEmbed] });

      } catch (error) {
        console.error('Lỗi khi khôi phục giveaway:', error);
        return message.reply(`❌ | Có lỗi xảy ra khi khôi phục giveaway: ${error.message}`);
      }
    }

    if (subCommand === 'fix') {
      // Sửa giveaway bằng cách fetch participants từ reactions
      if (!args[1]) {
        return message.reply('❌ | Vui lòng cung cấp Message ID. Sử dụng: `tga-recover fix <message_id>`');
      }

      const messageId = args[1];
      const giveaway = giveawayDB.findGiveaway(messageId);
      
      if (!giveaway) {
        return message.reply('❌ | Không tìm thấy giveaway với ID đã cung cấp.');
      }

      try {
        const guild = client.guilds.cache.get(giveaway.guildId);
        const channel = guild.channels.cache.get(giveaway.channelId);
        const giveawayMessage = await channel.messages.fetch(giveaway.messageId);
        
        const reaction = giveawayMessage.reactions.cache.get('🎉');
        let participants = [];
        
        if (reaction) {
          const users = await reaction.users.fetch();
          participants = users
            .filter(user => !user.bot)
            .map(user => user.id);
          
          // Cập nhật participants vào database
          giveawayDB.updateGiveaway(messageId, { participants });
          
          const embed = new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle('✅ Đã khôi phục giveaway')
            .addFields(
              { name: '🎁 Phần thưởng', value: giveaway.prize, inline: true },
              { name: '👥 Người tham gia', value: participants.length.toString(), inline: true },
              { name: '📝 Danh sách', value: participants.length > 0 ? participants.map(id => `<@${id}>`).join(', ') : 'Không có ai tham gia', inline: false }
            );
          
          return message.reply({ embeds: [embed] });
        } else {
          return message.reply('❌ | Không tìm thấy reaction 🎉 trên tin nhắn giveaway.');
        }
      } catch (error) {
        console.error('Lỗi khi khôi phục giveaway:', error);
        return message.reply('❌ | Có lỗi xảy ra khi khôi phục giveaway.');
      }
    }

    if (subCommand === 'end') {
      // Kết thúc giveaway thủ công
      if (!args[1]) {
        return message.reply('❌ | Vui lòng cung cấp Message ID. Sử dụng: `tga-recover end <message_id>`');
      }

      const messageId = args[1];
      const giveaway = giveawayDB.findGiveaway(messageId);
      
      if (!giveaway) {
        return message.reply('❌ | Không tìm thấy giveaway với ID đã cung cấp.');
      }

      if (giveaway.ended) {
        return message.reply('❌ | Giveaway này đã kết thúc rồi.');
      }

      try {
        const guild = client.guilds.cache.get(giveaway.guildId);
        const channel = guild.channels.cache.get(giveaway.channelId);
        const giveawayMessage = await channel.messages.fetch(giveaway.messageId);
        
        // Fetch participants từ reactions
        const reaction = giveawayMessage.reactions.cache.get('🎉');
        let participants = [];
        
        if (reaction) {
          const users = await reaction.users.fetch();
          participants = users
            .filter(user => !user.bot)
            .map(user => user.id);
        }

        // Chọn người thắng
        let winners = [];
        if (participants.length > 0) {
          const shuffled = participants.sort(() => 0.5 - Math.random());
          winners = shuffled.slice(0, Math.min(giveaway.winnerCount, participants.length));
        }

        // Cập nhật database
        giveawayDB.updateGiveaway(messageId, { 
          participants, 
          ended: true 
        });

        // Tạo embed kết thúc
        const winnerMentions = winners.length > 0 ? winners.map(id => `<@${id}>`).join(', ') : 'Không có người thắng';
        
        const endedEmbed = EmbedBuilder.from(giveawayMessage.embeds[0])
          .setColor('#203354')
          .setTitle('🎉 GIVEAWAY KẾT THÚC 🎉')
          .setDescription(`**${giveaway.prize}**\n\nNgười thắng: ${winnerMentions}\nNgười tổ chức: <@${giveaway.createdBy}>`)
          .spliceFields(0, 3)
          .setFooter({ text: `Giveaway kết thúc: ${new Date().toLocaleString('vi-VN')}` });

        await giveawayMessage.edit({ embeds: [endedEmbed] });

        // Gửi thông báo kết quả
        const resultEmbed = new EmbedBuilder()
          .setColor(client.embedColor || '#0099ff')
          .setTitle('🎉 KẾT QUÁ GIVEAWAY')
          .setDescription(`**${giveaway.prize}**`)
          .addFields(
            { name: 'Người thắng', value: winnerMentions, inline: false },
            { name: 'Tổng số người tham gia', value: participants.length.toString(), inline: true },
            { name: 'Số giải thưởng', value: giveaway.winnerCount.toString(), inline: true }
          )
          .setFooter({ text: 'Chúc mừng các bạn đã thắng!' })
          .setTimestamp();

        await channel.send({ embeds: [resultEmbed] });
        
        return message.reply('✅ | Đã kết thúc giveaway thành công!');
        
      } catch (error) {
        console.error('Lỗi khi kết thúc giveaway:', error);
        return message.reply('❌ | Có lỗi xảy ra khi kết thúc giveaway.');
      }
    }

    // Hiển thị help nếu không có subcommand hợp lệ
    const helpEmbed = new EmbedBuilder()
      .setColor('#ff9900')
      .setTitle('🛠️ Giveaway Recovery Tool')
      .setDescription('Công cụ khôi phục giveaway bị lỗi')
      .addFields(
        { name: '🔍 Check', value: '`tga-recover check` - Kiểm tra giveaway đang hoạt động', inline: false },
        { name: '🔄 Restore', value: '`tga-recover restore <message_id> [channel_id]` - Khôi phục giveaway không có trong database', inline: false },
        { name: '🔧 Fix', value: '`tga-recover fix <message_id>` - Khôi phục participants từ reactions', inline: false },
        { name: '🏁 End', value: '`tga-recover end <message_id>` - Kết thúc giveaway thủ công', inline: false }
      )
      .setFooter({ text: 'Chỉ dành cho Staff/Admin' });

    return message.reply({ embeds: [helpEmbed] });
  }
};
