const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const giveawayDB = require('../data/giveawayDB');
const giveawayManager = require('../utils/giveawayManager');

module.exports = {
  name: 'giveaway',
  aliases: ['ga', 'tga'],
  description: 'Quản lý giveaway',
  usage: 'giveaway <start|end|reroll|help> [options]',
  
  async execute(client, message, args) {
    // Kiểm tra message và author
    if (!message || !message.author) {
      console.error('❌ Message hoặc author không hợp lệ');
      return;
    }

    const subCommand = args[0]?.toLowerCase();

    // Lệnh help
    if (subCommand === 'help' || !subCommand) {
      const helpEmbed = new EmbedBuilder()
        .setColor('#203354')
        .setTitle('📖 **Hướng dẫn sử dụng Giveaway**')
        .setDescription(
          '**<PERSON><PERSON><PERSON> lệnh sử dụng:**\n' +
          '```' +
          'tga start <thời gian> <số người thắng> <phần thưởng>\n' +
          '→ Tạo giveaway mới\n\n' +
          'tga end <message_id>\n' +
          '→ Kết thúc giveaway sớm\n\n' +
          'tga reroll <message_id>\n' +
          '→ Roll lại giveaway đã kết thúc\n\n' +
          'tga help\n' +
          '→ Hiển thị hướng dẫn sử dụng' +
          '```\n' +
          '**Lưu ý:**\n' +
          '- Thời gian: s (giây), m (phút), h (giờ), d (ngày)\n' +
          '- Bạn cần quyền **Manage Events** hoặc role **Staff** để sử dụng bot.'
        )
        .setFooter({
          text: `Yêu cầu bởi ${message.author?.tag || 'Unknown'}`,
          iconURL: message.author?.displayAvatarURL() || null
        });
      
      return message.reply({ 
        embeds: [helpEmbed], 
        allowedMentions: { users: [message.author.id] } 
      });
    }

    // Kiểm tra quyền
    if (!message.member) {
      return message.reply('❌ | Không thể xác định quyền của bạn.');
    }

    const hasPermission = message.member.permissions.has(PermissionFlagsBits.ManageEvents) ||
                         message.member.roles.cache.some(role =>
                           role.name.toLowerCase().includes('staff') ||
                           role.name.toLowerCase().includes('admin')
                         );

    if (!hasPermission) {
      return message.reply('❌ | Bạn không có quyền sử dụng lệnh này. Chỉ những người có quyền quản lý sự kiện hoặc role Staff mới có thể sử dụng.');
    }

    // Tạo giveaway mới
    if (subCommand === 'start' || subCommand === 's') {
      if (args.length < 4) {
        return message.reply('❌ | Cú pháp không đúng. Sử dụng: `tga start <thời gian> <số người thắng> <phần thưởng>`');
      }

      try {
        const durationMs = giveawayManager.parseDuration(args[1]);
        const winnerCount = parseInt(args[2]);
        
        if (isNaN(winnerCount) || winnerCount <= 0) {
          return message.reply('❌ | Số người thắng phải là một số dương.');
        }

        const prize = args.slice(3).join(' ');
        const endTime = Date.now() + durationMs;

        // Tạo giveaway
        const giveaway = giveawayManager.createGiveaway({
          message,
          prize,
          winnerCount,
          duration: durationMs,
          endTime
        });

        // Tạo embed
        const giveawayEmbed = giveawayManager.createGiveawayEmbed(giveaway, message.author);
        
        // Gửi tin nhắn giveaway
        const giveawayMessage = await message.channel.send({ embeds: [giveawayEmbed] });
        await giveawayMessage.react('🎉');

        // Cập nhật message ID và lưu vào database
        giveaway.messageId = giveawayMessage.id;
        giveawayDB.addGiveaway(giveaway);

        // Đặt timer để kết thúc giveaway
        setTimeout(() => {
          giveawayManager.endGiveaway(client, giveaway.messageId);
        }, durationMs);

        // Xóa tin nhắn lệnh
        try {
          await message.delete();
          console.log(`✅ Đã xóa tin nhắn lệnh tạo giveaway từ ${message.author.tag}`);
        } catch (error) {
          console.error('❌ Lỗi khi xóa tin nhắn lệnh tạo giveaway:', error);
        }

        console.log(`✅ Đã tạo giveaway: ${prize} (${winnerCount} giải, kết thúc sau ${args[1]})`);
        
      } catch (error) {
        return message.reply(`❌ | ${error.message}`);
      }
      return;
    }

    // Kết thúc giveaway sớm
    if (subCommand === 'end' || subCommand === 'e') {
      if (args.length < 2) {
        return message.reply('❌ | Cú pháp không đúng. Sử dụng: `tga end <message_id>`');
      }

      const messageId = args[1];
      const giveaway = giveawayDB.findGiveaway(messageId);
      
      if (!giveaway) {
        return message.reply('❌ | Không tìm thấy giveaway với ID đã cung cấp.');
      }

      if (giveaway.ended) {
        return message.reply('❌ | Giveaway này đã kết thúc rồi.');
      }

      await giveawayManager.endGiveaway(client, messageId);

      // Xóa tin nhắn lệnh
      try {
        await message.delete();
        console.log(`✅ Đã xóa tin nhắn lệnh kết thúc giveaway từ ${message.author.tag}`);
      } catch (error) {
        console.error('❌ Lỗi khi xóa tin nhắn lệnh kết thúc giveaway:', error);
      }
      return;
    }

    // Roll lại giveaway
    if (subCommand === 'reroll' || subCommand === 'r') {
      if (args.length < 2) {
        return message.reply('❌ | Cú pháp không đúng. Sử dụng: `tga reroll <message_id>`');
      }

      const messageId = args[1];
      const giveaway = giveawayDB.findGiveaway(messageId);
      
      if (!giveaway) {
        return message.reply('❌ | Không tìm thấy giveaway với ID đã cung cấp.');
      }

      if (!giveaway.ended) {
        return message.reply('❌ | Giveaway này chưa kết thúc. Bạn chỉ có thể roll lại giveaway đã kết thúc.');
      }

      try {
        const channel = await client.channels.fetch(giveaway.channelId);
        const giveawayMessage = await channel.messages.fetch(giveaway.messageId);
        const reaction = giveawayMessage.reactions.cache.get('🎉');
        
        let participants = [];
        if (reaction) {
          const users = await reaction.users.fetch();
          participants = users.filter(user => !user.bot).map(user => user.id);
          giveawayDB.updateGiveaway(messageId, { participants });
        }

        const winners = giveawayManager.selectRandomWinners(participants, giveaway.winnerCount);
        const winnerMentions = winners.length > 0
          ? winners.map(id => `<@${id}>`).join(', ')
          : 'Không có người thắng cuộc (không có người tham gia)';

        // Cập nhật embed với người thắng mới
        const endedEmbed = EmbedBuilder.from(giveawayMessage.embeds[0])
          .setColor('#203354')
          .setTitle('🎉 GIVEAWAY KẾT THÚC 🎉')
          .setDescription(`**${giveaway.prize}**\n\nNgười thắng: ${winnerMentions}\nNgười tổ chức: <@${giveaway.createdBy}>`)
          .spliceFields(0, 3)
          .setFooter({ text: `Giveaway kết thúc: ${new Date().toLocaleString('vi-VN')}` });

        await giveawayMessage.edit({ embeds: [endedEmbed] });
        await giveawayManager.displayGiveawayResults(message.channel, giveaway, winners);

        // Xóa tin nhắn lệnh
        try {
          await message.delete();
          console.log(`✅ Đã xóa tin nhắn lệnh reroll giveaway từ ${message.author.tag}`);
        } catch (error) {
          console.error('❌ Lỗi khi xóa tin nhắn lệnh reroll giveaway:', error);
        }

      } catch (error) {
        console.error('❌ Lỗi khi roll lại giveaway:', error);
        return message.reply('❌ | Có lỗi xảy ra khi roll lại giveaway.');
      }
      return;
    }

    // Lệnh không hợp lệ
    return message.reply('❌ | Lệnh không hợp lệ. Sử dụng `tga help` để xem hướng dẫn.');
  }
};
