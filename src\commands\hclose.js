const { EmbedBuilder, PermissionFlagsBits, ChannelType } = require('discord.js');

// Import Map từ interactionCreate.js để truy cập thông tin ticket
const { ticketLogMessages } = require('../events/interactionCreate.js');

// Thêm import
const TranscriptManager = require('../utils/transcriptManager');
const transcriptManager = new TranscriptManager();

module.exports = {
    name: 'close',
    description: 'Đóng ticket hiện tại',
    async execute(client, message, args) {
        try {
            // Kiểm tra xem có phải đang trong ticket channel hoặc thread không
            const channelName = message.channel.name;
            const categoryName = message.channel.parent?.name || '';
            const isTicketChannel = channelName.startsWith('└') ||
                                   categoryName.includes('TICKET') ||
                                   channelName.startsWith('ticket-') ||
                                   channelName.includes('-booking') ||
                                   channelName.includes('-support') ||
                                   channelName.includes('-recruitment');

            // Ki<PERSON><PERSON> tra xem có phải ticket thread không
            const isTicketThread = message.channel.isThread() &&
                                  (channelName.includes('Booking -') ||
                                   channelName.includes('Support -') ||
                                   channelName.includes('Tuyển dụng -'));

            if (!isTicketChannel && !isTicketThread) {
                const errorEmbed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
                    .setDescription('<:error:1383005371542798346> | Lệnh này chỉ có thể sử dụng trong ticket channel hoặc ticket thread!')
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [errorEmbed] });
            }
            
            // Kiểm tra quyền - admin hoặc các role được phép đóng ticket
            const hasPermission = message.member.permissions.has(PermissionFlagsBits.Administrator) ||
                                 message.member.roles.cache.has('1376500798896214108') ||
                                 message.member.roles.cache.has('1376884726232514620');
            
            if (!hasPermission) {
                const permissionEmbed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
                    .setDescription('<:error:1383005371542798346> | Bạn không có quyền đóng ticket!')
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [permissionEmbed] });
            }
            
            // TẠO TRANSCRIPT TRƯỚC KHI ĐÓNG
            let transcriptFile = null;
            try {
                const ticketData = ticketLogMessages.get(channelName);
                transcriptFile = await transcriptManager.createTranscript(message.channel, ticketData);
                console.log(`Đã tạo transcript: ${transcriptFile.fileName}`);
            } catch (error) {
                console.error('Lỗi khi tạo transcript:', error);
            }
            
            // TÌM VÀ UPDATE LOG EMBED - Giống như nút đóng ticket
            const ticketData = ticketLogMessages.get(channelName);
            
            if (ticketData) {
                try {
                    const logChannel = client.channels.cache.get(ticketData.channelId);
                    if (logChannel) {
                        const ownerUsername = channelName.split('-')[0];
                        
                        // Thay vì edit embed cũ, gửi embed mới với transcript
                        const newCloseEmbed = new EmbedBuilder()
                            .setColor(0xff0000)
                            .setTitle('TICKET ĐƯỢC ĐÓNG')
                            .setThumbnail(message.author.displayAvatarURL())
                            .setDescription(
                                `**Người tạo:** <@${ticketData.creatorId}> (${ticketData.creatorTag})\n\n` +
                                `**Loại ticket:** ${ticketData.ticketType}\n\n` +
                                `**Kênh:** ${ticketData.ticketChannel}\n\n` +
                                `**Owner ticket:** ${ownerUsername}\n\n` +
                                `**Thời gian tạo:** <t:${ticketData.createdAt}:F>\n\n` +
                                `**Người đóng:** ${message.author} (${message.author.tag})\n\n` +
                                `**Phương thức:** Lệnh \`hclose\`\n\n` +
                                `**Thời gian đóng:** <t:${Math.floor(Date.now() / 1000)}:F>\n\n` +
                                `**Trạng thái:** 🔴 Đã đóng\n\n` +
                                `**Transcript:** ${transcriptFile ? '<:done:1383009630581424250> Đã tạo' : '<:error:1383005371542798346> Lỗi tạo file'}`
                            )
                            .setTimestamp()
                            .setFooter({ text: `Closed by: ${message.author.id}` });
                        
                        // Gửi embed mới với transcript file
                        const newLogContent = { embeds: [newCloseEmbed] };
                        if (transcriptFile) {
                            newLogContent.files = [transcriptFile.attachment];
                        }
                        
                        await logChannel.send(newLogContent);
                        
                        // Xóa khỏi Map sau khi đóng
                        ticketLogMessages.delete(channelName);
                    }
                } catch (error) {
                    console.error('Lỗi khi gửi log embed mới:', error);
                    // Nếu không thể gửi, tạo log mới như cũ
                    await this.createFallbackLog(logChannel, channelName, message, ticketData, transcriptFile);
                }
            } else {
                // Nếu không có thông tin trong Map, tạo log mới
                const logChannel = client.channels.cache.get('1380335203364503644');
                if (logChannel) {
                    await this.createFallbackLog(logChannel, channelName, message, null, transcriptFile);
                }
            }
            
            // Thông báo và đóng ticket
            const successEmbed = new EmbedBuilder()
                .setAuthor({ name: 'ACTION SUCCESSFUL', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:done:1383009630581424250> | Ticket sẽ được đóng sau 5 giây...')
                .setColor(client.embedColor || '#0099ff')
                .setTimestamp();
            await message.reply({ embeds: [successEmbed] });
            
            setTimeout(async () => {
                try {
                    const channel = message.channel;

                    // Xử lý đóng thread hoặc channel
                    if (channel.isThread()) {
                        try {
                            // Lock thread trước để user không thể gửi tin nhắn
                            await channel.setLocked(true);
                            console.log(`🔒 Đã khóa thread: ${channel.name}`);

                            // Sau đó archive thread
                            await channel.setArchived(true);
                            console.log(`📁 Đã archive thread: ${channel.name}`);
                        } catch (error) {
                            console.log(`❌ Không thể khóa hoặc archive thread: ${channel.name}`);
                        }
                    } else if (channel && channel.deletable) {
                        // Xóa text channel
                        await channel.delete();
                        console.log(`🗑️ Đã xóa channel: ${channel.name}`);
                    }
                } catch (error) {
                    console.error('Lỗi khi đóng ticket:', error);
                }
            }, 5000);
        } catch (error) {
            console.error('Lỗi trong lệnh hclose:', error);
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi thực hiện lệnh!')
                .setColor(client.embedColor || '#0099ff')
                .setTimestamp();
            try {
                await message.reply({ embeds: [errorEmbed] });
            } catch (replyError) {
                console.error('Không thể gửi thông báo lỗi:', replyError);
            }
        }
    },
    
    // Hàm tạo log dự phòng
    async createFallbackLog(logChannel, channelName, message, ticketData, transcriptFile) {
        let ticketType = 'Unknown';
        let ownerUsername = 'Unknown';

        // Xử lý cho thread format: "Booking - username" hoặc "Support - username"
        if (channelName.includes(' - ')) {
            const parts = channelName.split(' - ');
            ticketType = parts[0]; // "Booking", "Support", "Tuyển dụng"
            ownerUsername = parts[1]; // username
        }
        // Xử lý cho channel format: "ticket-type-username"
        else {
            if (channelName.includes('-booking')) ticketType = 'Booking';
            else if (channelName.includes('-support')) ticketType = 'Support';
            else if (channelName.includes('-recruitment')) ticketType = 'Tuyển dụng';

            const parts = channelName.split('-');
            ownerUsername = parts.length >= 3 ? parts[2] : parts[0];
        }
        
        const fallbackEmbed = new EmbedBuilder()
            .setColor(0xff0000)
            .setTitle('TICKET ĐƯỢC ĐÓNG')
            .setThumbnail(message.author.displayAvatarURL())
            .setDescription(
                `**Người tạo:** ${ownerUsername}\n\n` +
                `**Loại ticket:** ${ticketType}\n\n` +
                `**Kênh:** <#${message.channel.id}>\n\n` +
                `**Owner ticket:** ${ownerUsername}\n\n` +
                `**Người đóng:** ${message.author} (${message.author.tag})\n\n` +
                `**Phương thức:** Lệnh \`hclose\`\n\n` +
                `**Thời gian đóng:** <t:${Math.floor(Date.now() / 1000)}:F>\n\n` +
                `**Trạng thái:** 🔴 Đã đóng\n\n` +
                `**Transcript:** ${transcriptFile ? '<:done:1383009630581424250> Đã tạo' : '<:error:1383005371542798346> Lỗi tạo file'}`
            )
            .setTimestamp()
            .setFooter({ text: `Closed by: ${message.author.id}` });
        
        const fallbackContent = { embeds: [fallbackEmbed] };
        if (transcriptFile) {
            fallbackContent.files = [transcriptFile.attachment];
        }
        
        await logChannel.send(fallbackContent);
    }
};