const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const fs = require('fs');
const path = require('path');

module.exports = {
  name: 'help',
  aliases: ['h', 'commands'],
  description: '<PERSON><PERSON><PERSON> thị danh sách lệnh và cách sử dụng',
  usage: 'help [command_name]',
  
  async execute(client, message, args) {
    const commandName = args[0]?.toLowerCase();
    
    // Nếu có tên lệnh cụ thể, hiển thị chi tiết lệnh đó
    if (commandName) {
      const command = client.commands.get(commandName) || 
                     client.commands.find(cmd => cmd.aliases && cmd.aliases.includes(commandName));
      
      if (!command) {
        return message.reply(`❌ | Không tìm thấy lệnh \`${commandName}\``);
      }
      
      const detailEmbed = new EmbedBuilder()
        .setColor(client.embedColor || '#0099ff')
        .setTitle(`<:cham:1391893252029808682> ${command.name}`)
        .setDescription(
          `${command.description || 'Không có mô tả'}\n\n` +
          `**Cách sử dụng:**\n` +
          `\`${client.prefix}${command.usage || command.name}\`\n\n` +
          `**Tên khác:**\n` +
          `${command.aliases ? command.aliases.map(alias => `\`${alias}\``).join(', ') : 'Không có'}`
        )
        .setThumbnail(client.user.displayAvatarURL({ dynamic: true }))
        .setTimestamp()
      
      return message.reply({ embeds: [detailEmbed] });
    }
    
    // Hiển thị danh sách tất cả lệnh
    const isAdmin = message.member.permissions.has(PermissionFlagsBits.Administrator);
    
    const helpEmbed = new EmbedBuilder()
      .setColor(client.embedColor || '#0099ff')
      .setTitle('COMMANDS DIRECTORY')
      .setDescription(`Prefix hiện tại: \`${client.prefix}\`\nSử dụng \`${client.prefix}help <tên_lệnh>\` để xem chi tiết về lệnh`)
      .setThumbnail(client.user.displayAvatarURL({ dynamic: true }))
      .setTimestamp();

    // Lệnh cơ bản (cho tất cả user)
    const basicCommands = [
      '`ping` - Kiểm tra độ trễ bot',
      '`help` - Hiển thị danh sách lệnh',
      '`profile` - Xem profile cá nhân\n\n',
    ];

    helpEmbed.addFields({
      name: '<:cham:1391893252029808682> Lệnh Cơ Bản',
      value: basicCommands.join('\n'),
      inline: false
    });

    // Lệnh profile (cho tất cả user)
    const profileCommands = [
      '`es` - Xem danh sách profile',
      '`es description` - Tạo profile mới',
      '`es editprofile` - Chỉnh sửa profile',
      '`es setimage` - Đặt ảnh cho profile',
      '`es repostprofiles` - Đăng lại profiles',
      '`es removeprf` - Xóa profile'
    ];

    helpEmbed.addFields({
      name: '<:cham:1391893252029808682> Lệnh Profile',
      value: profileCommands.join('\n'),
      inline: false
    });

    // Lệnh giveaway (cho staff)
    const giveawayCommands = [
      '`tga start` - Tạo giveaway mới',
      '`tga end` - Kết thúc giveaway sớm',
      '`tga reroll` - Roll lại giveaway đã kết thúc',
      '`tga help` - Hiển thị hướng dẫn giveaway',
    ];

    helpEmbed.addFields({
      name: '<:cham:1391893252029808682> Lệnh Giveaway',
      value: giveawayCommands.join('\n'),
      inline: false
    });

    // Lệnh admin (chỉ admin)
    if (isAdmin) {
      const adminCommands = [
        '`es: set` - Gán profile cho user',
        '`ban/unban` - Cấm/bỏ cấm user',
        '`kick` - Đuổi user',
        '`mute/unmute` - Tắt tiếng user',
        '`lock/unlock` - Khóa/mở khóa channel',
        '`purge` - Xóa tin nhắn hàng loạt',
        '`role/unrole` - Thêm/xóa role cho user',
      ];

      helpEmbed.addFields({
        name: '<:cham:1391893252029808682> Lệnh Admin',
        value: adminCommands.join('\n'),
        inline: false
      });
    }

    // Slash commands
    const slashCommands = [
      '`/giveaway`- Quản lý giveaway',
      '`/bill` - Hệ thống tính bill',
      '`/luong` - Quản lý lương',
      '`/bank` - Quản lý ngân hàng',
      '`/react` - Hệ thống react bill',
    ];

    helpEmbed.addFields({
      name: '<:cham:1391893252029808682> Slash Commands',
      value: slashCommands.join('\n'),
      inline: false
    });

    // Thông tin thêm
    helpEmbed.addFields({
      name: '<:cham:1391893252029808682> Lưu Ý',
      value: '• Một số lệnh yêu cầu quyền đặc biệt\n• Sử dụng `/` cho slash commands\n• Liên hệ admin nếu cần hỗ trợ',
      inline: false
    });

    // Thống kê bot
    const totalCommands = client.commands.size;
    const totalSlashCommands = client.slashCommands ? client.slashCommands.size : 0;
    
    helpEmbed.setFooter({ 
      text: `${totalCommands} prefix commands • ${totalSlashCommands} slash commands • Powered by Fiin HUB ` 
    });

    await message.reply({ embeds: [helpEmbed] });
  }
};
