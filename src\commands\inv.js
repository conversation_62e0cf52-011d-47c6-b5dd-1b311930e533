const { EmbedBuilder } = require('discord.js');
const purchaseDB = require('../data/purchaseDB');


const shopItems = [
  { id: '01', emoji: '<:fistdate:1395218976115789925>', name: '**Fist date**', price: 100000 },
  { id: '02', emoji: '<:eternia:1395218967681040527>', name: '**Eternia**', price: 200000 },
  { id: '03', emoji: '<:amora:1395218962815651924>', name: '**Amora**', price: 300000 },
  { id: '04', emoji: '<:serenity:1395218985401716867>', name: '**Serenity**', price: 400000 },
  { id: '05', emoji: '<:unity:1395218993220026418>', name: '**Unity**', price: 500000 },
];

module.exports = {
  name: 'inv',
  description: '<PERSON><PERSON><PERSON> tra vật phẩm bạn đang sở hữu',
  async execute(client, message, args) {
    const userId = message.author.id;
    const userInventory = await purchaseDB.getInventory(userId);
    // Lọc chỉ lấy item có quantity > 0
    const inventoryWithQuantity = userInventory.filter(item => item.total > 0);
    
    let desc = "";
    if (inventoryWithQuantity.length === 0) {
        desc = "Bạn chưa sở hữu vật phẩm nào!";
    } else {
        desc = inventoryWithQuantity.map((item, idx) => {
            const shopItem = shopItems.find(si => si.id === item.itemId);
            const itemDisplay = shopItem ? `${shopItem.emoji}   ${shopItem.name}` : `ID ${item.itemId}`;
            return `\`0${idx + 1}\`. ${itemDisplay} \`x${item.total}\``;
        }).join("\n\n");
    }
    
    const embed = new EmbedBuilder()
        .setTitle("INVENTORY")
        .setColor(client.embedColor || '#0099ff')
        .setFooter({ text: `${message.author.username} • Hôm nay lúc ${getCurrentTime()}` })
        .setDescription(desc);
    
    return message.reply({ embeds: [embed] });
  },
};

function getCurrentTime() {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
}