const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
    name: 'kick',
    description: 'Kick một thành viên khỏi server',
    async execute(client, message, args) {
        try {
            // <PERSON><PERSON><PERSON> tra quyền: Administrator hoặc role cụ thể
            const hasAdminPermission = message.member.permissions.has(PermissionFlagsBits.Administrator);
            const hasSpecialRole = message.member.roles.cache.has('1376500798896214108') ||
                                  message.member.roles.cache.has('1376884726232514620');

            if (!hasAdminPermission && !hasSpecialRole) {
                const permissionEmbed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
                    .setDescription('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!')
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [permissionEmbed] });
            }
            
            if (!args[0]) {
                const syntaxEmbed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
                    .setDescription('<:error:1383005371542798346> | Vui lòng tag thành viên hoặc nhập ID thành viên cần kick.')
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [syntaxEmbed] });
            }
            
            const member = message.mentions.members.first()
                || await message.guild.members.fetch(args[0]).catch(() => null);
                
            if (!member) {
                const notFoundEmbed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
                    .setDescription('<:error:1383005371542798346> | Vui lòng tag thành viên hoặc nhập ID thành viên cần kick.')
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [notFoundEmbed] });
            }
            
            if (!member.kickable) {
                const notKickableEmbed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
                    .setDescription('<:error:1383005371542798346> | Không thể kick thành viên này.')
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [notKickableEmbed] });
            }
            
            const reason = args.slice(1).join(' ') || `Bị kick bởi ${message.author.tag}`;
            
            await member.kick(reason);
            
            const successEmbed = new EmbedBuilder()
                .setAuthor({ name: 'ACTION SUCCESSFUL', iconURL: client.user.displayAvatarURL() })
                .setDescription(`<:done:1383009630581424250> | Đã kick ${member.user.tag} khỏi server.${reason ? ` Lý do: ${reason}` : ''}`)
                .setColor(client.embedColor || '#0099ff')
                .setTimestamp();
            return message.reply({ embeds: [successEmbed] });
            
        } catch (err) {
            console.error('Lỗi trong lệnh kick:', err);
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Không thể kick thành viên này.')
                .setColor(client.embedColor || '#0099ff')
                .setTimestamp();
            try {
                return message.reply({ embeds: [errorEmbed] });
            } catch (replyError) {
                console.error('Không thể gửi thông báo lỗi:', replyError);
            }
        }
    }
};