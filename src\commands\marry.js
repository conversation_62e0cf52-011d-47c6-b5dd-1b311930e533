const { Em<PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON>uilder, But<PERSON>B<PERSON>er, ButtonStyle } = require('discord.js');
const cashDB = require('../data/cashDB');
const purchaseDB = require('../data/purchaseDB');
const marriageDB = require('../data/marriageDB');
const lovePointsDB = require('../data/lovePointsDB'); // Thêm import

// Danh sách nhẫn cưới từ shop
const weddingRings = [
  { id: '01', emoji: '<:fistdate:1395218976115789925>', name: '**Fist date**', price: 100000 },
  { id: '02', emoji: '<:eternia:1395218967681040527>', name: '**Eternia**', price: 200000 },
  { id: '03', emoji: '<:amora:1395218962815651924>', name: '**Amora**', price: 300000 },
  { id: '04', emoji: '<:serenity:1395218985401716867>', name: '**Serenity**', price: 400000 },
  { id: '05', emoji: '<:unity:1395218993220026418>', name: '**Unity**', price: 500000 },
];

module.exports = {
  name: 'marry', 
  aliases: ['marry', 'mry'],
  async execute(client, message, args) {
    try {
      // Xử lý lệnh li hôn: hmry divorce hoặc hmarry divorce
      if (args.length > 0 && args[0].toLowerCase() === 'divorce') {
        // Kiểm tra xem người dùng đã kết hôn chưa
        const isMarried = await marriageDB.isMarried(message.author.id);
        if (!isMarried) {
          const notMarriedEmbed = new EmbedBuilder()
            .setAuthor({
              name: 'MARRIAGE',
              iconURL: client.user.displayAvatarURL()
            })
            .setDescription(`<a:emoji_01:1385189247589285899> | ${message.author}, bạn chưa kết hôn với ai cả, không thể li hôn!`)
            .setColor(client.embedColor || '#0099ff')
            .setTimestamp();
          return message.reply({ embeds: [notMarriedEmbed] });
        }

        // Lấy thông tin hôn nhân
        const marriage = await marriageDB.getMarriage(message.author.id);
        const partnerId = marriage.user1Id === message.author.id ? marriage.user2Id : marriage.user1Id;
        const partner = await client.users.fetch(partnerId).catch(() => null);
        const ring = weddingRings.find(r => r.id === marriage.ringId);

        // Tạo embed xác nhận li hôn
        const divorceConfirmEmbed = new EmbedBuilder()
          .setAuthor({
            name: 'DIVORCE',
            iconURL: client.user.displayAvatarURL()
          })
          .setDescription(
            `💔 **${message.author}, bạn có chắc chắn muốn li hôn với ${partner || 'người ấy'} không?**\n\n` +
            `Hai bạn đã bên nhau **${Math.floor((Date.now() - new Date(marriage.marriedAt).getTime()) / (1000 * 60 * 60 * 24))} ngày**. Mong rằng bạn sẽ suy nghĩ kỹ trước khi đưa ra quyết định.'` 
          )
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp()
          .setThumbnail(partner ? partner.displayAvatarURL() : null)
          .setFooter({ text: 'Bạn có 60 giây để quyết định!' });

        // Tạo các nút xác nhận
        const confirmButton = new ButtonBuilder()
          .setCustomId(`divorce_confirm_${message.author.id}`)
          .setLabel('Đồng ý li hôn')
          .setEmoji('<a:emoji_200:1379902510122139648>')
          .setStyle(ButtonStyle.Secondary);

        const cancelButton = new ButtonBuilder()
          .setCustomId(`divorce_cancel_${message.author.id}`)
          .setLabel('Hủy bỏ')
          .setEmoji('<a:emoji_lh:1379902510122139648>')
          .setStyle(ButtonStyle.Secondary);

        const row = new ActionRowBuilder()
          .addComponents(confirmButton, cancelButton);

        // Gửi tin nhắn với embed và buttons
        const divorceMessage = await message.reply({ 
          embeds: [divorceConfirmEmbed], 
          components: [row] 
        });

        // Tạo collector để xử lý button interactions
        const filter = (interaction) => {
          return interaction.user.id === message.author.id && 
                 (interaction.customId.startsWith('divorce_confirm_') || 
                  interaction.customId.startsWith('divorce_cancel_'));
        };

        const collector = divorceMessage.createMessageComponentCollector({ 
          filter, 
          time: 60000 
        });

        collector.on('collect', async (interaction) => {
          try {
            if (interaction.customId.startsWith('divorce_confirm_')) {
              // Kiểm tra lại trạng thái hôn nhân trước khi thực hiện
              const stillMarried = await marriageDB.isMarried(message.author.id);
              
              if (!stillMarried) {
                const errorEmbed = new EmbedBuilder()
                  .setDescription('<:error:1383005371542798346> | Bạn đã không còn trong tình trạng kết hôn!')
                  .setColor(client.embedColor || '#0099ff');
                return await interaction.update({ embeds: [errorEmbed], components: [] });
              }

              // Thực hiện li hôn
              const divorceSuccess = await marriageDB.divorceMarriage(message.author.id);
              
              if (divorceSuccess) {
                // Reset điểm yêu thương của cả hai người về 0
                try {
                  await lovePointsDB.resetLovePoints(message.author.id);
                  await lovePointsDB.resetLovePoints(partnerId);
                } catch (error) {
                  console.error('Lỗi khi reset điểm yêu thương:', error);
                }
                
                // Xử lý thành công li hôn
                const successEmbed = new EmbedBuilder()
                  .setAuthor({
                    name: 'DIVORCE SUCCESSFUL',
                    iconURL: client.user.displayAvatarURL()
                  })
                  .setDescription(
                    `💔 **${message.author} đã li hôn với ${partner || 'Unknown User'}.**\n\n` +
                    `<:SAduck:1164647524825567232> Hy vọng cả hai sẽ tìm được hạnh phúc mới trong tương lai.`
                  )
                  .setColor(client.embedColor || '#0099ff')
                  .setTimestamp();
                
                await interaction.update({ 
                  embeds: [successEmbed], 
                  components: [] 
                });
              } else {
                const errorEmbed = new EmbedBuilder()
                  .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi thực hiện li hôn!')
                  .setColor(client.embedColor || '#0099ff');
                await interaction.update({ embeds: [errorEmbed], components: [] });
              }
              
            } else if (interaction.customId.startsWith('divorce_cancel_')) {
              // Xử lý hủy bỏ li hôn
              const cancelEmbed = new EmbedBuilder()
                .setAuthor({
                  name: 'DIVORCE CANCELATION',
                  iconURL: client.user.displayAvatarURL()
                })
                .setDescription(
                  `💕 **${message.author} đã quyết định không li hôn với ${partner || 'người ấy'}.**\n\n` +
                  `Tình yêu vẫn còn đó, hãy trân trọng nhau nhé!`
                )
                .setColor(client.embedColor || '#0099ff')
                .setTimestamp();

              await interaction.update({ 
                embeds: [cancelEmbed], 
                components: [] 
              });
            }
          } catch (error) {
            console.error('Error handling divorce interaction:', error);
            const errorEmbed = new EmbedBuilder()
              .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi xử lý li hôn!')
              .setColor(client.embedColor || '#0099ff');
            await interaction.update({ embeds: [errorEmbed], components: [] });
          }
        });

        collector.on('end', async (collected) => {
          if (collected.size === 0) {
            // Hết thời gian
            const timeoutEmbed = new EmbedBuilder()
              .setAuthor({
                name: 'DIVORCE TIMEOUT',
                iconURL: client.user.displayAvatarURL()
              })
              .setDescription(
                `Yêu cầu li hôn của ${message.author} đã hết thời gian.`
              )
              .setColor(client.embedColor || '#0099ff')
              .setTimestamp();

            await divorceMessage.edit({ 
              embeds: [timeoutEmbed], 
              components: [] 
            });
          }
        });

        return; // Kết thúc xử lý divorce
      }

      // Xử lý lệnh mry luv
      if (args.length > 0 && args[0].toLowerCase() === 'luv') {
        // Kiểm tra xem người dùng đã kết hôn chưa
        const isMarried = await marriageDB.isMarried(message.author.id);
        if (!isMarried) {
          const notMarriedEmbed = new EmbedBuilder()
            .setAuthor({
              name: 'MARRIAGE',
              iconURL: client.user.displayAvatarURL()
            })
            .setDescription(`<a:emoji_01:1385189247589285899> | ${message.author}, bạn chưa kết hôn với ai cả. Chúc bạn sớm tìm được "người ấy" nhé.`)
            .setColor(client.embedColor || '#0099ff')
            .setTimestamp();
          return message.reply({ embeds: [notMarriedEmbed] });
        }

        // Kiểm tra cooldown
        const canUse = await lovePointsDB.canUseLuv(message.author.id);
        if (!canUse) {
          const remainingTime = await lovePointsDB.getCooldownTime(message.author.id);
          const minutes = Math.ceil(remainingTime / (1000 * 60));
          
          const cooldownEmbed = new EmbedBuilder()
            .setAuthor({
              name: 'MARRIAGE',
              iconURL: client.user.displayAvatarURL()
            })
            .setDescription(`| ${message.author}, bạn cần chờ thêm **${minutes} phút** nữa mới có thể sử dụng lệnh luv!`)
            .setColor(client.embedColor || '#0099ff')
            .setTimestamp();
          return message.reply({ embeds: [cooldownEmbed] });
        }

        // Thêm điểm yêu thương
        await lovePointsDB.addLovePoint(message.author.id);
        
        // Lấy thông tin hôn nhân và điểm hiện tại
        const marriage = await marriageDB.getMarriage(message.author.id);
        const partnerId = marriage.user1Id === message.author.id ? marriage.user2Id : marriage.user1Id;
        const partner = await client.users.fetch(partnerId).catch(() => null);
        const loveData = await lovePointsDB.getLovePoints(message.author.id);
        
        // Tạo embed hiển thị tình yêu
        const luvEmbed = new EmbedBuilder()
          .setAuthor({
            name: 'MARRIAGE',
            iconURL: client.user.displayAvatarURL()
          })
          .setDescription(
            `💕 **${message.author} đang cảm thấy hạnh phúc cùng ${partner || 'người ấy'}**`
          )
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        
        return message.reply({ embeds: [luvEmbed] });
      }

      // Nếu không có args, hiển thị trạng thái hôn nhân
      if (args.length === 0) {
        const isMarried = await marriageDB.isMarried(message.author.id);
        
        if (!isMarried) {
          const embed = new EmbedBuilder()
            .setAuthor({
              name: 'MARRIAGE',
              iconURL: client.user.displayAvatarURL()
            })
            .setDescription(`<a:emoji_01:1385189247589285899> | ${message.author}, bạn chưa có cuộc hôn nhân nào cả, hãy tìm cho mình 1 người bạn đời để kết hôn nhé!`)
            .setColor(client.embedColor || '#0099ff')
            .setTimestamp();
          return message.reply({ embeds: [embed] });
        } else {
          const marriage = await marriageDB.getMarriage(message.author.id);
          const partnerId = marriage.user1Id === message.author.id ? marriage.user2Id : marriage.user1Id;
          const partner = await client.users.fetch(partnerId).catch(() => null);
          const ring = weddingRings.find(r => r.id === marriage.ringId);
          
          // Lấy điểm yêu thương
          const loveData = await lovePointsDB.getLovePoints(message.author.id);
          
          // Lấy điểm yêu thương của cả hai người
          const loveData1 = await lovePointsDB.getLovePoints(message.author.id);
          const loveData2 = await lovePointsDB.getLovePoints(partnerId);
          const totalLovePoints = loveData1.points + loveData2.points;

          //Embed hiển thị tình yêu
          let embedDescription = `Ngày đính hôn: **${new Date(marriage.marriedAt).toLocaleDateString('vi-VN')}**\n` +
              `Thời gian yêu nhau: **${(() => {
                const marriedDate = new Date(marriage.marriedAt);
                const now = new Date();
                const diffMs = now - marriedDate;
                const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
                const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
                
                return `${days} ngày, ${hours} giờ, ${minutes} phút`;
              })()})**\n` +
              `Nhẫn đính hôn: ${ring? ring.emoji + ' ' + ring.name : 'Không có nhẫn'}\n\n` +
              `Điểm yêu thương: **${totalLovePoints} điểm**`;
            
            // Thêm bio nếu có
            if (marriage.bioText) {
              embedDescription += `\n\n> ${marriage.bioText.replace(/\n/g, '\n> ')}`;
            }

          const embed = new EmbedBuilder()
            .setAuthor({
              name: `${message.author.displayName} đang hạnh phúc ở cạnh ${partner?.displayName || 'người ấy'}`,
              iconURL: client.user.displayAvatarURL()
            })
            .setThumbnail(marriage.thumbnailUrl || `https://cdn.discordapp.com/emojis/${ring.emoji.match(/<:(.*?):(\d+)>/)[2]}.png`)
            .setDescription(embedDescription)
            .setColor(client.embedColor || '#0099ff')
            .setTimestamp();
            
            if (marriage.imageUrl) {
              embed.setImage(marriage.imageUrl);
            }

          return message.reply({ embeds: [embed] });
        }
      }
      
      // Xử lý lệnh mry thumb
    if (args.length > 0 && args[0].toLowerCase() === 'thumb') {
      // Kiểm tra xem người dùng đã kết hôn chưa
      const isMarried = await marriageDB.isMarried(message.author.id);
      if (!isMarried) {
        const notMarriedEmbed = new EmbedBuilder()
          .setAuthor({
            name: 'MARRIAGE',
            iconURL: client.user.displayAvatarURL()
          })
          .setDescription(`<:SAduck:1164647524825567232> | ${message.author}, bạn chưa kết hôn với ai cả. Không thể set thumbnail!`)
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [notMarriedEmbed] });
      }
      
      // Kiểm tra xem có ảnh đính kèm không
      if (message.attachments.size === 0) {
        const noImageEmbed = new EmbedBuilder()
          .setAuthor({
            name: 'ACTION FAILED',
            iconURL: client.user.displayAvatarURL()
          })
          .setDescription(`<:error:1383005371542798346> | ${message.author}, vui lòng đính kèm một ảnh để set làm thumbnail!`)
          .setColor('#ff6b6b')
          .setTimestamp();
        return message.reply({ embeds: [noImageEmbed] });
      }
      
      // Lấy ảnh đầu tiên từ attachments
      const attachment = message.attachments.first();
      
      // Kiểm tra xem có phải là ảnh không
      if (!attachment.contentType || !attachment.contentType.startsWith('image/')) {
        const invalidImageEmbed = new EmbedBuilder()
          .setAuthor({
            name: 'ACTION FAILED',
            iconURL: client.user.displayAvatarURL()
          })
          .setDescription(`<:error:1383005371542798346> | ${message.author}, file đính kèm phải là một ảnh (jpg, png, gif, webp)!`)
          .setColor('#ff6b6b')
          .setTimestamp();
        return message.reply({ embeds: [invalidImageEmbed] });
      }
      
      try {
        // Cập nhật thumbnail URL vào database
        const updated = await marriageDB.updateThumbnail(message.author.id, attachment.url);
        
        if (updated) {
          const successEmbed = new EmbedBuilder()
            .setAuthor({
              name: 'ACTION SUCCESSFUL',
              iconURL: client.user.displayAvatarURL()
            })
            .setDescription(`<:done:1383009630581424250> | ${message.author}, đã cập nhật thumbnail thành công!`)
            .setColor(client.embedColor || '#0099ff')
            .setThumbnail(attachment.url)
            .setTimestamp();
          return message.reply({ embeds: [successEmbed] });
        } else {
          throw new Error('Không thể cập nhật thumbnail');
        }
      } catch (error) {
        console.error('Lỗi khi cập nhật thumbnail:', error);
        const errorEmbed = new EmbedBuilder()
          .setAuthor({
            name: 'ACTION FAILED',
            iconURL: client.user.displayAvatarURL()
          })
          .setDescription(`<:error:1383005371542798346> | ${message.author}, đã xảy ra lỗi khi cập nhật thumbnail!`)
          .setColor('#ff6b6b')
          .setTimestamp();
        return message.reply({ embeds: [errorEmbed] });
      }
    }

      // Xử lý lệnh mry image
      if (args.length > 0 && args[0].toLowerCase() === 'image') {
        // Kiểm tra xem người dùng đã kết hôn chưa
        const isMarried = await marriageDB.isMarried(message.author.id);
        if (!isMarried) {
          const notMarriedEmbed = new EmbedBuilder()
            .setAuthor({
              name: 'MARRIAGE',
              iconURL: client.user.displayAvatarURL()
            })
            .setDescription(`<:SAduck:1164647524825567232> | ${message.author}, bạn chưa kết hôn với ai cả. Không thể set image!`)
            .setColor(client.embedColor || '#0099ff')
            .setTimestamp();
          return message.reply({ embeds: [notMarriedEmbed] });
        }
        
        // Kiểm tra xem có ảnh đính kèm không
        if (message.attachments.size === 0) {
          const noImageEmbed = new EmbedBuilder()
            .setAuthor({
              name: 'ACTION FAILED',
              iconURL: client.user.displayAvatarURL()
            })
            .setDescription(`<:error:1383005371542798346> | ${message.author}, vui lòng đính kèm một ảnh để set làm image!`)
            .setColor('#ff6b6b')
            .setTimestamp();
          return message.reply({ embeds: [noImageEmbed] });
        }
        
        // Lấy ảnh đầu tiên từ attachments
        const attachment = message.attachments.first();
        
        // Kiểm tra xem có phải là ảnh không
        if (!attachment.contentType || !attachment.contentType.startsWith('image/')) {
          const invalidImageEmbed = new EmbedBuilder()
            .setAuthor({
              name: 'ACTION FAILED',
              iconURL: client.user.displayAvatarURL()
            })
            .setDescription(`<:error:1383005371542798346> | ${message.author}, file đính kèm phải là một ảnh (jpg, png, gif, webp)!`)
            .setColor('#ff6b6b')
            .setTimestamp();
          return message.reply({ embeds: [invalidImageEmbed] });
        }
        
        try {
          // Cập nhật image URL vào database
          const updated = await marriageDB.updateImage(message.author.id, attachment.url);
          
          if (updated) {
            const successEmbed = new EmbedBuilder()
              .setAuthor({
                name: 'MARRIAGE',
                iconURL: client.user.displayAvatarURL()
              })
              .setDescription(`<:done:1383009630581424250> | ${message.author}, đã cập nhật image thành công!`)
              .setColor(client.embedColor || '#0099ff')
              .setImage(attachment.url)
              .setTimestamp();
            return message.reply({ embeds: [successEmbed] });
          } else {
            throw new Error('Không thể cập nhật image');
          }
        } catch (error) {
          console.error('Lỗi khi cập nhật image:', error);
          const errorEmbed = new EmbedBuilder()
            .setAuthor({
              name: 'ACTION FAILED',
              iconURL: client.user.displayAvatarURL()
            })
            .setDescription(`<:error:1383005371542798346> | ${message.author}, đã xảy ra lỗi khi cập nhật image!`)
            .setColor(client.embedColor || '#0099ff')
            .setTimestamp();
          return message.reply({ embeds: [errorEmbed] });
        }
      }

      // Xử lý lệnh mry bio
      if (args.length > 0 && args[0].toLowerCase() === 'bio') {
        // Kiểm tra xem người dùng đã kết hôn chưa
        const isMarried = await marriageDB.isMarried(message.author.id);
        if (!isMarried) {
          const notMarriedEmbed = new EmbedBuilder()
            .setAuthor({
              name: 'MARRIAGE',
              iconURL: client.user.displayAvatarURL()
            })
            .setDescription(`<:SAduck:1164647524825567232> | ${message.author}, bạn chưa kết hôn với ai cả. Không thể set bio!`)
            .setColor(client.embedColor || '#0099ff')
            .setTimestamp();
          return message.reply({ embeds: [notMarriedEmbed] });
        }
        
        // Kiểm tra xem có nội dung bio không
        if (args.length < 2) {
          const noBioEmbed = new EmbedBuilder()
            .setAuthor({
              name: 'ACTION FAILED',
              iconURL: client.user.displayAvatarURL()
            })
            .setDescription(`<:error:1383005371542798346> | ${message.author}, vui lòng nhập nội dung bio!\n\n**Cú pháp:** \`hmry bio <nội dung>\``)
            .setColor('#ff6b6b')
            .setTimestamp();
          return message.reply({ embeds: [noBioEmbed] });
        }
        
        // Lấy nội dung bio từ args (bỏ qua args[0] là "bio")
        const bioContent = args.slice(1).join(' ');
        
        // Kiểm tra độ dài bio (tối đa 500 ký tự)
        if (bioContent.length > 500) {
          const tooLongEmbed = new EmbedBuilder()
            .setAuthor({
              name: 'ACTION FAILED',
              iconURL: client.user.displayAvatarURL()
            })
            .setDescription(`<:error:1383005371542798346> | ${message.author}, bio quá dài! Tối đa 500 ký tự (hiện tại: ${bioContent.length} ký tự).`)
            .setColor('#ff6b6b')
            .setTimestamp();
          return message.reply({ embeds: [tooLongEmbed] });
        }
        
        try {
          // Cập nhật bio vào database
          const updated = await marriageDB.updateBio(message.author.id, bioContent);
          
          if (updated) {
            const successEmbed = new EmbedBuilder()
              .setAuthor({
                name: 'MARRIAGE',
                iconURL: client.user.displayAvatarURL()
              })
              .setDescription(`<:done:1383009630581424250> | ${message.author}, đã cập nhật bio thành công!\n\n**Bio mới:**\n\`\`\`\n${bioContent}\n\`\`\``)
              .setColor(client.embedColor || '#0099ff')
              .setTimestamp();
            return message.reply({ embeds: [successEmbed] });
          } else {
            throw new Error('Không thể cập nhật bio');
          }
        } catch (error) {
          console.error('Lỗi khi cập nhật bio:', error);
          const errorEmbed = new EmbedBuilder()
            .setAuthor({
              name: 'ACTION FAILED',
              iconURL: client.user.displayAvatarURL()
            })
            .setDescription(`<:error:1383005371542798346> | ${message.author}, đã xảy ra lỗi khi cập nhật bio!`)
            .setColor('#ff6b6b')
            .setTimestamp();
          return message.reply({ embeds: [errorEmbed] });
        }
      }

      // Xử lý lệnh cầu hôn: hmry @user <id item>
      if (args.length < 2) {
        return message.reply('Cú pháp: `hmry @user <id nhẫn>` hoặc `hmry divorce` để li hôn');
      }

      // Kiểm tra xem người cầu hôn đã kết hôn chưa
      const proposerMarried = await marriageDB.isMarried(message.author.id);
      if (proposerMarried) {
        // Lấy thông tin về người bạn đời hiện tại
        const marriage = await marriageDB.getMarriage(message.author.id);
        const partnerId = marriage.user1Id === message.author.id ? marriage.user2Id : marriage.user1Id;
        const partner = await client.users.fetch(partnerId).catch(() => null);
        
        const alreadyMarriedEmbed = new EmbedBuilder()
          .setDescription(
            `<:SAduck:1164647524825567232> **Chắc hẵn tình cảm của bạn dành cho ${partner || 'người ấy'} đã không còn nhiều nữa !?**`
          )
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        
        return message.reply({ embeds: [alreadyMarriedEmbed] });
      }

      // Lấy user được mention
      const targetUser = message.mentions.users.first();
      if (!targetUser) {
        return message.reply('Bạn cần mention người muốn cầu hôn!');
      }

      if (targetUser.id === message.author.id) {
        return message.reply('Bạn không thể cầu hôn chính mình!');
      }

      if (targetUser.bot) {
        return message.reply('Bạn không thể cầu hôn bot!');
      }

      // Kiểm tra xem người được cầu hôn đã kết hôn chưa
      const targetMarried = await marriageDB.isMarried(targetUser.id);
      if (targetMarried) {
        // Lấy thông tin hôn nhân để biết partner
        const marriageInfo = await marriageDB.getMarriage(targetUser.id);
        const partnerId = marriageInfo.user1Id === targetUser.id ? marriageInfo.user2Id : marriageInfo.user1Id;

        const alreadyMarriedEmbed = new EmbedBuilder()
          .setAuthor({
            name: 'MARRIGED',
            iconURL: client.user.displayAvatarURL()
          })
          .setDescription(`<@${targetUser.id}> đang hạnh phúc trong vòng tay của <@${partnerId}>.\n\n Đừng buồn nhé, rồi bạn sẽ tìm được cho mình nữa còn lại.`)
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        
        return message.reply({ embeds: [alreadyMarriedEmbed] });
      }

      // Lấy ID nhẫn
      const ringId = args[1];
      if (!/^[0-9]{2}$/.test(ringId)) {
        return message.reply('Nhẫn không hợp lệ!');
      }

      // Tìm nhẫn trong danh sách
      const ring = weddingRings.find(r => r.id === ringId);
      if (!ring) {
        return message.reply('Không tìm thấy nhẫn bạn yêu cầu.');
      }

      // Kiểm tra xem user có nhẫn này trong inventory không
      const hasRing = await purchaseDB.hasItem(message.author.id, ringId, 1);
      if (!hasRing) {
        return message.reply(`Bạn không có nhẫn ${ring.emoji} ${ring.name} trong inventory. Hãy mua nhẫn trước khi cầu hôn!`);
      }

      // Tạo embed cầu hôn
      const proposalEmbed = new EmbedBuilder()
        .setAuthor({
          name: 'PROPOSAL',
          iconURL: client.user.displayAvatarURL()
        })
        .setDescription(
          `<a:_hearts_:1171742405104513084> **${message.author} đang cầu hôn ${targetUser}!**\n\n` +
          `${targetUser}, bạn có muốn nhận lời cầu hôn này không?`
        )
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp()
        .setThumbnail(targetUser.displayAvatarURL())
        .setFooter({ text: 'Bạn có 60 giây để quyết định!' });

      // Tạo buttons
      const acceptButton = new ButtonBuilder()
        .setCustomId(`marry_accept_${message.author.id}_${targetUser.id}_${ringId}`)
        .setLabel('Đồng ý')
        .setEmoji('<a:hihi:1379895656973336599>')
        .setStyle(ButtonStyle.Secondary);

      const rejectButton = new ButtonBuilder()
        .setCustomId(`marry_reject_${message.author.id}_${targetUser.id}`)
        .setLabel('Từ chối')
        .setEmoji('<a:emoji_197:1379895610940850187>')
        .setStyle(ButtonStyle.Secondary);

      const row = new ActionRowBuilder()
        .addComponents(acceptButton, rejectButton);

      // Gửi tin nhắn với embed và buttons
      const proposalMessage = await message.reply({ 
        embeds: [proposalEmbed], 
        components: [row] 
      });

      // Tạo collector để xử lý button interactions
      const filter = (interaction) => {
        return interaction.user.id === targetUser.id && 
               (interaction.customId.startsWith('marry_accept_') || 
                interaction.customId.startsWith('marry_reject_'));
      };

      const collector = proposalMessage.createMessageComponentCollector({ 
        filter, 
        time: 60000 
      });

      collector.on('collect', async (interaction) => {
        try {
          if (interaction.customId.startsWith('marry_accept_')) {
            // Kiểm tra lại trạng thái hôn nhân trước khi thực hiện
            const proposerStillSingle = !(await marriageDB.isMarried(message.author.id));
            const targetStillSingle = !(await marriageDB.isMarried(targetUser.id));
            const stillHasRing = await purchaseDB.hasItem(message.author.id, ringId, 1);
            
            if (!proposerStillSingle) {
              const errorEmbed = new EmbedBuilder()
                .setDescription('<:error:1383005371542798346> | Người cầu hôn đã kết hôn với người khác!')
                .setColor(client.embedColor || '#0099ff');
              return await interaction.update({ embeds: [errorEmbed], components: [] });
            }
            
            if (!targetStillSingle) {
              const errorEmbed = new EmbedBuilder()
                .setDescription('<:error:1383005371542798346> | Bạn đã kết hôn với người khác!')
                .setColor(client.embedColor || '#0099ff');
              return await interaction.update({ embeds: [errorEmbed], components: [] });
            }
            
            if (!stillHasRing) {
              const errorEmbed = new EmbedBuilder()
                .setDescription('< Người cầu hôn không còn nhẫn này!')
                .setColor(client.embedColor || '#0099ff');
              return await interaction.update({ embeds: [errorEmbed], components: [] });
            }

            // Tạo hôn nhân trong database
            await marriageDB.createMarriage(message.author.id, targetUser.id, ringId);
            
            // Trừ nhẫn khỏi inventory của người cầu hôn
            await purchaseDB.subtractItem(message.author.id, ringId, 1);
            
            // Xử lý chấp nhận cầu hôn
            const successEmbed = new EmbedBuilder()
              .setAuthor({
                name: 'MARRIAGE SUCCESS',
                iconURL: client.user.displayAvatarURL()
              })
              .setThumbnail(`https://cdn.discordapp.com/emojis/${ring.emoji.match(/<:(.*?):(\d+)>/)[2]}.png`)
              .setDescription(
                `<a:_hearts_:1171742405104513084> **${targetUser} đã đồng ý lời cầu hôn của ${message.author}!**\n\n` +
                `<:bunny_bouquet:1379409770246569984> Xin chúc mừng, ${message.author} và ${targetUser} đã trở thành cặp đôi. Hy vọng các bạn sẽ luôn thấy hạnh phúc khi bên cạnh nhau.`
              )
              .setColor(client.embedColor || '#0099ff')
              .setTimestamp();

            await interaction.update({ 
              embeds: [successEmbed], 
              components: [] 
            });
            
          } else if (interaction.customId.startsWith('marry_reject_')) {
            // Xử lý từ chối cầu hôn
            const rejectEmbed = new EmbedBuilder()
              .setAuthor({
                name: 'PROPOSAL REJECTED',
                iconURL: client.user.displayAvatarURL()
              })
              .setDescription(
                `<:SAduck:1164647524825567232> ${targetUser} đã từ chối lời cầu hôn của ${message.author}.\n\n` +
                `Đừng buồn, hy vọng bạn sẽ sớm tìm được nữa còn lại của bạn.`
              )
              .setColor(client.embedColor || '#0099ff')
              .setTimestamp();

            await interaction.update({ 
              embeds: [rejectEmbed], 
              components: [] 
            });
          }
        } catch (error) {
          console.error('Error handling marriage interaction:', error);
          const errorEmbed = new EmbedBuilder()
            .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi xử lý cầu hôn!')
            .setColor(client.embedColor || '#0099ff');
          await interaction.update({ embeds: [errorEmbed], components: [] });
        }
      });

      collector.on('end', async (collected) => {
        if (collected.size === 0) {
          // Hết thời gian
          const timeoutEmbed = new EmbedBuilder()
            .setAuthor({
              name: 'PROPOSAL TIMEOUT',
              iconURL: client.user.displayAvatarURL()
            })
            .setDescription(
              `Lời cầu hôn của ${message.author} đã hết thời gian.`
            )
            .setColor(client.embedColor || '#0099ff')
            .setTimestamp();

            try {
              await proposalMessage.edit({
                embeds: [timeoutEmbed],
                components: []
              });
            } catch (error) {
              if (error.code === 10008) {
                // Tin nhắn đã bị xóa, không cần làm gì
                console.log('Tin nhắn cầu hôn đã bị xóa, bỏ qua việc cập nhật.');
              } else {
                // Lỗi khác, log để debug
                console.error('Lỗi khi cập nhật tin nhắn cầu hôn:', error);
              }
            }
        }
      });

    } catch (error) {
      console.error('Error in marry command:', error);
      message.reply('Đã xảy ra lỗi khi xử lý lệnh cầu hôn.');
    }
  }
};
