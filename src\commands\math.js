const { EmbedBuilder } = require('discord.js');

module.exports = {
  name: 'math',
  description: '<PERSON><PERSON><PERSON> toán các phép toán cơ bản',
  usage: 'math <phép tính>\nVí dụ: math 5x5, math 10+20, math 100/4',
  aliases: ['calc', 'calculate'],

  async execute(client, message, args) {
    try {
      // Kiểm tra tham số
      if (args.length === 0) {
        const helpEmbed = new EmbedBuilder()
          .setColor(client.embedColor || '#0099ff')
          .setAuthor({ name: 'MATH CALCULATOR', iconURL: client.user.displayAvatarURL() })
          .setDescription(
            '**Cách sử dụng:**\n' +
            '`math <phép tính>`\n\n' +
            '**Các phép toán hỗ trợ:**\n' +
            '• `+` - Cộng\n' +
            '• `-` - Trừ\n' +
            '• `*` hoặc `x` - Nhân\n' +
            '• `/` hoặc `:` - Chia\n' +
            '• `^` hoặc `**` - L<PERSON>y thừa\n' +
            '• `%` - <PERSON><PERSON> l<PERSON>y dư\n' +
            '• `sqrt()` - Căn bậc hai\n' +
            '• `()` - Ngoặc đơn\n\n' +
            '**Ví dụ:**\n' +
            '`math 5x5` → 25\n' +
            '`math 10+20-5` → 25\n' +
            '`math 100/4` → 25\n' +
            '`math 2^3` → 8\n' +
            '`math sqrt(16)` → 4\n' +
            '`math (10+5)*2` → 30'
          )
          .setTimestamp();

        return message.reply({ embeds: [helpEmbed] });
      }

      // Lấy biểu thức toán học
      let expression = args.join(' ').trim();

      // Chuẩn hóa biểu thức
      expression = normalizeExpression(expression);

      // Kiểm tra biểu thức có hợp lệ không
      if (!isValidExpression(expression)) {
        const errorEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Biểu thức không hợp lệ! Chỉ được sử dụng số, phép toán cơ bản và ngoặc đơn.')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [errorEmbed] });
      }

      // Tính toán
      let result;
      try {
        result = evaluateExpression(expression);
      } catch (error) {
        const errorEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Không thể tính toán biểu thức này! Vui lòng kiểm tra lại cú pháp.')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [errorEmbed] });
      }

      // Kiểm tra kết quả
      if (!isFinite(result)) {
        const errorEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Kết quả không hợp lệ (vô cực hoặc không phải số)!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [errorEmbed] });
      }

      // Format kết quả
      const formattedResult = formatResult(result);

      // Reply với format đặc biệt thay vì embed
      const specialReply = `<:done:1383009630581424250> | Đáp án phép tính của bạn là **${formattedResult}**!`;
      await message.reply(specialReply);

    } catch (error) {
      console.error('Lỗi trong lệnh math:', error);
      const errorEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
        .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi thực hiện phép tính!')
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      return message.reply({ embeds: [errorEmbed] });
    }
  }
};

/**
 * Chuẩn hóa biểu thức toán học
 * @param {string} expr - Biểu thức gốc
 * @returns {string} - Biểu thức đã chuẩn hóa
 */
function normalizeExpression(expr) {
  return expr
    .toLowerCase()
    .replace(/\s+/g, '') // Xóa khoảng trắng
    .replace(/x/g, '*') // Thay x thành *
    .replace(/:/g, '/') // Thay : thành /
    .replace(/\^/g, '**') // Thay ^ thành **
    .replace(/sqrt\(/g, 'Math.sqrt(') // Thay sqrt thành Math.sqrt
    .replace(/sin\(/g, 'Math.sin(') // Hỗ trợ sin
    .replace(/cos\(/g, 'Math.cos(') // Hỗ trợ cos
    .replace(/tan\(/g, 'Math.tan(') // Hỗ trợ tan
    .replace(/log\(/g, 'Math.log(') // Hỗ trợ log
    .replace(/pi/g, 'Math.PI') // Hỗ trợ pi
    .replace(/e/g, 'Math.E'); // Hỗ trợ e
}

/**
 * Kiểm tra biểu thức có hợp lệ không
 * @param {string} expr - Biểu thức
 * @returns {boolean} - True nếu hợp lệ
 */
function isValidExpression(expr) {
  // Chỉ cho phép số, phép toán, ngoặc, Math functions
  const validPattern = /^[0-9+\-*/.()%\s^Math.sqrtsincotan logPIE]+$/;
  return validPattern.test(expr);
}

/**
 * Tính toán biểu thức một cách an toàn
 * @param {string} expr - Biểu thức
 * @returns {number} - Kết quả
 */
function evaluateExpression(expr) {
  // Sử dụng Function constructor thay vì eval để an toàn hơn
  try {
    const func = new Function('return ' + expr);
    return func();
  } catch (error) {
    throw new Error('Invalid expression');
  }
}

/**
 * Format kết quả hiển thị
 * @param {number} result - Kết quả
 * @returns {string} - Kết quả đã format
 */
function formatResult(result) {
  // Nếu là số nguyên, hiển thị không có thập phân
  if (Number.isInteger(result)) {
    return result.toLocaleString('vi-VN');
  }

  // Nếu là số thập phân, làm tròn đến 10 chữ số thập phân
  const rounded = Math.round(result * 10000000000) / 10000000000;

  // Nếu sau khi làm tròn thành số nguyên
  if (Number.isInteger(rounded)) {
    return rounded.toLocaleString('vi-VN');
  }

  return rounded.toLocaleString('vi-VN');
}