const { PermissionFlagsBits, EmbedBuilder } = require('discord.js');

module.exports = {
    name: 'mute',
    description: 'Tắt tiếng hoặc chặn gửi tin nhắn một thành viên',
    usage: '?mute @user [thời gian, ví dụ: 10m]',
    async execute(client, message, args) {
        try {
            // Kiểm tra quyền: Administrator hoặc role cụ thể
            const hasAdminPermission = message.member.permissions.has(PermissionFlagsBits.Administrator);
            const hasSpecialRole = message.member.roles.cache.has('1376500798896214108') ||
                                  message.member.roles.cache.has('1376884726232514620');

            if (!hasAdminPermission && !hasSpecialRole) {
                const embed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED' })
                    .setDescription('<:error:1383005371542798346> <PERSON>ạn không có quyền sử dụng lệnh này!')
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [embed] });
            }
            
            const member = message.mentions.members.first() 
                || await message.guild.members.fetch(args[0]).catch(() => null);
            if (!member) {
                const embed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED' })
                    .setDescription('<:error:1383005371542798346> Vui lòng tag thành viên hoặc nhập ID thành viên cần mute.')
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [embed] });
            }
            
            // Lấy thời gian timeout (mặc định 10 phút nếu không nhập)
            let duration = 10 * 60 * 1000; // 10 phút
            if (args[1]) {
                const match = args[1].match(/^(\d+)([smhd])$/);
                if (match) {
                    const value = parseInt(match[1]);
                    const unit = match[2];
                    if (unit === 's') duration = value * 1000;
                    if (unit === 'm') duration = value * 60 * 1000;
                    if (unit === 'h') duration = value * 60 * 60 * 1000;
                    if (unit === 'd') duration = value * 24 * 60 * 60 * 1000;
                }
            }
            
            await member.timeout(duration, `Bị mute bởi ${message.author.tag}`);
            const embed = new EmbedBuilder()
                .setAuthor({ name: 'ACTION SUCCESSFUL' })
                .setDescription(`<:done:1383009630581424250> Đã mute ${member.user.tag} trong ${args[1] || '10m'}.`)
                .setColor(client.embedColor || '#0099ff')
                .setTimestamp();
            return message.reply({ embeds: [embed] });
            
        } catch (err) {
            const embed = new EmbedBuilder()
                .setAuthor({ name: 'ACTION FAILED' })
                .setDescription('<:error:1383005371542798346> Không thể mute thành viên này.')
                .setColor(client.embedColor || '#0099ff')
                .setTimestamp();
            return message.reply({ embeds: [embed] });
        }
    }
};