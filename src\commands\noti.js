const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const fs = require('fs');
const path = require('path');

// File để lưu trữ notifications
const NOTI_FILE = path.join(__dirname, '../data/notifications.json');

// Khởi tạo file nếu chưa tồn tại
if (!fs.existsSync(NOTI_FILE)) {
    const dir = path.dirname(NOTI_FILE);
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(NOTI_FILE, JSON.stringify([], null, 2));
}

// Map để lưu trữ các timers đang chạy
const activeTimers = new Map();

// Hàm đọc notifications
function loadNotifications() {
    try {
        const data = fs.readFileSync(NOTI_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Lỗi khi đọc notifications:', error);
        return [];
    }
}

// Hàm lưu notifications
function saveNotifications(notifications) {
    try {
        fs.writeFileSync(NOTI_FILE, JSON.stringify(notifications, null, 2));
        return true;
    } catch (error) {
        console.error('Lỗi khi lưu notifications:', error);
        return false;
    }
}

// Hàm tạo ID ngẫu nhiên
function generateId() {
    return Math.random().toString(36).substr(2, 9);
}

// Hàm chuyển đổi thời gian Việt Nam
function getVietnamTime() {
    return new Date().toLocaleString('en-US', { timeZone: 'Asia/Ho_Chi_Minh' });
}

// Hàm parse thời gian
function parseTime(timeStr) {
    // Format: HH:MM hoặc HH:MM:SS
    const timeRegex = /^(\d{1,2}):(\d{2})(?::(\d{2}))?$/;
    const match = timeStr.match(timeRegex);
    
    if (!match) return null;
    
    const hours = parseInt(match[1]);
    const minutes = parseInt(match[2]);
    const seconds = parseInt(match[3] || '0');
    
    if (hours > 23 || minutes > 59 || seconds > 59) return null;
    
    return { hours, minutes, seconds };
}

// Hàm tính thời gian đến lần thực hiện tiếp theo
function getNextExecutionTime(timeObj, isDaily = false) {
    const now = new Date();
    const vietnamNow = new Date(now.toLocaleString('en-US', { timeZone: 'Asia/Ho_Chi_Minh' }));
    
    const targetTime = new Date(vietnamNow);
    targetTime.setHours(timeObj.hours, timeObj.minutes, timeObj.seconds, 0);
    
    // Nếu thời gian đã qua trong ngày hôm nay, chuyển sang ngày mai
    if (targetTime <= vietnamNow) {
        if (isDaily) {
            targetTime.setDate(targetTime.getDate() + 1);
        } else {
            return null; // Thời gian đã qua, không thể tạo notification một lần
        }
    }
    
    return targetTime;
}

// Hàm gửi notification
async function sendNotification(client, notification) {
    try {
        const channel = client.channels.cache.get(notification.channelId);
        if (!channel) {
            console.log(`❌ Không tìm thấy channel ${notification.channelId} cho notification ${notification.id}`);
            return;
        }

        const embed = new EmbedBuilder()
            .setAuthor({ name: 'SCHEDULED NOTIFICATION', iconURL: client.user.displayAvatarURL() })
            .setTitle('🔔 Thông báo đã đến giờ!')
            .setDescription(notification.message)
            .setColor('#ffa500')
            .setTimestamp()
            .setFooter({ text: `ID: ${notification.id} | Tạo bởi: ${notification.createdByTag}` });

        await channel.send({ embeds: [embed] });
        console.log(`✅ Đã gửi notification ${notification.id} tại ${getVietnamTime()}`);

        // Nếu là notification hàng ngày, lên lịch cho lần tiếp theo
        if (notification.isDaily) {
            scheduleNotification(client, notification);
        } else {
            // Xóa notification một lần sau khi gửi
            removeNotification(notification.id);
        }
    } catch (error) {
        console.error(`❌ Lỗi khi gửi notification ${notification.id}:`, error);
    }
}

// Hàm lên lịch notification
function scheduleNotification(client, notification) {
    const timeObj = parseTime(notification.time);
    if (!timeObj) return;

    const nextExecution = getNextExecutionTime(timeObj, notification.isDaily);
    if (!nextExecution) return;

    const delay = nextExecution.getTime() - Date.now();
    
    // Hủy timer cũ nếu có
    if (activeTimers.has(notification.id)) {
        clearTimeout(activeTimers.get(notification.id));
    }

    // Tạo timer mới
    const timer = setTimeout(() => {
        sendNotification(client, notification);
        activeTimers.delete(notification.id);
    }, delay);

    activeTimers.set(notification.id, timer);
    
    console.log(`⏰ Đã lên lịch notification ${notification.id} cho ${nextExecution.toLocaleString('vi-VN', { timeZone: 'Asia/Ho_Chi_Minh' })}`);
}

// Hàm xóa notification
function removeNotification(notificationId) {
    const notifications = loadNotifications();
    const filteredNotifications = notifications.filter(n => n.id !== notificationId);
    saveNotifications(filteredNotifications);
    
    // Hủy timer nếu có
    if (activeTimers.has(notificationId)) {
        clearTimeout(activeTimers.get(notificationId));
        activeTimers.delete(notificationId);
    }
}

// Hàm khởi tạo lại các notifications khi bot restart
function initializeNotifications(client) {
    const notifications = loadNotifications();
    console.log(`🔄 Đang khởi tạo ${notifications.length} notifications...`);
    
    notifications.forEach(notification => {
        scheduleNotification(client, notification);
    });
    
    console.log(`✅ Đã khởi tạo ${notifications.length} notifications!`);
}

module.exports = {
    name: 'noti',
    description: 'Quản lý hệ thống thông báo theo giờ',
    initializeNotifications, // Export để sử dụng trong index.js
    
    async execute(client, message, args) {
        try {
            // Kiểm tra quyền - chỉ admin hoặc staff
            const hasPermission = message.member.permissions.has(PermissionFlagsBits.Administrator) || 
                                 message.member.roles.cache.has('1376884726232514620') || // Staff role
                                 message.member.roles.cache.has('1376500798896214108');   // Supporters role
            
            if (!hasPermission) {
                const permissionEmbed = new EmbedBuilder()
                    .setAuthor({ name: 'ACCESS DENIED', iconURL: client.user.displayAvatarURL() })
                    .setDescription('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!')
                    .setColor('#ff0000')
                    .setTimestamp();
                return message.reply({ embeds: [permissionEmbed] });
            }

            const subcommand = args[0]?.toLowerCase();
            
            if (!subcommand) {
                return this.showHelp(client, message);
            }

            switch (subcommand) {
                case 'add':
                case 'create':
                    return this.addNotification(client, message, args.slice(1));
                
                case 'list':
                case 'show':
                    return this.listNotifications(client, message);
                
                case 'remove':
                case 'delete':
                    return this.removeNotification(client, message, args.slice(1));
                
                case 'time':
                    return this.showCurrentTime(client, message);
                
                default:
                    return this.showHelp(client, message);
            }
        } catch (error) {
            console.error('Lỗi trong lệnh noti:', error);
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'ERROR', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi thực hiện lệnh!')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }
    },

    // Hiển thị help
    showHelp(client, message) {
        const helpEmbed = new EmbedBuilder()
            .setAuthor({ name: 'NOTIFICATION SYSTEM HELP', iconURL: client.user.displayAvatarURL() })
            .setTitle('🔔 Hệ thống Thông báo theo Giờ')
            .setDescription('**Các lệnh có sẵn:**')
            .addFields(
                { name: '⏰ Tạo thông báo', value: '`noti add <HH:MM> <tin nhắn>` - Thông báo một lần\n`noti add daily <HH:MM> <tin nhắn>` - Thông báo hàng ngày', inline: false },
                { name: '📋 Xem danh sách', value: '`noti list` - Xem tất cả thông báo', inline: false },
                { name: '🗑️ Xóa thông báo', value: '`noti remove <id>` - Xóa thông báo', inline: false },
                { name: '🕐 Xem giờ hiện tại', value: '`noti time` - Xem giờ Việt Nam hiện tại', inline: false }
            )
            .addFields(
                { name: '📝 Ví dụ:', value: '`noti add 14:30 Họp team hàng ngày`\n`noti add daily 09:00 Chào buổi sáng!`\n`noti add 23:59:30 Sắp hết ngày rồi!`', inline: false }
            )
            .setColor('#3498db')
            .setTimestamp()
            .setFooter({ text: 'Notification System v1.0 | Múi giờ: GMT+7 (Việt Nam)' });
        
        return message.reply({ embeds: [helpEmbed] });
    },

    // Thêm notification mới
    addNotification(client, message, args) {
        if (args.length < 2) {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'INVALID INPUT', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Vui lòng nhập đầy đủ thông tin!\n\n**Cách sử dụng:**\n`noti add <HH:MM> <tin nhắn>`\n`noti add daily <HH:MM> <tin nhắn>`')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }

        let isDaily = false;
        let timeStr, messageContent;

        // Kiểm tra xem có phải daily không
        if (args[0].toLowerCase() === 'daily') {
            isDaily = true;
            timeStr = args[1];
            messageContent = args.slice(2).join(' ');
        } else {
            timeStr = args[0];
            messageContent = args.slice(1).join(' ');
        }

        if (!messageContent) {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'INVALID INPUT', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Vui lòng nhập nội dung thông báo!')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }

        // Parse thời gian
        const timeObj = parseTime(timeStr);
        if (!timeObj) {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'INVALID TIME', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Định dạng thời gian không hợp lệ!\n\n**Định dạng đúng:** `HH:MM` hoặc `HH:MM:SS`\n**Ví dụ:** `14:30`, `09:00:00`')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }

        // Kiểm tra thời gian có hợp lệ không (cho notification một lần)
        if (!isDaily) {
            const nextExecution = getNextExecutionTime(timeObj, false);
            if (!nextExecution) {
                const errorEmbed = new EmbedBuilder()
                    .setAuthor({ name: 'TIME PASSED', iconURL: client.user.displayAvatarURL() })
                    .setDescription('<:error:1383005371542798346> | Thời gian này đã qua trong ngày hôm nay!\n\n💡 **Gợi ý:** Sử dụng `daily` để tạo thông báo hàng ngày.')
                    .setColor('#ffa500')
                    .setTimestamp();
                return message.reply({ embeds: [errorEmbed] });
            }
        }

        const notifications = loadNotifications();

        const newNotification = {
            id: generateId(),
            time: timeStr,
            message: messageContent,
            isDaily: isDaily,
            channelId: message.channel.id,
            channelName: message.channel.name,
            createdBy: message.author.id,
            createdByTag: message.author.tag,
            createdAt: Date.now()
        };

        notifications.push(newNotification);

        if (saveNotifications(notifications)) {
            // Lên lịch notification
            scheduleNotification(client, newNotification);

            const nextExecution = getNextExecutionTime(timeObj, isDaily);
            const typeText = isDaily ? '🔄 Hàng ngày' : '⏰ Một lần';

            const successEmbed = new EmbedBuilder()
                .setAuthor({ name: 'NOTIFICATION CREATED', iconURL: client.user.displayAvatarURL() })
                .setTitle('✅ Thông báo đã được tạo thành công!')
                .setDescription(
                    `**ID:** \`${newNotification.id}\`\n` +
                    `**Thời gian:** ${timeStr}\n` +
                    `**Loại:** ${typeText}\n` +
                    `**Nội dung:** ${messageContent}\n` +
                    `**Kênh:** ${message.channel}\n` +
                    `**Lần tiếp theo:** <t:${Math.floor(nextExecution.getTime() / 1000)}:F>`
                )
                .setColor('#00ff00')
                .setTimestamp()
                .setFooter({ text: `Tạo bởi: ${message.author.tag}` });

            return message.reply({ embeds: [successEmbed] });
        } else {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'ERROR', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Không thể lưu thông báo!')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }
    },

    // Xem danh sách notifications
    listNotifications(client, message) {
        const notifications = loadNotifications();

        if (notifications.length === 0) {
            const emptyEmbed = new EmbedBuilder()
                .setAuthor({ name: 'NOTIFICATION LIST', iconURL: client.user.displayAvatarURL() })
                .setTitle('📋 Danh sách Thông báo')
                .setDescription('📭 Không có thông báo nào!')
                .setColor('#ffa500')
                .setTimestamp();
            return message.reply({ embeds: [emptyEmbed] });
        }

        // Sắp xếp theo thời gian
        notifications.sort((a, b) => {
            const timeA = parseTime(a.time);
            const timeB = parseTime(b.time);
            return (timeA.hours * 3600 + timeA.minutes * 60 + timeA.seconds) -
                   (timeB.hours * 3600 + timeB.minutes * 60 + timeB.seconds);
        });

        const embed = new EmbedBuilder()
            .setAuthor({ name: 'NOTIFICATION LIST', iconURL: client.user.displayAvatarURL() })
            .setTitle('📋 Danh sách Thông báo')
            .setColor('#3498db')
            .setTimestamp()
            .setFooter({ text: `Tổng cộng: ${notifications.length} thông báo | Múi giờ: GMT+7` });

        let description = '';
        notifications.forEach(noti => {
            const typeIcon = noti.isDaily ? '🔄' : '⏰';
            const timeObj = parseTime(noti.time);
            const nextExecution = getNextExecutionTime(timeObj, noti.isDaily);

            description += `${typeIcon} **ID:** \`${noti.id}\` | **${noti.time}**\n`;
            description += `📝 ${noti.message}\n`;
            description += `📍 <#${noti.channelId}> • 👨‍💼 <@${noti.createdBy}>\n`;
            if (nextExecution) {
                description += `⏰ Tiếp theo: <t:${Math.floor(nextExecution.getTime() / 1000)}:R>\n`;
            }
            description += '\n';
        });

        embed.setDescription(description);
        return message.reply({ embeds: [embed] });
    },

    // Xóa notification
    removeNotification(client, message, args) {
        if (args.length === 0) {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'INVALID INPUT', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Vui lòng nhập ID thông báo!\n\n**Cách sử dụng:** `noti remove <id>`')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }

        const notificationId = args[0];
        const notifications = loadNotifications();
        const notificationIndex = notifications.findIndex(noti => noti.id === notificationId);

        if (notificationIndex === -1) {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'NOT FOUND', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Không tìm thấy thông báo với ID này!')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }

        const notification = notifications[notificationIndex];

        // Chỉ người tạo hoặc admin mới có thể xóa
        const canDelete = message.member.permissions.has(PermissionFlagsBits.Administrator) ||
                         notification.createdBy === message.author.id;

        if (!canDelete) {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'ACCESS DENIED', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Bạn chỉ có thể xóa thông báo do mình tạo!')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }

        // Xóa notification
        removeNotification(notificationId);

        const successEmbed = new EmbedBuilder()
            .setAuthor({ name: 'NOTIFICATION DELETED', iconURL: client.user.displayAvatarURL() })
            .setTitle('🗑️ Thông báo đã được xóa!')
            .setDescription(
                `**ID:** \`${notification.id}\`\n` +
                `**Thời gian:** ${notification.time}\n` +
                `**Nội dung:** ${notification.message}\n` +
                `**Xóa bởi:** ${message.author}`
            )
            .setColor('#ff0000')
            .setTimestamp();

        return message.reply({ embeds: [successEmbed] });
    },

    // Hiển thị thời gian hiện tại
    showCurrentTime(client, message) {
        const vietnamTime = getVietnamTime();
        const now = new Date(vietnamTime);

        const timeEmbed = new EmbedBuilder()
            .setAuthor({ name: 'CURRENT TIME', iconURL: client.user.displayAvatarURL() })
            .setTitle('🕐 Thời gian hiện tại')
            .setDescription(
                `**Múi giờ:** GMT+7 (Việt Nam - Hồ Chí Minh)\n\n` +
                `**Thời gian:** ${now.toLocaleString('vi-VN', {
                    timeZone: 'Asia/Ho_Chi_Minh',
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                })}\n\n` +
                `**Định dạng 24h:** ${now.toLocaleTimeString('en-GB', { timeZone: 'Asia/Ho_Chi_Minh' })}\n\n` +
                `**Timestamp:** <t:${Math.floor(now.getTime() / 1000)}:F>`
            )
            .setColor('#3498db')
            .setTimestamp()
            .setFooter({ text: 'Sử dụng định dạng HH:MM để tạo thông báo' });

        return message.reply({ embeds: [timeEmbed] });
    }
};
