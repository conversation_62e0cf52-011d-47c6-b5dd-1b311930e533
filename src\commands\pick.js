module.exports = {
    name: 'pick',
    description: 'Pick a random item from a list',
    aliases: ['choose', 'random'],
    execute(client, message, args) {
        if(!args.length) {
            return message.channel.send('Bạn đang phân vân điều gì? Hãy để tôi giúp bạn lựa chọn!');
        }

        const options = args.join(' ').split(',').map(option => option.trim()).filter(option => option.length > 0);

        if (options.length < 2) {
            return message.channel.send('H<PERSON>y cho tôi thêm 1 lựa chọn nữa. Tôi sẽ đưa ra lựa chọn giúp bạn!');
        }

        const randomIndex = Math.floor(Math.random() * options.length);
        const selectedOption = options[randomIndex];

        message.reply(`| Mình chọn **${selectedOption}** còn bạn?`);
    },
};