const { PermissionFlagsBits } = require('discord.js');

module.exports = {
    name: 'ping',
    description: 'Kiểm tra độ trễ của bot',
    async execute(client, message, args) {
        // Kiểm tra quyền: <PERSON><PERSON> vì kiểm tra role cụ thể, kiểm tra quyền Administrator
        if (!message.member.permissions.has(PermissionFlagsBits.Administrator)) {
            return message.reply('❌ | Bạn không có quyền sử dụng lệnh này!');
        }
        
        const sent = await message.reply('<PERSON><PERSON> tính toán ping...');
        const pingTime = sent.createdTimestamp - message.createdTimestamp;
        
        sent.edit(`Độ trễ của bot là ${pingTime}ms.\n\n Độ trễ API là ${Math.round(client.ws.ping)}ms`);
    },
};