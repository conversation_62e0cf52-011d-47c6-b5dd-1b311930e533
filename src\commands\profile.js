const { EmbedBuilder } = require('discord.js');
const playerHistoryDB = require('../data/playerHistoryDB');
const donateHistoryDB = require('../data/donateHistoryDB');
const userBookingHistoryDB = require('../data/userBookingHistoryDB');
const { formatNumber } = require('../utils/formatUtils');

module.exports = {
  name: 'profile',
  description: 'Hiển thị thông tin profile của người dùng',
  usage: 'profile [mention]',
  async execute(client, message, args) {
    try {
      // Kiểm tra xem message có phải là đối tượng Message thông thường không
      const isNormalMessage = message.content !== undefined;
      
      // Xác định người dùng cần kiểm tra
      let targetUser;
      
      if (isNormalMessage) {
        // X<PERSON> lý cho lệnh thông thường
        if (message.mentions.users && message.mentions.users.size > 0) {
          // Nếu có mention, lấy người dùng đầu tiên được mention
          targetUser = message.mentions.users.first();
        } else if (args.length > 0) {
          // Nếu có ID hoặc tên người dùng
          const query = args.join(' ').toLowerCase();
          
          // Tìm kiếm theo ID - Sử dụng client trực tiếp thay vì message.client
          targetUser = client.users.cache.get(query);
          
          // Nếu không tìm thấy theo ID, tìm theo tên - Sử dụng client trực tiếp
          if (!targetUser) {
            targetUser = client.users.cache.find(
              user => user.username.toLowerCase().includes(query) || 
                     (message.guild.members.cache.get(user.id)?.displayName.toLowerCase().includes(query))
            );
          }
        } else {
          // Nếu không có tham số, lấy người gửi tin nhắn
          targetUser = message.author;
        }
        
        // Nếu không tìm thấy người dùng
        if (!targetUser) {
          // Kiểm tra message.channel tồn tại trước khi gọi send
          if (message.channel && typeof message.channel.send === 'function') {
            return message.channel.send('❌ | Không tìm thấy người dùng này!');
          }
          return; // Thoát nếu không thể gửi tin nhắn
        }
      } else {
        // Xử lý cho trường hợp không phải lệnh thông thường
        targetUser = message.user || message.author;
      }
      
      // Lấy thông tin guild
      const guild = message.guild;
      if (!guild) {
        const response = '❌ | Lệnh này chỉ có thể sử dụng trong server!';
        // Kiểm tra message.channel tồn tại trước khi gọi send
        if (message.channel && typeof message.channel.send === 'function') {
          return message.channel.send(response);
        }
        return; // Thoát nếu không thể gửi tin nhắn
      }
      
      // Lấy thông tin member từ guild
      const member = guild.members.cache.get(targetUser.id);
      
      // Lấy tổng số giờ đã book từ userBookingHistoryDB
      const userBookingHistory = await userBookingHistoryDB.getUserBookingHistory(targetUser.id);
      const totalBookHours = userBookingHistory.totalHours || 0;
      const totalBookMoney = userBookingHistory.totalBookedMoney || 0; // PHẢI LẤY TRƯỜNG NÀY
      
      // Lấy lịch sử donate
      const donateHistory = await donateHistoryDB.getKhachDonateHistory(targetUser.id);
      
      // Lấy tổng donate cho player (nếu muốn hiển thị tổng donate cho một player cụ thể, cần truyền playerId)
      // Ví dụ: const totalDonated = await donateHistoryDB.getTotalDonated(targetUser.id, playerId);
      // Nếu chỉ muốn tổng donate của khách cho tất cả player, giữ nguyên như dưới:
      const totalDonate = donateHistory.reduce((sum, entry) => sum + (entry.amount > 0 ? entry.amount : 0), 0);
      
      // Tính tổng tiền (book + donate)
      const totalMoney = totalBookMoney + totalDonate;
      
      // Tạo embed
      const embed = new EmbedBuilder()
        .setColor(client.embedColor || '#0099ff') 
        .setAuthor({ 
          name: member ? member.displayName : targetUser.username,
          iconURL: targetUser.displayAvatarURL({ dynamic: true })
        })
        .setThumbnail(targetUser.displayAvatarURL({ dynamic: true }))
        .setDescription(
          `**Tổng giờ:** \`${totalBookHours}h (${formatNumber(totalBookMoney)} VND)\`\n\n` +
          `**Donate:** \`${formatNumber(totalDonate)} VND\`\n\n` +
          `**Tổng Tiền:** \`${formatNumber(totalBookMoney + totalDonate)} VND\`\n\n` 
        )
        .setFooter({ text: `Hôm nay lúc: ${new Date().toLocaleString('vi-VN')}` });
      
      // Kiểm tra message.channel tồn tại trước khi gửi embed
      if (message.channel && typeof message.channel.send === 'function') {
        return message.channel.send({ embeds: [embed] });
      }
      return; // Thoát nếu không thể gửi tin nhắn
      
    } catch (error) {
      console.error('Lỗi khi kiểm tra profile:', error);
      
      // Xử lý lỗi an toàn hơn
      try {
        if (message.channel && typeof message.channel.send === 'function') {
          return message.channel.send(`❌ | Đã xảy ra lỗi: ${error.message}`);
        }
      } catch (sendError) {
        console.error('Không thể gửi thông báo lỗi:', sendError);
      }
    }
  }
};