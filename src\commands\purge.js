const { PermissionFlagsBits, EmbedBuilder } = require('discord.js');

module.exports = {
    name: 'purge',
    description: '<PERSON><PERSON><PERSON> <PERSON>hiều tin nhắn cùng lúc',
    usage: 'spurge <số lượng> hoặc spurge <@user> <số lượng>',
    async execute(client, message, args) {
        try {
            // Kiểm tra chỉ thực hiện trong server và member tồn tại
            if (!message.guild || !message.member) {
                const embed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED' })
                    .setDescription('<:error:1383005371542798346> | Lệnh này chỉ dùng trong server.')
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [embed] });
            }

            // Kiểm tra quyền: Administrator hoặc role cụ thể
            const hasAdminPermission = message.member.permissions.has(PermissionFlagsBits.Administrator);
            const hasSpecialRole = message.member.roles.cache.has('1376500798896214108') ||
                                  message.member.roles.cache.has('1376884726232514620');

            if (!hasAdminPermission && !hasSpecialRole) {
                const embed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED' })
                    .setDescription('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!')
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [embed] });
            }
        

            if (!args.length) {
                const embed = new EmbedBuilder()
                        .setAuthor({ name: 'ACTION FAILED' })
                        .setDescription('<:error:1383005371542798346> | Vui lòng nhập số lượng tin nhắn cần xóa.')
                        .setColor(client.embedColor || '#0099ff')
                        .setTimestamp();
                    return message.reply({ embeds: [embed] });
            }

            let amount;
            let user = message.mentions.users.first();

            if (user) {
                amount = parseInt(args[1]);
                if (isNaN(amount) || amount < 1 || amount > 1000) {
                    const embed = new EmbedBuilder()
                            .setAuthor({ name: 'ACTION FAILED' })
                            .setDescription('<:error:1383005371542798346> | Số lượng phải từ 1 đến 1000.')
                            .setColor(client.embedColor || '#0099ff')
                            .setTimestamp();
                        return message.reply({ embeds: [embed] });
                }
                let deleted = 0;
                while (deleted < amount) {
                    const fetchAmount = Math.min(amount - deleted, 100);
                    const messages = await message.channel.messages.fetch({ limit: 100 });
                    const userMessages = messages.filter(m => m.author.id === user.id).first(fetchAmount);
                    if (userMessages.length === 0) break;
                    await message.channel.bulkDelete(userMessages, true);
                    deleted += userMessages.length;
                    if (userMessages.length < fetchAmount) break;
                }
                return;
            } else {
                amount = parseInt(args[0]);
                if (isNaN(amount) || amount < 1 || amount > 1000) {
                    const embed = new EmbedBuilder()
                            .setAuthor({ name: 'ACTION FAILED' })
                            .setDescription('<:error:1383005371542798346> Số lượng phải từ 1 đến 1000.')
                            .setColor(client.embedColor || '#0099ff')
                            .setTimestamp();
                        return message.reply({ embeds: [embed] });
                }
                let deleted = 0;
                while (deleted < amount) {
                    const fetchAmount = Math.min(amount - deleted, 100);
                    const deletedMessages = await message.channel.bulkDelete(fetchAmount, true);
                    if (deletedMessages.size === 0) break;
                    deleted += deletedMessages.size;
                    if (deletedMessages.size < fetchAmount) break;
                }
                return;
            }
        } catch (err) {
            const embed = new EmbedBuilder()
                .setAuthor({ name: 'ACTION FAILED' })
                .setDescription('<:error:1383005371542798346> Đã xảy ra lỗi khi xóa tin nhắn.')
                .setColor(client.embedColor || '#0099ff')
                .setTimestamp();
            return message.reply({ embeds: [embed] });
        }
    },
};