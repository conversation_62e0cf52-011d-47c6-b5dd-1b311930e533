module.exports = {
  name: 'random',
  aliases: ['rd', 'rand'],
  description: 'Tạo số ngẫu nhiên',
  usage: '[max]',
  execute(client, message, args) {
    // Mặc định max là 100 nếu không có tham số
    let max = 100;

    // Nếu có tham số, sử dụng tham số đó làm max
    if (args.length > 0) {
      const parsedMax = parseInt(args[0]);
      if (!isNaN(parsedMax) && parsedMax > 0) {
        max = parsedMax;
      } else {
        return message.reply('<PERSON>ui lòng nhập một số nguyên dương hợp lệ.');
      }
    }

    // Random số từ 0 đến max
    const randomNumber = Math.floor(Math.random() * (max + 1));
    message.reply(`🎲| Số ngẫu nhiên từ 0 đến ${max} là: **${randomNumber}**`);
  },
};