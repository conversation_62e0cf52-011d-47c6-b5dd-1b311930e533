const { <PERSON><PERSON>ow<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Embed<PERSON><PERSON><PERSON>, PermissionFlagsBits } = require('discord.js');

module.exports = {
  name: 'react',
  async execute(client, message, args) {
    
    // Kiểm tra quyền: Ad<PERSON> hoặc role cụ thể
    const hasAdminPermission = message.member.permissions.has(PermissionFlagsBits.Administrator);
    const hasSpecialRole = message.member.roles.cache.has('1376500798896214108') ||
                          message.member.roles.cache.has('1376884726232514620');
    
    if (!hasAdminPermission && !hasSpecialRole) {
      const embed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED' })
                    .setDescription('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!')
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [embed] });
    }
  

    // <PERSON><PERSON>y nội dung tin nhắn sau lệnh hreact
    const content = message.content.slice('hreact'.length).trim();  

    // Tạo các nút
    const row = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId('react_bill')
        .setLabel('React')
        .setStyle(ButtonStyle.Primary),
      new ButtonBuilder()
        .setCustomId('cancel_react')
        .setLabel('Cancel')
        .setStyle(ButtonStyle.Danger),
      new ButtonBuilder()
        .setCustomId('stop_react')
        .setLabel('Stop react')
        .setStyle(ButtonStyle.Secondary)
    );

    // Tạo embed
    const embed = new EmbedBuilder()
      .setAuthor({
            name: 'REACT BILL',
            iconURL: client.user.displayAvatarURL()
        })
      .setDescription('Danh sách React:')
      .setColor(client.embedColor || '#0099ff') 
      .setTimestamp()
      .setFooter({ text: `${message.author.tag}`, iconURL: message.author.displayAvatarURL() });

    // Gửi tin nhắn với embed, attachment và các nút
    await message.channel.send({ 
      content: content, 
      embeds: [embed], 
      components: [row] 
    });

    try {
      await message.delete();
      console.log(`Đã xóa tin nhắn lệnh hreact từ ${message.author.tag}`);
    } catch (error) {
      console.error('Lỗi khi xóa tin nhắn lệnh hreact:', error);
    }
    
    return;
  }
};
