const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
  name: 'rn',
  description: 'Thay đổi biệt danh của user trong server',
  usage: 'rename <@user> <nickname mới>\nrename <@user> reset - Xóa nickname',
  
  async execute(client, message, args) {
    try {
      // Kiểm tra quyền: Administrator hoặc role cụ thể
      const hasAdminPermission = message.member.permissions.has(PermissionFlagsBits.Administrator);
      const hasSpecialRole = message.member.roles.cache.has('1376500798896214108') || 
                            message.member.roles.cache.has('1376884726232514620');
      
      if (!hasAdminPermission && !hasSpecialRole) {
        const failEmbed = new EmbedBuilder()
                  .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
                  .setDescription('<:error:1383005371542798346> | <PERSON>ạn không có quyền sử dụng lệnh này!')
                  .setColor(client.embedColor || '#0099ff')
                  .setTimestamp();
                return message.reply({ embeds: [failEmbed] });
      }

      // Kiểm tra bot có quyền Manage Nicknames không
      if (!message.guild.members.me.permissions.has(PermissionFlagsBits.ManageNicknames)) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Bot không có quyền **Manage Nicknames** trong server này!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      // Kiểm tra tham số
      if (args.length < 2) {
        const embed = new EmbedBuilder()
          .setColor(client.embedColor || '#0099ff')
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription(
            'Cú pháp không đúng\n\n' +
            '**Cách sử dụng:**\n' +
            '`trn <@user> <nickname mới>` - Đặt nickname mới\n' +
            '`trn <@user> reset` - Xóa nickname\n\n' +
            '**Ví dụ:**\n' +
            '`trn @Player123 Tên Mới`\n' +
            '`trn @Player123 reset`'
          )
          .setTimestamp();
        
        return message.reply({ embeds: [embed] });
      }

      // Lấy user được mention
      const targetUser = message.mentions.users.first();
      if (!targetUser) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Vui lòng mention user cần đổi tên!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      // Lấy member từ user
      const targetMember = message.guild.members.cache.get(targetUser.id);
      if (!targetMember) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Không tìm thấy user này trong server!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      // Kiểm tra hierarchy - không thể rename user có role cao hơn
      if (targetMember.roles.highest.position >= message.member.roles.highest.position && !hasAdminPermission) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Bạn không thể đổi tên user có role cao hơn hoặc bằng bạn!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      // Kiểm tra không thể rename owner server
      if (targetMember.id === message.guild.ownerId) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Không thể đổi tên owner của server!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      // Kiểm tra không thể rename bot
      if (targetUser.bot) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Không thể đổi tên bot!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      // Lấy nickname mới từ args (bỏ qua mention)
      const newNickname = args.slice(1).join(' ');
      const oldNickname = targetMember.nickname || targetUser.username;

      // Xử lý lệnh reset
      if (newNickname.toLowerCase() === 'reset') {
        try {
          await targetMember.setNickname(null, `Renamed by ${message.author.tag}`);
          
          const successEmbed = new EmbedBuilder()
            .setColor(client.embedColor || '#0099ff')
            .setAuthor({ name: 'ACTION SUCCESSFUL', iconURL: client.user.displayAvatarURL() })
            .setDescription(
              `<:done:1383009630581424250> |Đã reset nickname của ${targetUser}`
            )
            .setThumbnail(targetUser.displayAvatarURL())
            .setTimestamp();

          return message.reply({ embeds: [successEmbed] });
        } catch (error) {
          console.error('Lỗi khi reset nickname:', error);
          const failEmbed = new EmbedBuilder()
            .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
            .setDescription('<:error:1383005371542798346> | Không thể reset nickname! Có thể do quyền hạn hoặc lỗi Discord.')
            .setColor(client.embedColor || '#0099ff')
            .setTimestamp();
          return message.reply({ embeds: [failEmbed] });
        }
      }

      // Kiểm tra độ dài nickname
      if (newNickname.length > 32) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Nickname không được dài quá 32 ký tự!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      if (newNickname.length < 1) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Nickname không được để trống!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      // Kiểm tra nickname không chứa ký tự đặc biệt có thể gây lỗi
      const invalidChars = /[@#:`]/;
      if (invalidChars.test(newNickname)) {
        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Nickname không được chứa các ký tự: @ # : `')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

      // Thực hiện đổi tên
      try {
        await targetMember.setNickname(newNickname, `Renamed by ${message.author.tag}`);
        
        const successEmbed = new EmbedBuilder()
          .setColor(client.embedColor || '#0099ff')
          .setAuthor({ name: 'ACTION SUCCESSFUL', iconURL: client.user.displayAvatarURL() })
          .setDescription(
            `<:done:1383009630581424250> | Đã thay đổi nickname của ${targetUser} thành ${newNickname}`
          )
          .setThumbnail(targetUser.displayAvatarURL())
          .setTimestamp();

        await message.reply({ embeds: [successEmbed] });

        // Gửi log vào channel log nếu có
        const logChannelId = '1341454146372374591'; // Channel log
        const logChannel = message.guild.channels.cache.get(logChannelId);
        
        if (logChannel) {
          const logEmbed = new EmbedBuilder()
            .setColor('#ffa500')
            .setTitle('📝 NICKNAME CHANGED')
            .setDescription(
              `**User:** ${targetUser} (${targetUser.tag})\n` +
              `**Old Nickname:** ${oldNickname}\n` +
              `**New Nickname:** ${newNickname}\n` +
              `**Changed by:** ${message.author} (${message.author.tag})\n` +
              `**Channel:** ${message.channel}`
            )
            .setThumbnail(targetUser.displayAvatarURL())
            .setTimestamp()
            .setFooter({ text: `User ID: ${targetUser.id}` });

          await logChannel.send({ embeds: [logEmbed] });
        }

      } catch (error) {
        console.error('Lỗi khi đổi nickname:', error);

        let errorDescription = '<:error:1383005371542798346> | Không thể đổi nickname!';

        if (error.code === 50013) {
          errorDescription += ' Bot không có đủ quyền để đổi tên user này.';
        } else if (error.code === 50035) {
          errorDescription += ' Nickname không hợp lệ.';
        } else {
          errorDescription += ' Có thể do quyền hạn hoặc lỗi Discord.';
        }

        const failEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription(errorDescription)
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [failEmbed] });
      }

    } catch (error) {
      console.error('Lỗi trong lệnh rename:', error);
      const failEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
        .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi thực hiện lệnh!')
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      return message.reply({ embeds: [failEmbed] });
    }
  }
};
