const { PermissionFlagsBits, EmbedBuilder } = require('discord.js');

module.exports = {
    name: 'role',
    description: 'Thêm role cho một thành viên',
    usage: 'hrole @user <role>',
    async execute(client, message, args) {
        try {
            // Kiểm tra quyền: Administrator hoặc role cụ thể
            const hasAdminPermission = message.member.permissions.has(PermissionFlagsBits.Administrator);
            const hasSpecialRole = message.member.roles.cache.has('1376500798896214108') ||
                                  message.member.roles.cache.has('1376884726232514620');

            if (!hasAdminPermission && !hasSpecialRole) {
                const embed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED' })
                    .setDescription('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!')
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [embed] });
            }
            
            // Kiểm tra xem có đủ tham số không
            if (!args[0] || !args[1]) {
                const embed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED' })
                    .setDescription('<:error:1383005371542798346> | Vui lòng tag thành viên và nhập tên role.')
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [embed] });
            }
            
            // Lấy thành viên được tag hoặc từ ID
            const member = message.mentions.members.first()
                || await message.guild.members.fetch(args[0]).catch(() => null);
            
            if (!member) {
                const embed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED' })
                    .setDescription('<:error:1383005371542798346> | Không tìm thấy thành viên này.')
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [embed] });
            }
            
            // Lấy tên role từ args (có thể có khoảng trắng)
            const roleName = args.slice(1).join(' ');
            
            // Tìm role theo tên hoặc ID
            const role = message.guild.roles.cache.find(r => 
                r.name.toLowerCase() === roleName.toLowerCase() || 
                r.id === roleName
            );
            
            if (!role) {
                const embed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED' })
                    .setDescription(`<:error:1383005371542798346> | Không tìm thấy role "${roleName}".`)
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [embed] });
            }
            
            // Kiểm tra xem thành viên đã có role này chưa
            if (member.roles.cache.has(role.id)) {
                const embed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED' })
                    .setDescription(`<:error:1383005371542798346> | ${member.user.tag} đã có role "${role.name}" rồi.`)
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [embed] });
            }
            
            // Kiểm tra xem bot có thể thêm role này không
            if (role.position >= message.guild.members.me.roles.highest.position) {
                const embed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED' })
                    .setDescription('<:error:1383005371542798346> | Không thể add role vì role này có vị trí cao hơn bot!')
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [embed] });
            }
            
            await member.roles.add(role);
            const embed = new EmbedBuilder()
                .setAuthor({ name: 'ACTION SUCCESSFUL' })
                .setDescription(`<:done:1383009630581424250> | Đã thêm role "${role.name}" cho ${member}.`)
                .setColor(client.embedColor || '#0099ff')
                .setTimestamp();
            return message.reply({ embeds: [embed] });
            
        } catch (err) {
            console.error(err);
            const embed = new EmbedBuilder()
                .setAuthor({ name: 'ACTION FAILED' })
                .setDescription('<:error:1383005371542798346> | Có lỗi xảy ra khi thêm role.')
                .setColor(client.embedColor || '#0099ff')
                .setTimestamp();
            return message.reply({ embeds: [embed] });
        }
    }
};