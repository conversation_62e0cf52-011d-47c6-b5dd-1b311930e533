const { EmbedBuilder, ActionRowBuilder, StringSelectMenuBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
  name: 'store',
  aliases: ['store'],
  description: 'Tạo menu shop Discord',
  usage: 'store',
  
  async execute(client, message, args) {
    // Ki<PERSON>m tra quyền admin
    if (!message.member.permissions.has(PermissionFlagsBits.Administrator)) {
      return message.reply('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!');
    }

    // ID kênh cần gửi
    const targetChannelId = '1378883408679407727';
    
    try {
      const targetChannel = await client.channels.fetch(targetChannelId);
      
      if (!targetChannel) {
        return message.reply('<:error:1383005371542798346> | Không tìm thấy kênh đích!');
      }

      // Tạo embed shop
      const shopEmbed = new EmbedBuilder()
        .setColor(client.embedColor || '#0099ff')
        .setTitle('𝐓𝐡𝐞 𝐈𝐧𝐭𝐞𝐫𝐬𝐭𝐞𝐥𝐥𝐚𝐫  Shop')
        .setDescription(
          '**Chào mừng đến với 𝐓𝐡𝐞 𝐈𝐧𝐭𝐞𝐫𝐬𝐭𝐞𝐥𝐥𝐚𝐫  Shop!**\n\n' +
          '**Hãy chọn sản phẩm bạn muốn mua ở bên dưới nhé.**\n\n' +
          '**Phương thức thanh toán:**\n' +
          '• Banking (Vietcombank, Techcombank)\n' +
          '• Momo\n' +
          '• Zelle\n' +
          '• Remitly\n\n' +
          '**Liên hệ:**\n' +
          '• Tạo <#1341447407933722635> để được hỗ trợ mua hàng.'
        )
        .setThumbnail(client.user.displayAvatarURL({ dynamic: true }))
        .setImage('https://cdn.discordapp.com/attachments/**********/shop-banner.png')
        .setFooter({ 
          text: '𝐓𝐡𝐞 𝐈𝐧𝐭𝐞𝐫𝐬𝐭𝐞𝐥𝐥𝐚𝐫  Shop',
          iconURL: client.user.displayAvatarURL()
        })
        .setTimestamp();

      // Tạo select menu
      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('shop_select')
        .setPlaceholder('Chọn sản phẩm để xem chi tiết...')
        .addOptions([
          {
            label: 'Nitro Boost',
            description: 'Nâng cấp tài khoản Discord của bạn',
            value: 'nitro',
            emoji: '<:Nicho:1392668118551232563>'
          },
          {
            label: 'Server Boosts',
            description: 'Boost server Discord',
            value: 'boost',
            emoji: '<a:Emoji047:1376865715818070108>'
          },
          {
            label: 'Spotify',
            description: 'Tài khoản Spotify Premium',
            value: 'spotify',
            emoji: '<:Spotify:1392669668334108683>'
          },
          {
            label: 'Netflix',
            description: 'Tài khoản Netflix Premium',
            value: 'netflix',
            emoji: '<:Netflix:1392669992658538576>'
          },
          {
            label: 'Youtube',
            description: 'Tài khoản Youtube Premium',
            value: 'youtube',
            emoji: '<:youtube:1392669664751910913>'
          },
          {
            label: 'Rings',
            description: 'Nhẫn marry 𝐓𝐡𝐞 𝐈𝐧𝐭𝐞𝐫𝐬𝐭𝐞𝐥𝐥𝐚𝐫',
            value: 'ring',
            emoji: '<:Emoji009:1382956926979018782>'
          },
          {
            label: 'Custom Bot',
            description: 'Cho thuê và code bot Discord theo yêu cầu.',
            value: 'bot',
            emoji: '<:bot:1392670240797622282>'
          }
        ]);

      const row = new ActionRowBuilder().addComponents(selectMenu);

      // Gửi embed và menu đến kênh đích
      await targetChannel.send({
        embeds: [shopEmbed],
        components: [row]
      });

      // Xác nhận với admin
      await message.reply(`<:done:1383009630581424250> | Đã gửi menu shop đến <#${targetChannelId}> thành công!`);
      
      console.log(`🛒 ${message.author.tag} đã tạo menu shop tại kênh ${targetChannel.name}`);

    } catch (error) {
      console.error('Lỗi khi tạo shop menu:', error);
      return message.reply('<:error:1383005371542798346> | Có lỗi xảy ra khi tạo menu shop!');
    }
  }
};
