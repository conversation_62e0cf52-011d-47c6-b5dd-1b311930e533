const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
    name: 'sn',
    description: '<PERSON>ện lại tin nhắn bị xóa gần nhất trong kênh',
    usage: '?sn',
    async execute(client, message, args) {
        // Kiểm tra quyền: Administrator hoặc role cụ thể
        const hasAdminPermission = message.member.permissions.has(PermissionFlagsBits.Administrator);
        const hasSpecialRole = message.member.roles.cache.has('1376500798896214108') ||
                              message.member.roles.cache.has('1376884726232514620');

        if (!hasAdminPermission && !hasSpecialRole) {
            const embed = new EmbedBuilder()
            .setAuthor({ name: 'ACTION FAILED' })
            .setDescription('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!')
            .setColor(client.embedColor || '#0099ff')
            .setTimestamp();
            return message.reply({ embeds: [embed] });
        }
        
        const snipes = client.snipes.get(message.channel.id);
        if (!snipes) {
            const embed = new EmbedBuilder()
            .setAuthor({ name: 'ACTION FAILED' })
            .setDescription('<:error:1383005371542798346> | Không tìm thấy tin nhắn nào bị xóa gần đây.')
            .setColor(client.embedColor || '#0099ff')
            .setTimestamp();
            return message.reply({ embeds: [embed] });  
        }

        const snipe = message.client.snipes.get(message.channel.id);
        if (!snipe) {
            const embed = new EmbedBuilder()
            .setAuthor({ name: 'ACTION FAILED' })
            .setDescription('<:error:1383005371542798346> | Không tìm thấy tin nhắn nào bị xóa gần đây.')
            .setColor(client.embedColor || '#0099ff')
            .setTimestamp();
            return message.reply({ embeds: [embed] });  
        }

        const embed = new EmbedBuilder()
            .setAuthor({ name: 'MESSAGE SNIPED', iconURL: message.client.user.displayAvatarURL() })
            .setDescription(
                `**Người gửi:** ${snipe.member ? snipe.member.displayName : snipe.author.tag}\n\n` +
                `**Tin nhắn:** ${snipe.content || '*Không có nội dung*'}`
            )
            .setFooter({ text: `User ID: ${snipe.id} • Hôm nay lúc ${snipe.time.getHours().toString().padStart(2, '0')}:${snipe.time.getMinutes().toString().padStart(2, '0')}` })
            .setColor(client.embedColor || '#0099ff');

        await message.reply({ embeds: [embed] });
    }
};