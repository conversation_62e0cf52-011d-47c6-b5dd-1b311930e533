const { EmbedBuilder } = require('discord.js');
const topStarDB = require('../data/topStarDB');

module.exports = {
  name: 'star',
  description: 'Xem xếp hạng của bạn trong danh sách player đ<PERSON><PERSON>c book nhiều nhất',
  usage: 'star [@user]',
  
  async execute(client, message, args) {
    try {
      const hasRequiredRole = message.member.roles.cache.has('1376905574519799900');

      if (!hasRequiredRole) {
        const embed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [embed] });
      }

      // <PERSON><PERSON><PERSON> đ<PERSON>nh user cần check (nế<PERSON> c<PERSON> mention thì dùng user đượ<PERSON> mentioned, không thì dùng author)
      let targetUser = message.author;
      if (args.length > 0 && message.mentions.users.size > 0) {
        targetUser = message.mentions.users.first();
      }

      // Lấy tất cả players từ database và sắp xếp theo totalHours
      const allPlayers = await topStarDB.getTopStarPlayers(1000); // Lấy tối đa 1000 để tìm rank
      
      if (allPlayers.length === 0) {
        const embed = new EmbedBuilder()
          .setAuthor({ name: 'STAR RANKING', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Hiện chưa có player nào được book trong server.')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp()
          .setFooter({ text: `Yêu cầu bởi: ${message.author.tag}` });
        
        return message.reply({ embeds: [embed] });
      }

      // Tìm vị trí của user trong danh sách
      const userIndex = allPlayers.findIndex(player => player.playerId === targetUser.id);
      
      if (userIndex === -1) {
        // User không có trong danh sách
        const embed = new EmbedBuilder()
          .setAuthor({
            name: 'STAR',
            iconURL: client.user.displayAvatarURL()
          })
          .setDescription(
            '<a:tstar:1395226533039177808> **Chưa có xếp hạng** \n' +
            `${targetUser}\n\n` +
            `<a:emoji_99:1378452250321752095> **Số Giờ** \n` +
            `0h`
          )
          .setColor(client.embedColor || '#0099ff')
          .setThumbnail(targetUser.displayAvatarURL({ dynamic: true, size: 128 }))
          .setTimestamp()
          .setFooter({
            text: `${targetUser.username}`,
            iconURL: targetUser.displayAvatarURL({ dynamic: true })
          });

        return message.reply({ embeds: [embed] });
      }

      // User có trong danh sách
      const userRank = userIndex + 1;
      const userData = allPlayers[userIndex];

      // Xác định emoji rank và text
      let rankText = '';
      let rankEmoji = '';

      if (userRank === 1) {
        rankEmoji = '🥇';
        rankText = 'Top 1';
      } else if (userRank === 2) {
        rankEmoji = '🥈';
        rankText = 'Top 2';
      } else if (userRank === 3) {
        rankEmoji = '🥉';
        rankText = 'Top 3';
      } else {
        rankEmoji = '🏆';
        rankText = `Top ${userRank}`;
      }

      const embed = new EmbedBuilder()
        .setAuthor({
          name: 'STAR',
          iconURL: client.user.displayAvatarURL()
        })
        .setDescription(
          `<a:tstar:1395226533039177808> **Star ${rankText}\n**` +
          `${targetUser}\n\n` +
          `<a:emoji_99:1378452250321752095> **Số Giờ** \n` +
          `${userData.totalHours}h`
        )
        .setColor(client.embedColor || '#0099ff')
        .setThumbnail(targetUser.displayAvatarURL({ dynamic: true, size: 128 }))
        .setTimestamp()
        .setFooter({
          text: `${targetUser.username}`,
          iconURL: targetUser.displayAvatarURL({ dynamic: true })
        });

      return message.reply({ embeds: [embed] });

    } catch (error) {
      console.error('Lỗi trong lệnh star:', error);
      
      const errorEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ERROR', iconURL: client.user.displayAvatarURL() })
        .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi thực hiện lệnh!')
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      
      return message.reply({ embeds: [errorEmbed] });
    }
  }
};
