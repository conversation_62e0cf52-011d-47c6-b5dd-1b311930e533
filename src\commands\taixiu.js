module.exports = {
    name: 'tx',
    description: 'Lắc tài xỉu (3 viên xúc xắc)',
    usage: '?tx [tài/xỉu]',
    async execute(client, message, args) {
        const bet = (args[0] || '').toLowerCase();
        if (bet !== 'tài' && bet !== 'xỉu') {
            return message.reply('<PERSON>ui lòng đặt cược "tài" hoặc "xỉu". Ví dụ: !taixiu tài');
        }

        // Gửi hiệu ứng lắc xúc xắc ban đầu
        const rollingMsg = await message.reply('🎲 Đang lắc xúc xắc...');

        // Số lần cập nhật hiệu ứng lắc
        const shakeTimes = 6;
        let shakeCount = 0;
        const shakeInterval = setInterval(async () => {
            // Random 3 viên xúc xắc
            const fakeDice = [
                Math.floor(Math.random() * 6) + 1,
                Math.floor(Math.random() * 6) + 1,
                Math.floor(Math.random() * 6) + 1
            ];
            await rollingMsg.edit(`🎲 Đang lắc: [${fakeDice.join(' | ')}]`);
            shakeCount++;
            if (shakeCount >= shakeTimes) {
                clearInterval(shakeInterval);
                // Lắc thật
                const dice = [
                    Math.floor(Math.random() * 6) + 1,
                    Math.floor(Math.random() * 6) + 1,
                    Math.floor(Math.random() * 6) + 1
                ];
                const total = dice[0] + dice[1] + dice[2];
                const result = total >= 11 && total <= 17 ? 'tài' : 'xỉu';

                let msg = `🎲 Kết quả: [${dice.join(' + ')}] = **${total}**\n`;
                msg += `Kết quả là **${result.toUpperCase()}**!\n`;

                if (bet === result) {
                    msg += '🎉 Bạn đã **THẮNG**!';
                } else {
                    msg += '😢 Bạn đã **THUA**!';
                }
                await rollingMsg.edit(msg);
            }
        }, 200); // 350ms mỗi lần lắc, tổng khoảng 2 giây
    }
};