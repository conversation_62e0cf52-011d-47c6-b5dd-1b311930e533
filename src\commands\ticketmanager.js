const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const ticketAutoReply = require('../utils/ticketAutoReply');

module.exports = {
  name: 'ticketmanager',
  description: '<PERSON><PERSON><PERSON><PERSON> lý hệ thống ticket auto reply',
  usage: 'ticketmanager <stats|test>',
  aliases: ['tmanager', 'ticketmgr'],

  async execute(client, message, args) {
    try {
      // Kiểm tra quyền: Administrator hoặc role cụ thể
      const hasAdminPermission = message.member.permissions.has(PermissionFlagsBits.Administrator);
      const hasSpecialRole = message.member.roles.cache.has('1376500798896214108') ||
                            message.member.roles.cache.has('1376884726232514620');

      if (!hasAdminPermission && !hasSpecialRole) {
        return message.reply('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!');
      }

      // Hi<PERSON><PERSON> thị help nếu không có tham số
      if (args.length === 0) {
        const helpEmbed = new EmbedBuilder()
          .setColor(client.embedColor || '#0099ff')
          .setAuthor({ name: 'TICKET MANAGER HELP', iconURL: client.user.displayAvatarURL() })
          .setDescription(
            '**Hệ thống quản lý Ticket Auto Reply**\n\n' +
            '**Các lệnh có sẵn:**\n' +
            '`ticketmanager stats` - Xem thống kê tickets đang pending\n' +
            '`ticketmanager test` - Test hệ thống auto reply\n\n' +
            '**Tính năng:**\n' +
            '• Tự động reply sau 3 phút nếu không có staff phản hồi\n' +
            '• Theo dõi tất cả ticket threads\n' +
            '• Cung cấp nút hỗ trợ nhanh\n' +
            '• Thống kê và monitoring'
          )
          .setTimestamp();

        return message.reply({ embeds: [helpEmbed] });
      }

      const action = args[0].toLowerCase();

      switch (action) {
        case 'stats':
          await handleStats(client, message);
          break;
        case 'test':
          await handleTest(client, message);
          break;
        default:
          const errorEmbed = new EmbedBuilder()
            .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
            .setDescription('<:error:1383005371542798346> | Lệnh không hợp lệ! Sử dụng: `stats` hoặc `test`')
            .setColor(client.embedColor || '#0099ff')
            .setTimestamp();
          return message.reply({ embeds: [errorEmbed] });
      }

    } catch (error) {
      console.error('Lỗi trong lệnh ticketmanager:', error);
      const errorEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
        .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi thực hiện lệnh!')
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      return message.reply({ embeds: [errorEmbed] });
    }
  }
};

// Hiển thị thống kê tickets
async function handleStats(client, message) {
  try {
    const stats = ticketAutoReply.getStats();
    
    if (stats.pendingTickets === 0) {
      const noDataEmbed = new EmbedBuilder()
        .setAuthor({ name: 'TICKET STATS', iconURL: client.user.displayAvatarURL() })
        .setDescription('📭 | Hiện tại không có ticket nào đang chờ auto reply!')
        .setColor('#00ff00')
        .setTimestamp();
      return message.reply({ embeds: [noDataEmbed] });
    }

    let ticketList = '';
    for (let i = 0; i < stats.tickets.length; i++) {
      const ticket = stats.tickets[i];
      const createdTime = Math.floor(ticket.createdAt / 1000);
      const timeLeft = Math.max(0, 180 - Math.floor((Date.now() - ticket.createdAt) / 1000)); // 180s = 3 phút
      
      ticketList += `\`${(i + 1).toString().padStart(2, ' ')}.\` **${ticket.topic}**\n`;
      ticketList += `    📅 Tạo: <t:${createdTime}:R>\n`;
      ticketList += `    ⏰ Auto reply sau: ${timeLeft}s\n`;
      ticketList += `    🔗 <#${ticket.threadId}>\n\n`;
    }

    const statsEmbed = new EmbedBuilder()
      .setColor('#ffaa00')
      .setAuthor({
        name: 'TICKET AUTO REPLY STATS',
        iconURL: client.user.displayAvatarURL()
      })
      .setTitle('📊 Thống kê tickets đang chờ')
      .setDescription(ticketList)
      .addFields(
        { name: '🎫 Tổng tickets pending', value: `${stats.pendingTickets}`, inline: true },
        { name: '⏰ Thời gian auto reply', value: '3 phút', inline: true },
        { name: '🤖 Trạng thái hệ thống', value: '🟢 Hoạt động', inline: true }
      )
      .setTimestamp()
      .setFooter({
        text: `Thống kê bởi ${message.author.username}`,
        iconURL: message.author.displayAvatarURL()
      });

    return message.reply({ embeds: [statsEmbed] });

  } catch (error) {
    console.error('Lỗi khi lấy thống kê tickets:', error);
    const errorEmbed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
      .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi lấy thống kê!')
      .setColor(client.embedColor || '#0099ff')
      .setTimestamp();
    return message.reply({ embeds: [errorEmbed] });
  }
}

// Test hệ thống auto reply
async function handleTest(client, message) {
  try {
    const testEmbed = new EmbedBuilder()
      .setColor('#00ff00')
      .setAuthor({
        name: 'TICKET AUTO REPLY TEST',
        iconURL: client.user.displayAvatarURL()
      })
      .setTitle('🧪 Test hệ thống thành công!')
      .setDescription(
        '**Thông tin hệ thống:**\n' +
        '• ✅ Ticket Auto Reply System: Hoạt động\n' +
        '• ✅ Event Listeners: Đã đăng ký\n' +
        '• ✅ Client Connection: Kết nối thành công\n' +
        '• ✅ Staff Roles: Đã cấu hình\n\n' +
        '**Cách thức hoạt động:**\n' +
        '1. User tạo ticket → Bắt đầu tracking\n' +
        '2. Chờ 3 phút → Kiểm tra staff response\n' +
        '3. Không có staff → Gửi auto reply\n' +
        '4. Có staff response → Dừng tracking\n\n' +
        '**Staff Roles:**\n' +
        '• <@&1376884726232514620>\n' +
        '• <@&1376500798896214108>'
      )
      .addFields(
        { name: '⏰ Thời gian auto reply', value: '3 phút', inline: true },
        { name: '🎫 Tickets đang chờ', value: `${ticketAutoReply.getStats().pendingTickets}`, inline: true },
        { name: '🤖 Trạng thái', value: '🟢 Sẵn sàng', inline: true }
      )
      .setTimestamp()
      .setFooter({
        text: `Test bởi ${message.author.username}`,
        iconURL: message.author.displayAvatarURL()
      });

    return message.reply({ embeds: [testEmbed] });

  } catch (error) {
    console.error('Lỗi khi test hệ thống:', error);
    const errorEmbed = new EmbedBuilder()
      .setAuthor({ name: 'TEST FAILED', iconURL: client.user.displayAvatarURL() })
      .setDescription('<:error:1383005371542798346> | Test thất bại! Kiểm tra console để xem lỗi chi tiết.')
      .setColor('#ff0000')
      .setTimestamp();
    return message.reply({ embeds: [errorEmbed] });
  }
}
