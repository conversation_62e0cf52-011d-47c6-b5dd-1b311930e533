const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const fs = require('fs');
const path = require('path');

// File để lưu trữ todo list
const TODO_FILE = path.join(__dirname, '../data/todos.json');

// Khởi tạo file nếu chưa tồn tại
if (!fs.existsSync(TODO_FILE)) {
    const dir = path.dirname(TODO_FILE);
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(TODO_FILE, JSON.stringify([], null, 2));
}

// Hàm đọc todos
function loadTodos() {
    try {
        const data = fs.readFileSync(TODO_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Lỗi khi đọc todos:', error);
        return [];
    }
}

// Hàm lưu todos
function saveTodos(todos) {
    try {
        fs.writeFileSync(TODO_FILE, JSON.stringify(todos, null, 2));
        return true;
    } catch (error) {
        console.error('Lỗi khi lưu todos:', error);
        return false;
    }
}

// Hàm tạo ID ngẫu nhiên
function generateId() {
    return Math.random().toString(36).substr(2, 9);
}

module.exports = {
    name: 'todo',
    description: 'Quản lý todo list cho staff',
    async execute(client, message, args) {
        try {
            // Kiểm tra quyền - chỉ admin hoặc staff
            const hasPermission = message.member.permissions.has(PermissionFlagsBits.Administrator) || 
                                 message.member.roles.cache.has('1376884726232514620') || // Staff role
                                 message.member.roles.cache.has('1376500798896214108');   // Supporters role
            
            if (!hasPermission) {
                const permissionEmbed = new EmbedBuilder()
                    .setAuthor({ name: 'ACCESS DENIED', iconURL: client.user.displayAvatarURL() })
                    .setDescription('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!')
                    .setColor('#ff0000')
                    .setTimestamp();
                return message.reply({ embeds: [permissionEmbed] });
            }

            const subcommand = args[0]?.toLowerCase();
            
            if (!subcommand) {
                return this.showHelp(client, message);
            }

            switch (subcommand) {
                case 'add':
                case 'create':
                    return this.addTodo(client, message, args.slice(1));
                
                case 'list':
                case 'show':
                    return this.listTodos(client, message, args.slice(1));
                
                case 'done':
                case 'complete':
                    return this.completeTodo(client, message, args.slice(1));
                
                case 'remove':
                case 'delete':
                    return this.removeTodo(client, message, args.slice(1));
                
                case 'assign':
                    return this.assignTodo(client, message, args.slice(1));
                
                case 'edit':
                    return this.editTodo(client, message, args.slice(1));
                
                case 'priority':
                    return this.setPriority(client, message, args.slice(1));
                
                default:
                    return this.showHelp(client, message);
            }
        } catch (error) {
            console.error('Lỗi trong lệnh todo:', error);
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'ERROR', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi thực hiện lệnh!')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }
    },

    // Hiển thị help
    showHelp(client, message) {
        const helpEmbed = new EmbedBuilder()
            .setAuthor({ name: 'TODO SYSTEM HELP', iconURL: client.user.displayAvatarURL() })
            .setTitle('📝 Hệ thống Todo List cho Staff')
            .setDescription('**Các lệnh có sẵn:**')
            .addFields(
                { name: '📌 Tạo todo', value: '`todo add <nội dung>` - Tạo todo mới', inline: false },
                { name: '📋 Xem danh sách', value: '`todo list [all/pending/done]` - Xem todos', inline: false },
                { name: '✅ Hoàn thành', value: '`todo done <id>` - Đánh dấu hoàn thành', inline: false },
                { name: '🗑️ Xóa todo', value: '`todo remove <id>` - Xóa todo', inline: false },
                { name: '👤 Giao việc', value: '`todo assign <id> <@user>` - Giao việc cho ai đó', inline: false },
                { name: '✏️ Chỉnh sửa', value: '`todo edit <id> <nội dung mới>` - Sửa nội dung', inline: false },
                { name: '⚡ Độ ưu tiên', value: '`todo priority <id> <low/medium/high>` - Đặt độ ưu tiên', inline: false }
            )
            .setColor('#3498db')
            .setTimestamp()
            .setFooter({ text: 'Todo System v1.0' });
        
        return message.reply({ embeds: [helpEmbed] });
    },

    // Thêm todo mới
    addTodo(client, message, args) {
        if (args.length === 0) {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'INVALID INPUT', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Vui lòng nhập nội dung todo!\n\n**Cách sử dụng:** `todo add <nội dung>`')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }

        const content = args.join(' ');
        const todos = loadTodos();
        
        const newTodo = {
            id: generateId(),
            content: content,
            status: 'pending',
            priority: 'medium',
            createdBy: message.author.id,
            createdByTag: message.author.tag,
            assignedTo: null,
            assignedToTag: null,
            createdAt: Date.now(),
            completedAt: null,
            completedBy: null
        };

        todos.push(newTodo);
        
        if (saveTodos(todos)) {
            const successEmbed = new EmbedBuilder()
                .setAuthor({ name: 'TODO CREATED', iconURL: client.user.displayAvatarURL() })
                .setTitle('✅ Todo đã được tạo thành công!')
                .setDescription(`**ID:** \`${newTodo.id}\`\n**Nội dung:** ${content}\n**Trạng thái:** 🟡 Pending\n**Độ ưu tiên:** 🔵 Medium`)
                .setColor('#00ff00')
                .setTimestamp()
                .setFooter({ text: `Tạo bởi: ${message.author.tag}` });
            
            return message.reply({ embeds: [successEmbed] });
        } else {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'ERROR', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Không thể lưu todo!')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }
    },

    // Xem danh sách todos
    listTodos(client, message, args) {
        const todos = loadTodos();
        const filter = args[0]?.toLowerCase() || 'all';
        
        let filteredTodos = todos;
        let title = '📋 Tất cả Todo';
        
        switch (filter) {
            case 'pending':
                filteredTodos = todos.filter(todo => todo.status === 'pending');
                title = '🟡 Todo đang chờ';
                break;
            case 'done':
            case 'completed':
                filteredTodos = todos.filter(todo => todo.status === 'completed');
                title = '✅ Todo đã hoàn thành';
                break;
        }

        if (filteredTodos.length === 0) {
            const emptyEmbed = new EmbedBuilder()
                .setAuthor({ name: 'TODO LIST', iconURL: client.user.displayAvatarURL() })
                .setTitle(title)
                .setDescription('📭 Không có todo nào!')
                .setColor('#ffa500')
                .setTimestamp();
            return message.reply({ embeds: [emptyEmbed] });
        }

        // Sắp xếp theo độ ưu tiên và thời gian tạo
        const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
        filteredTodos.sort((a, b) => {
            if (a.status !== b.status) {
                return a.status === 'pending' ? -1 : 1;
            }
            if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
                return priorityOrder[b.priority] - priorityOrder[a.priority];
            }
            return b.createdAt - a.createdAt;
        });

        const embed = new EmbedBuilder()
            .setAuthor({ name: 'TODO LIST', iconURL: client.user.displayAvatarURL() })
            .setTitle(title)
            .setColor('#3498db')
            .setTimestamp()
            .setFooter({ text: `Tổng cộng: ${filteredTodos.length} todo` });

        // Chia thành các trang nếu quá nhiều
        const itemsPerPage = 10;
        const totalPages = Math.ceil(filteredTodos.length / itemsPerPage);
        const currentPage = 1; // Có thể mở rộng để hỗ trợ phân trang
        
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const currentTodos = filteredTodos.slice(startIndex, endIndex);

        let description = '';
        currentTodos.forEach(todo => {
            const statusIcon = todo.status === 'completed' ? '✅' : '🟡';
            const priorityIcon = todo.priority === 'high' ? '🔴' : todo.priority === 'medium' ? '🔵' : '🟢';
            const assignedText = todo.assignedTo ? `👤 <@${todo.assignedTo}>` : '👤 Chưa giao';
            
            description += `${statusIcon} **ID:** \`${todo.id}\` ${priorityIcon}\n`;
            description += `📝 ${todo.content}\n`;
            description += `${assignedText} • 👨‍💼 <@${todo.createdBy}>\n`;
            if (todo.status === 'completed' && todo.completedBy) {
                description += `✅ Hoàn thành bởi <@${todo.completedBy}> • <t:${Math.floor(todo.completedAt / 1000)}:R>\n`;
            }
            description += `⏰ <t:${Math.floor(todo.createdAt / 1000)}:R>\n\n`;
        });

        embed.setDescription(description);

        if (totalPages > 1) {
            embed.setFooter({ text: `Trang ${currentPage}/${totalPages} • Tổng cộng: ${filteredTodos.length} todo` });
        }

        return message.reply({ embeds: [embed] });
    },

    // Hoàn thành todo
    async completeTodo(client, message, args) {
        if (args.length === 0) {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'INVALID INPUT', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Vui lòng nhập ID todo!\n\n**Cách sử dụng:** `todo done <id>`')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }

        const todoId = args[0];
        const todos = loadTodos();
        const todoIndex = todos.findIndex(todo => todo.id === todoId);

        if (todoIndex === -1) {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'NOT FOUND', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Không tìm thấy todo với ID này!')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }

        const todo = todos[todoIndex];

        if (todo.status === 'completed') {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'ALREADY COMPLETED', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Todo này đã được hoàn thành rồi!')
                .setColor('#ffa500')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }

        // Cập nhật trạng thái
        todos[todoIndex].status = 'completed';
        todos[todoIndex].completedAt = Date.now();
        todos[todoIndex].completedBy = message.author.id;

        if (saveTodos(todos)) {
            // Gửi DM cho người tạo todo (nếu không phải người hoàn thành)
            if (todo.createdBy !== message.author.id) {
                try {
                    const creator = await message.guild.members.fetch(todo.createdBy);
                    if (creator) {
                        const dmEmbed = new EmbedBuilder()
                            .setAuthor({ name: 'TASK COMPLETED', iconURL: client.user.displayAvatarURL() })
                            .setTitle('✅ Task của bạn đã được hoàn thành!')
                            .setDescription(
                                `**ID Task:** \`${todo.id}\`\n\n` +
                                `**Nội dung:** ${todo.content}\n\n` +
                                `**Hoàn thành bởi:** ${message.author} (${message.author.tag})\n\n` +
                                `**Thời gian hoàn thành:** <t:${Math.floor(Date.now() / 1000)}:F>\n\n` +
                                `**Server:** ${message.guild.name}\n\n` +
                                `🎉 **Chúc mừng!** Task đã được hoàn thành thành công!`
                            )
                            .setColor('#00ff00')
                            .setTimestamp()
                            .setFooter({ text: `Task ID: ${todo.id}` });

                        await creator.user.send({ embeds: [dmEmbed] });
                        console.log(`✅ Đã gửi DM thông báo hoàn thành cho ${creator.user.tag}`);
                    }
                } catch (dmError) {
                    console.log(`❌ Không thể gửi DM cho người tạo task:`, dmError.message);
                }
            }

            const successEmbed = new EmbedBuilder()
                .setAuthor({ name: 'TODO COMPLETED', iconURL: client.user.displayAvatarURL() })
                .setTitle('✅ Todo đã được đánh dấu hoàn thành!')
                .setDescription(
                    `**ID:** \`${todo.id}\`\n` +
                    `**Nội dung:** ${todo.content}\n` +
                    `**Hoàn thành bởi:** ${message.author}\n` +
                    `**Thời gian:** <t:${Math.floor(Date.now() / 1000)}:F>\n\n` +
                    `📩 **Notification:** ${todo.createdBy !== message.author.id ? '✅ Đã thông báo người tạo' : '📝 Tự hoàn thành'}`
                )
                .setColor('#00ff00')
                .setTimestamp();

            return message.reply({ embeds: [successEmbed] });
        } else {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'ERROR', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Không thể cập nhật todo!')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }
    },

    // Xóa todo
    removeTodo(client, message, args) {
        if (args.length === 0) {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'INVALID INPUT', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Vui lòng nhập ID todo!\n\n**Cách sử dụng:** `todo remove <id>`')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }

        const todoId = args[0];
        const todos = loadTodos();
        const todoIndex = todos.findIndex(todo => todo.id === todoId);

        if (todoIndex === -1) {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'NOT FOUND', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Không tìm thấy todo với ID này!')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }

        const todo = todos[todoIndex];

        // Chỉ người tạo hoặc admin mới có thể xóa
        const canDelete = message.member.permissions.has(PermissionFlagsBits.Administrator) ||
                         todo.createdBy === message.author.id;

        if (!canDelete) {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'ACCESS DENIED', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Bạn chỉ có thể xóa todo do mình tạo!')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }

        // Xóa todo
        todos.splice(todoIndex, 1);

        if (saveTodos(todos)) {
            const successEmbed = new EmbedBuilder()
                .setAuthor({ name: 'TODO DELETED', iconURL: client.user.displayAvatarURL() })
                .setTitle('🗑️ Todo đã được xóa!')
                .setDescription(`**ID:** \`${todo.id}\`\n**Nội dung:** ${todo.content}\n**Xóa bởi:** ${message.author}`)
                .setColor('#ff0000')
                .setTimestamp();

            return message.reply({ embeds: [successEmbed] });
        } else {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'ERROR', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Không thể xóa todo!')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }
    },

    // Giao việc cho ai đó
    async assignTodo(client, message, args) {
        if (args.length < 2) {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'INVALID INPUT', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Vui lòng nhập đầy đủ thông tin!\n\n**Cách sử dụng:** `todo assign <id> <@user>`')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }

        const todoId = args[0];
        const userMention = args[1];
        const userId = userMention.replace(/[<@!>]/g, '');

        // Kiểm tra user có tồn tại không
        const targetUser = message.guild.members.cache.get(userId);
        if (!targetUser) {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'USER NOT FOUND', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Không tìm thấy user này!')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }

        const todos = loadTodos();
        const todoIndex = todos.findIndex(todo => todo.id === todoId);

        if (todoIndex === -1) {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'NOT FOUND', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Không tìm thấy todo với ID này!')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }

        // Cập nhật assignment
        todos[todoIndex].assignedTo = userId;
        todos[todoIndex].assignedToTag = targetUser.user.tag;

        if (saveTodos(todos)) {
            const todo = todos[todoIndex];
            const priorityIcon = todo.priority === 'high' ? '🔴' : todo.priority === 'medium' ? '🔵' : '🟢';
            const priorityText = todo.priority === 'high' ? 'Cao' : todo.priority === 'medium' ? 'Trung bình' : 'Thấp';

            // Gửi DM cho user được giao task
            let dmStatus = '❌ Không thể gửi';
            try {
                console.log(`🔄 Đang thử gửi DM cho ${targetUser.user.tag} (ID: ${targetUser.user.id})`);

                const dmEmbed = new EmbedBuilder()
                    .setAuthor({ name: 'TASK ASSIGNED', iconURL: client.user.displayAvatarURL() })
                    .setTitle('📋 Bạn đã được giao một task mới!')
                    .setDescription(
                        `**ID Task:** \`${todoId}\`\n\n` +
                        `**Nội dung:** ${todo.content}\n\n` +
                        `**Độ ưu tiên:** ${priorityIcon} ${priorityText}\n\n` +
                        `**Giao bởi:** ${message.author} (${message.author.tag})\n\n` +
                        `**Thời gian giao:** <t:${Math.floor(Date.now() / 1000)}:F>\n\n` +
                        `**Server:** ${message.guild.name}\n\n` +
                        `💡 **Lưu ý:** Sử dụng lệnh \`todo done ${todoId}\` để đánh dấu hoàn thành khi xong task!`
                    )
                    .setColor(todo.priority === 'high' ? '#ff0000' : todo.priority === 'medium' ? '#3498db' : '#00ff00')
                    .setTimestamp()
                    .setFooter({ text: `Task ID: ${todoId}` });

                await targetUser.user.send({ embeds: [dmEmbed] });
                console.log(`✅ Đã gửi DM thông báo task cho ${targetUser.user.tag} thành công!`);
                dmStatus = '✅ Đã gửi thành công';
            } catch (dmError) {
                console.error(`❌ Lỗi khi gửi DM cho ${targetUser.user.tag}:`, dmError);
                if (dmError.code === 50007) {
                    dmStatus = '❌ User đã đóng DM';
                } else if (dmError.code === 50013) {
                    dmStatus = '❌ Không có quyền gửi DM';
                } else {
                    dmStatus = `❌ Lỗi: ${dmError.message}`;
                }
            }

            const successEmbed = new EmbedBuilder()
                .setAuthor({ name: 'TODO ASSIGNED', iconURL: client.user.displayAvatarURL() })
                .setTitle('👤 Todo đã được giao việc!')
                .setDescription(
                    `**ID:** \`${todoId}\`\n` +
                    `**Nội dung:** ${todo.content}\n` +
                    `**Giao cho:** ${targetUser}\n` +
                    `**Giao bởi:** ${message.author}\n` +
                    `**Độ ưu tiên:** ${priorityIcon} ${priorityText}\n\n` +
                    `📩 **DM Notification:** ${dmStatus}`
                )
                .setColor('#3498db')
                .setTimestamp();

            return message.reply({ embeds: [successEmbed] });
        } else {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'ERROR', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Không thể cập nhật todo!')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }
    },

    // Chỉnh sửa todo
    editTodo(client, message, args) {
        if (args.length < 2) {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'INVALID INPUT', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Vui lòng nhập đầy đủ thông tin!\n\n**Cách sử dụng:** `todo edit <id> <nội dung mới>`')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }

        const todoId = args[0];
        const newContent = args.slice(1).join(' ');
        const todos = loadTodos();
        const todoIndex = todos.findIndex(todo => todo.id === todoId);

        if (todoIndex === -1) {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'NOT FOUND', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Không tìm thấy todo với ID này!')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }

        const todo = todos[todoIndex];
        const oldContent = todo.content;

        // Chỉ người tạo hoặc admin mới có thể sửa
        const canEdit = message.member.permissions.has(PermissionFlagsBits.Administrator) ||
                       todo.createdBy === message.author.id;

        if (!canEdit) {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'ACCESS DENIED', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Bạn chỉ có thể sửa todo do mình tạo!')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }

        // Cập nhật nội dung
        todos[todoIndex].content = newContent;

        if (saveTodos(todos)) {
            const successEmbed = new EmbedBuilder()
                .setAuthor({ name: 'TODO EDITED', iconURL: client.user.displayAvatarURL() })
                .setTitle('✏️ Todo đã được chỉnh sửa!')
                .setDescription(`**ID:** \`${todoId}\`\n**Nội dung cũ:** ${oldContent}\n**Nội dung mới:** ${newContent}\n**Sửa bởi:** ${message.author}`)
                .setColor('#ffa500')
                .setTimestamp();

            return message.reply({ embeds: [successEmbed] });
        } else {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'ERROR', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Không thể cập nhật todo!')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }
    },

    // Đặt độ ưu tiên
    setPriority(client, message, args) {
        if (args.length < 2) {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'INVALID INPUT', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Vui lòng nhập đầy đủ thông tin!\n\n**Cách sử dụng:** `todo priority <id> <low/medium/high>`')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }

        const todoId = args[0];
        const priority = args[1].toLowerCase();

        if (!['low', 'medium', 'high'].includes(priority)) {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'INVALID PRIORITY', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Độ ưu tiên phải là: `low`, `medium`, hoặc `high`')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }

        const todos = loadTodos();
        const todoIndex = todos.findIndex(todo => todo.id === todoId);

        if (todoIndex === -1) {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'NOT FOUND', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Không tìm thấy todo với ID này!')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }

        const todo = todos[todoIndex];

        // Cập nhật độ ưu tiên
        todos[todoIndex].priority = priority;

        if (saveTodos(todos)) {
            const priorityIcon = priority === 'high' ? '🔴' : priority === 'medium' ? '🔵' : '🟢';
            const priorityText = priority === 'high' ? 'Cao' : priority === 'medium' ? 'Trung bình' : 'Thấp';

            const successEmbed = new EmbedBuilder()
                .setAuthor({ name: 'PRIORITY UPDATED', iconURL: client.user.displayAvatarURL() })
                .setTitle('⚡ Độ ưu tiên đã được cập nhật!')
                .setDescription(`**ID:** \`${todoId}\`\n**Nội dung:** ${todo.content}\n**Độ ưu tiên:** ${priorityIcon} ${priorityText}\n**Cập nhật bởi:** ${message.author}`)
                .setColor(priority === 'high' ? '#ff0000' : priority === 'medium' ? '#3498db' : '#00ff00')
                .setTimestamp();

            return message.reply({ embeds: [successEmbed] });
        } else {
            const errorEmbed = new EmbedBuilder()
                .setAuthor({ name: 'ERROR', iconURL: client.user.displayAvatarURL() })
                .setDescription('<:error:1383005371542798346> | Không thể cập nhật todo!')
                .setColor('#ff0000')
                .setTimestamp();
            return message.reply({ embeds: [errorEmbed] });
        }
    }
};
