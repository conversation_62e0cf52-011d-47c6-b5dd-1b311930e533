const { 
    joinVoiceChannel, 
    createAudioPlayer, 
    createAudioResource, 
    entersState, 
    VoiceConnectionStatus, 
    AudioPlayerStatus, 
    getVoiceConnection,
    NoSubscriberBehavior
} = require('@discordjs/voice');
const axios = require('axios');
const { Readable } = require('stream');
const { EmbedBuilder } = require('discord.js');
// Thêm dòng import opus
const { OpusEncoder } = require('@discordjs/opus');
const ffmpeg = require('ffmpeg-static');

// Biến toàn cục để theo dõi thời gian hoạt động cuối cùng của bot trong kênh thoại
const lastActivityTimestamps = new Map();
// Thời gian chờ trước khi ngắt kết nối (5 phút = 300000ms)
const DISCONNECT_TIMEOUT = 300000;

// Thêm biến toàn cục quản lý queue và player cho từng guild
const ttsQueues = new Map();
const ttsPlayers = new Map();

module.exports = {
    name: 's',
    description: 'Chuyển văn bản thành giọng nói trong kênh thoại',
    usage: 'ts <nội dung>',
    async execute(client, message, args) {
        if (!message.guild) {
            return message.reply('Lệnh này chỉ dùng trong server!');
        }

        // Sau đó mới dùng message.guild.members
        const member = message.guild.members.cache.get(message.author.id);
        const userVoiceChannel = member.voice.channel;
        if (!userVoiceChannel) {
            return message.reply('Bạn cần tham gia một kênh thoại trước khi sử dụng lệnh này.');
        }

        // Kiểm tra quyền của bot trong kênh thoại
        const botPermissions = userVoiceChannel.permissionsFor(message.guild.members.me);
        if (!botPermissions.has('Connect') || !botPermissions.has('Speak')) {
            return message.reply('Bot cần quyền Connect và Speak trong kênh thoại này.');
        }

        const botVoiceChannel = message.guild.members.me.voice.channel;
        if (botVoiceChannel && botVoiceChannel.id !== userVoiceChannel.id) {
            const embed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED' })
                    .setDescription(`<:error:1383005371542798346> | Bot đang ở kênh <#${botVoiceChannel.id}>. Bạn cần tham gia vào kênh để sử dụng Bot.`)
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [embed] });
        }

        if (!args.length) {
            const embed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED' })
                    .setDescription(`<:error:1383005371542798346> | Vui lòng nhập nội dung để chuyển thành giọng nói.`)
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [embed] });
        }
        const text = args.join(' ');
        if (text.length > 200) {
            const embed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED' })
                    .setDescription(`<:error:1383005371542798346> | Nội dung quá dài. Vui lòng giữ dưới 200 ký tự.`)
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [embed] });
        }

        // Tạo queue cho guild nếu chưa có
        if (!ttsQueues.has(message.guild.id)) {
            ttsQueues.set(message.guild.id, []);
        }
        const queue = ttsQueues.get(message.guild.id);

        // Kiểm tra nếu đang phát thì gửi embed cảnh báo và không phát ngay
        let player = ttsPlayers.get(message.guild.id);
        if (player && player.state.status === AudioPlayerStatus.Playing) {

            // Lấy thời gian hiện tại
            const now = new Date();
            const hours = now.getHours();
            const minutes = now.getMinutes().toString().padStart(2, '0');
            const ampm = hours >= 12 ? 'CH' : 'SA';
            const displayHour = ((hours + 11) % 12 + 1); // chuyển 0-23 thành 1-12
            const timeString = `Hôm nay lúc ${displayHour}:${minutes} ${ampm}`;

            // Gửi embed cảnh báo
            const embed = new EmbedBuilder()
            .setAuthor({
                name: "ERROR OCCURRED!",
                iconURL: message.client.user.displayAvatarURL()
            })
            .setDescription('<:error:1383005371542798346> | Mình đang nói vui lòng thử lại sau.')
            .setColor(client.embedColor || '#0099ff')
            .setFooter({ text: timeString });
            await message.reply({ embeds: [embed] });
            return;
        }

        // Nếu không có player hoặc player đã idle, phát luôn
        await playTTS({ message, text });

        // Hàm phát TTS
        async function playTTS({ message, text }) {
            try {
                // Tạo URL TTS từ Google Translate
                const ttsUrl = `https://translate.google.com/translate_tts?ie=UTF-8&q=${encodeURIComponent(text)}&tl=vi&client=tw-ob`;
                
                // Tạo kết nối voice
                let connection = getVoiceConnection(message.guild.id);
                if (!connection) {
                    connection = joinVoiceChannel({
                        channelId: userVoiceChannel.id,
                        guildId: message.guild.id,
                        adapterCreator: message.guild.voiceAdapterCreator,
                        selfDeaf: false,
                        selfMute: false
                    });

                    try {
                        await entersState(connection, VoiceConnectionStatus.Ready, 30_000);
                    } catch (error) {
                        console.error('Lỗi kết nối voice:', error);
                        // Log trạng thái kết nối để debug
                        console.error('Voice connection state:', connection.state);
                        connection.destroy();
                        return message.reply('Không thể kết nối tới kênh thoại. Hãy kiểm tra quyền của bot và thử lại.');
                    }
                }

                player = createAudioPlayer({
                    behaviors: {
                        noSubscriber: NoSubscriberBehavior.Play
                    }
                });
                ttsPlayers.set(message.guild.id, player);

                // Tải audio từ Google TTS bằng axios
                const response = await axios({
                    method: 'get',
                    url: ttsUrl,
                    responseType: 'arraybuffer'
                });

                // Chuyển đổi dữ liệu nhận được thành stream
                const audioStream = Readable.from(Buffer.from(response.data));
                
                // Tạo resource từ stream
                // Khi tạo resource
                const resource = createAudioResource(audioStream, {
                    inputType: 'arbitrary',
                    inlineVolume: true,
                    ffmpegPath: ffmpeg // Sử dụng đường dẫn từ package
                });
                
                // Điều chỉnh âm lượng nếu cần
                if (resource.volume) {
                    resource.volume.setVolume(1.0); // Âm lượng tối đa
                }

                // Đăng ký các sự kiện cho player
                player.on(AudioPlayerStatus.Playing, () => {
                    console.log('Bắt đầu phát TTS');
                });

                player.on(AudioPlayerStatus.Idle, async () => {
                    console.log('Kết thúc phát TTS');
                    
                    // Thêm reaction dấu tick vào tin nhắn gốc
                    message.react('✅').catch(error => {
                        console.error('Không thể thêm reaction:', error);
                    });
                    
                    // Cập nhật thời gian hoạt động cuối cùng
                    lastActivityTimestamps.set(message.guild.id, Date.now());
                    
                    // Phát lệnh tiếp theo trong queue nếu có
                    if (queue.length > 0) {
                        const next = queue.shift();
                        await playTTS(next);
                    } else {
                        ttsPlayers.delete(message.guild.id);
                        setupDisconnectTimer(message.guild.id, connection);
                    }
                });

                player.on('error', error => {
                    console.error('Lỗi khi phát TTS:', error);
                    if (connection) {
                        connection.destroy();
                    }
                });

                // Kết nối player với connection
                connection.subscribe(player);
                
                // Cập nhật thời gian hoạt động cuối cùng
                lastActivityTimestamps.set(message.guild.id, Date.now());
                
                // Phát audio
                player.play(resource);

            } catch (error) {
                console.error('Lỗi tổng thể:', error);
                message.reply('Đã xảy ra lỗi khi xử lý yêu cầu TTS.');
            }
        }
    },
};

// Hàm thiết lập bộ đếm thời gian ngắt kết nối
function setupDisconnectTimer(guildId, connection) {
    // Xóa bộ hẹn giờ cũ nếu có
    if (connection.disconnectTimeout) {
        clearTimeout(connection.disconnectTimeout);
    }
    
    // Thiết lập bộ hẹn giờ mới
    connection.disconnectTimeout = setTimeout(() => {
        const lastActivity = lastActivityTimestamps.get(guildId) || 0;
        const now = Date.now();
        
        // Kiểm tra xem đã đủ thời gian chờ chưa
        if (now - lastActivity >= DISCONNECT_TIMEOUT) {
            console.log(`Không có hoạt động trong ${DISCONNECT_TIMEOUT / 60000} phút, ngắt kết nối khỏi kênh thoại.`);
            connection.destroy();
            lastActivityTimestamps.delete(guildId);
        } else {
            // Nếu có hoạt động mới, thiết lập lại bộ hẹn giờ
            const remainingTime = DISCONNECT_TIMEOUT - (now - lastActivity);
            setupDisconnectTimer(guildId, connection);
        }
    }, DISCONNECT_TIMEOUT);
}