const { PermissionFlagsBits, EmbedBuilder } = require('discord.js');

module.exports = {
    name: 'unban',
    description: 'Bỏ cấm một thành viên khỏi server',
    usage: 'tunban <userID> [lý do]',
    async execute(client, message, args) {
        // Ki<PERSON><PERSON> tra quyền: Administrator hoặc role cụ thể
        const hasAdminPermission = message.member.permissions.has(PermissionFlagsBits.Administrator);
        const hasSpecialRole = message.member.roles.cache.has('1376500798896214108') ||
                              message.member.roles.cache.has('1376884726232514620');

        if (!hasAdminPermission && !hasSpecialRole) {
            const embed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED' })
                    .setDescription(`<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!`)
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [embed] });
        }
        
        
        if (!args[0]) {
            const embed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED' })
                    .setDescription(`<:error:1383005371542798346> | Vui lòng nhập ID người dùng cần unban.`)
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [embed] });
        }
        
        const userId = args[0];
        
        // Kiểm tra xem ID có hợp lệ không (chỉ chứa số và có độ dài phù hợp)
        if (!/^\d{17,19}$/.test(userId)) {
            const embed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED' })
                    .setDescription(`<:error:1383005371542798346> | ID người dùng không hợp lệ. ID Discord thường có 17-19 chữ số.`)
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [embed] });
        }
        
        const reason = args.slice(1).join(' ') || `Được unban bởi ${message.author.tag}`;
        
        try {
            // Lấy danh sách ban
            const bans = await message.guild.bans.fetch();
            
            // Kiểm tra xem người dùng có trong danh sách ban không
            if (!bans.has(userId)) {
                const embed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED' })
                    .setDescription(`<:error:1383005371542798346> | Người dùng này không bị ban hoặc ID không chính xác.`)
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [embed] });
            }
            
            // Unban người dùng
            await message.guild.members.unban(userId, reason);
            const embed = new EmbedBuilder()
                .setAuthor({ name: 'ACTION SUCCESSFUL' })
                .setDescription(`<:done:1383009630581424250> | Đã unban người dùng có ID: ${userId}.${reason ? ` Lý do: ${reason}` : ''}`)
                .setColor(client.embedColor || '#0099ff')
                .setTimestamp();
            return message.reply({ embeds: [embed] });

        } catch (err) {
            console.error(err);
            const embed = new EmbedBuilder()
                .setAuthor({ name: 'ACTION FAILED' })
                .setDescription('<:error:1383005371542798346> | Không thể unban người dùng này. Vui lòng kiểm tra lại ID và đảm bảo nó chính xác.')
                .setColor(client.embedColor || '#0099ff')
                .setTimestamp();
            return message.reply({ embeds: [embed] });
        }
    }
};