const { PermissionFlagsBits, EmbedBuilder } = require('discord.js');

module.exports = {
  name: 'unlock',
  async execute(client, message, args) {
    // <PERSON><PERSON><PERSON> tra quyền: Administrator hoặc role cụ thể
    const hasAdminPermission = message.member.permissions.has(PermissionFlagsBits.Administrator);
    const hasSpecialRole = message.member.roles.cache.has('1376500798896214108') ||
                          message.member.roles.cache.has('1376884726232514620');

    if (!hasAdminPermission && !hasSpecialRole) {
      const embed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED' })
          .setDescription('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [embed] });
    }
    const channel = message.channel;
    try {
      await channel.permissionOverwrites.edit(channel.guild.roles.everyone, {
        SendMessages: true
      });
      const successEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION SUCCESSFUL' })
        .setDescription('<:done:1383009630581424250> | Kênh đã được mở khóa!')
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      message.reply({ embeds: [successEmbed] });

    } catch (err) {
      console.error(err);
      const errorEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED' })
        .setDescription('<:error:1383005371542798346> | Có lỗi khi khóa kênh!')
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      message.reply({ embeds: [errorEmbed] });
    }
  }
};