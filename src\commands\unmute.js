const { PermissionFlagsBits, EmbedBuilder } = require('discord.js');

module.exports = {
    name: 'unmute',
    description: 'Bỏ tắt tiếng (timeout và voice) một thành viên',
    usage: '?unmute @user hoặc !unmute userID',
    async execute(client, message, args) {
        try {
            // Ki<PERSON><PERSON> tra quyền: Administrator hoặc role cụ thể
            const hasAdminPermission = message.member.permissions.has(PermissionFlagsBits.Administrator);
            const hasSpecialRole = message.member.roles.cache.has('1376500798896214108') ||
                                  message.member.roles.cache.has('1376884726232514620');

            if (!hasAdminPermission && !hasSpecialRole) {
                const embed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED' })
                    .setDescription('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!')
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [embed] });
            }
            
            const member = message.mentions.members.first()
                || await message.guild.members.fetch(args[0]).catch(() => null);
            if (!member) {
                const embed = new EmbedBuilder()
                    .setAuthor({ name: 'ACTION FAILED' })
                    .setDescription('<:error:1383005371542798346> | Vui lòng tag thành viên hoặc nhập ID thành viên cần unmute.')
                    .setColor(client.embedColor || '#0099ff')
                    .setTimestamp();
                return message.reply({ embeds: [embed] });
            }
            
            // Bỏ timeout (unmute chat)
            await member.timeout(null, `Được unmute bởi ${message.author.tag}`);
            // Bỏ mute voice (nếu có)
            await member.voice.setMute(false, `Được bỏ tắt tiếng bởi ${message.author.tag}`).catch(() => {});
            
            const embed = new EmbedBuilder()
                .setAuthor({ name: 'ACTION SUCCESSFUL' })
                .setDescription(`<:done:1383009630581424250> | Đã bỏ mọi tắt tiếng cho ${member.user.tag}.`)
                .setColor(client.embedColor || '#0099ff')
                .setTimestamp();
            return message.reply({ embeds: [embed] });
            
        } catch (err) {
            const embed = new EmbedBuilder()
                .setAuthor({ name: 'ACTION FAILED' })
                .setDescription('<:error:1383005371542798346> | Không thể bỏ tắt tiếng thành viên này.')
                .setColor(client.embedColor || '#0099ff')
                .setTimestamp();
            return message.reply({ embeds: [embed] });
        }
    }
};