const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const weeklyMessageDB = require('../data/weeklyMessageDB');

module.exports = {
  name: 'weeklycount',
  description: 'Qu<PERSON>n lý và xem thống kê tin nhắn hàng tuần',
  usage: 'weeklycount <add|remove|list|stats> [#kênh]',
  aliases: ['wcount', 'weekcount'],

  async execute(client, message, args) {
    try {
      // Kiểm tra quyền: Administrator hoặc role cụ thể
      const hasAdminPermission = message.member.permissions.has(PermissionFlagsBits.Administrator);
      const hasSpecialRole = message.member.roles.cache.has('1376500798896214108') ||
                            message.member.roles.cache.has('1376884726232514620');

      if (!hasAdminPermission && !hasSpecialRole) {
        return message.reply('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!');
      }

      // <PERSON><PERSON><PERSON> thị help nếu không có tham số
      if (args.length === 0) {
        const helpEmbed = new EmbedBuilder()
          .setColor(client.embedColor || '#0099ff')
          .setAuthor({ name: 'WEEKLY MESSAGE TRACKER HELP', iconURL: client.user.displayAvatarURL() })
          .setDescription(
            '**Hệ thống giám sát tin nhắn hàng tuần (Thứ 2 - Chủ nhật)**\n\n' +
            '**Các lệnh có sẵn:**\n' +
            '`weeklycount add #kênh` - Thêm kênh vào giám sát\n' +
            '`weeklycount remove #kênh` - Xóa kênh khỏi giám sát\n' +
            '`weeklycount list` - Xem danh sách kênh đang giám sát\n' +
            '`weeklycount stats` - Xem thống kê tuần tất cả kênh\n' +
            '`weeklycount stats #kênh` - Xem thống kê tuần của kênh cụ thể\n\n' +
            '**Tính năng:**\n' +
            '• Tự động đếm tin nhắn từ thứ 2 đến chủ nhật\n' +
            '• Reset tự động mỗi tuần mới\n' +
            '• Leaderboard theo kênh hoặc tổng hợp\n' +
            '• Chỉ đếm tin nhắn từ users (không đếm bot)'
          )
          .setTimestamp();

        return message.reply({ embeds: [helpEmbed] });
      }

      const action = args[0].toLowerCase();

      switch (action) {
        case 'add':
          await handleAddChannel(client, message, args);
          break;
        case 'remove':
          await handleRemoveChannel(client, message, args);
          break;
        case 'list':
          await handleListChannels(client, message);
          break;
        case 'stats':
          await handleShowStats(client, message, args);
          break;
        default:
          const errorEmbed = new EmbedBuilder()
            .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
            .setDescription('<:error:1383005371542798346> | Lệnh không hợp lệ! Sử dụng: `add`, `remove`, `list`, hoặc `stats`')
            .setColor(client.embedColor || '#0099ff')
            .setTimestamp();
          return message.reply({ embeds: [errorEmbed] });
      }

    } catch (error) {
      console.error('Lỗi trong lệnh weeklycount:', error);
      const errorEmbed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
        .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi thực hiện lệnh!')
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      return message.reply({ embeds: [errorEmbed] });
    }
  }
};

// Thêm kênh vào giám sát
async function handleAddChannel(client, message, args) {
  if (args.length < 2) {
    const errorEmbed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
      .setDescription('<:error:1383005371542798346> | Vui lòng mention kênh cần thêm!\nVí dụ: `weeklycount add #general`')
      .setColor(client.embedColor || '#0099ff')
      .setTimestamp();
    return message.reply({ embeds: [errorEmbed] });
  }

  // Lấy kênh từ mention
  const channelMention = args[1];
  let targetChannel = null;
  
  if (channelMention.startsWith('<#') && channelMention.endsWith('>')) {
    const channelId = channelMention.slice(2, -1);
    targetChannel = message.guild.channels.cache.get(channelId);
  } else if (/^\d+$/.test(channelMention)) {
    targetChannel = message.guild.channels.cache.get(channelMention);
  }

  if (!targetChannel || targetChannel.type !== 0) {
    const errorEmbed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
      .setDescription('<:error:1383005371542798346> | Kênh không hợp lệ! Vui lòng mention kênh text.')
      .setColor(client.embedColor || '#0099ff')
      .setTimestamp();
    return message.reply({ embeds: [errorEmbed] });
  }

  try {
    await weeklyMessageDB.addMonitoredChannel(targetChannel.id, targetChannel.name, message.guild.id);
    
    const successEmbed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION SUCCESSFUL', iconURL: client.user.displayAvatarURL() })
      .setDescription(`<:done:1383009630581424250> | Đã thêm ${targetChannel} vào danh sách giám sát tuần!`)
      .setColor(client.embedColor || '#0099ff')
      .setTimestamp();
    
    return message.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error('Lỗi khi thêm kênh giám sát:', error);
    const errorEmbed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
      .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi thêm kênh!')
      .setColor(client.embedColor || '#0099ff')
      .setTimestamp();
    return message.reply({ embeds: [errorEmbed] });
  }
}

// Xóa kênh khỏi giám sát
async function handleRemoveChannel(client, message, args) {
  if (args.length < 2) {
    const errorEmbed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
      .setDescription('<:error:1383005371542798346> | Vui lòng mention kênh cần xóa!')
      .setColor(client.embedColor || '#0099ff')
      .setTimestamp();
    return message.reply({ embeds: [errorEmbed] });
  }

  // Lấy kênh từ mention
  const channelMention = args[1];
  let targetChannel = null;
  
  if (channelMention.startsWith('<#') && channelMention.endsWith('>')) {
    const channelId = channelMention.slice(2, -1);
    targetChannel = message.guild.channels.cache.get(channelId);
  } else if (/^\d+$/.test(channelMention)) {
    targetChannel = message.guild.channels.cache.get(channelMention);
  }

  if (!targetChannel) {
    const errorEmbed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
      .setDescription('<:error:1383005371542798346> | Kênh không hợp lệ!')
      .setColor(client.embedColor || '#0099ff')
      .setTimestamp();
    return message.reply({ embeds: [errorEmbed] });
  }

  try {
    await weeklyMessageDB.removeMonitoredChannel(targetChannel.id);
    
    const successEmbed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION SUCCESSFUL', iconURL: client.user.displayAvatarURL() })
      .setDescription(`<:done:1383009630581424250> | Đã xóa ${targetChannel} khỏi danh sách giám sát tuần!`)
      .setColor(client.embedColor || '#0099ff')
      .setTimestamp();
    
    return message.reply({ embeds: [successEmbed] });
  } catch (error) {
    console.error('Lỗi khi xóa kênh giám sát:', error);
    const errorEmbed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
      .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi xóa kênh!')
      .setColor(client.embedColor || '#0099ff')
      .setTimestamp();
    return message.reply({ embeds: [errorEmbed] });
  }
}

// Hiển thị danh sách kênh đang giám sát
async function handleListChannels(client, message) {
  try {
    const monitoredChannels = await weeklyMessageDB.getMonitoredChannels(message.guild.id);
    
    if (monitoredChannels.length === 0) {
      const noDataEmbed = new EmbedBuilder()
        .setAuthor({ name: 'NO DATA', iconURL: client.user.displayAvatarURL() })
        .setDescription('<:error:1383005371542798346>| Chưa có kênh nào được thêm vào giám sát tuần!')
        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      return message.reply({ embeds: [noDataEmbed] });
    }

    let channelList = '';
    for (let i = 0; i < monitoredChannels.length; i++) {
      const channel = message.guild.channels.cache.get(monitoredChannels[i].channelId);
      const channelDisplay = channel ? `${channel}` : `#${monitoredChannels[i].channelName}`;
      channelList += `\`${(i + 1).toString().padStart(2, ' ')}.\` ${channelDisplay}\n`;
    }

    const { weekStart, weekEnd } = weeklyMessageDB.getCurrentWeekRange();
    
    const listEmbed = new EmbedBuilder()
      .setColor(client.embedColor || '#0099ff')
      .setAuthor({
        name: 'MONITORED CHANNELS',
        iconURL: client.user.displayAvatarURL()
      })
      .setTitle('Danh sách kênh giám sát tuần')
      .setDescription(channelList)
      .addFields(
        { name: 'Tuần hiện tại', value: `${weekStart} đến ${weekEnd}`, inline: true },
        { name: 'Tổng kênh', value: `${monitoredChannels.length} kênh`, inline: true }
      )
      .setTimestamp()
      .setFooter({
        text: `Xem bởi ${message.author.username}`,
        iconURL: message.author.displayAvatarURL()
      });

    return message.reply({ embeds: [listEmbed] });
  } catch (error) {
    console.error('Lỗi khi lấy danh sách kênh:', error);
    const errorEmbed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
      .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi lấy danh sách kênh!')
      .setColor(client.embedColor || '#0099ff')
      .setTimestamp();
    return message.reply({ embeds: [errorEmbed] });
  }
}

// Hiển thị thống kê tuần
async function handleShowStats(client, message, args) {
  try {
    const { weekStart, weekEnd } = weeklyMessageDB.getCurrentWeekRange();

    // Nếu có mention kênh cụ thể
    if (args.length > 1) {
      const channelMention = args[1];
      let targetChannel = null;

      if (channelMention.startsWith('<#') && channelMention.endsWith('>')) {
        const channelId = channelMention.slice(2, -1);
        targetChannel = message.guild.channels.cache.get(channelId);
      } else if (/^\d+$/.test(channelMention)) {
        targetChannel = message.guild.channels.cache.get(channelMention);
      }

      if (!targetChannel) {
        const errorEmbed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
          .setDescription('<:error:1383005371542798346> | Kênh không hợp lệ!')
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [errorEmbed] });
      }

      // Thống kê cho kênh cụ thể
      const channelStats = await weeklyMessageDB.getWeeklyStats(targetChannel.id);

      if (channelStats.length === 0) {
        const noDataEmbed = new EmbedBuilder()
          .setAuthor({ name: 'NO DATA', iconURL: client.user.displayAvatarURL() })
          .setDescription(`<:error:1383005371542798346> | Chưa có dữ liệu tin nhắn nào trong ${targetChannel} tuần này!`)
          .setColor(client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [noDataEmbed] });
      }

      // Tạo leaderboard cho kênh
      let leaderboard = '';
      const medals = ['\` 1\`', '\` 2\`', '\` 3\`'];
      let totalMessages = 0;

      for (let i = 0; i < Math.min(channelStats.length, 15); i++) {
        const stat = channelStats[i];
        const user = await client.users.fetch(stat.userId).catch(() => null);
        const username = user ? user.username : `User ${stat.userId}`;
        const medal = i < 3 ? medals[i] : `\`${(i + 1).toString().padStart(2, ' ')}.\``;

        leaderboard += `${medal} **${username}** - ${stat.messageCount.toLocaleString('vi-VN')} tin nhắn\n`;
        totalMessages += stat.messageCount;
      }

      const channelEmbed = new EmbedBuilder()
        .setColor(client.embedColor || '#0099ff')
        .setAuthor({
          name: 'WEEKLY CHANNEL STATS',
          iconURL: client.user.displayAvatarURL()
        })
        .setTitle(`Thống kê tuần ${targetChannel.name}`)
        .setDescription(leaderboard)
        .addFields(
          { name: 'Tuần', value: `${weekStart} đến ${weekEnd}`, inline: true },
          { name: 'Tổng tin nhắn', value: `${totalMessages.toLocaleString('vi-VN')}`, inline: true },
          { name: 'Tổng users', value: `${channelStats.length.toLocaleString('vi-VN')}`, inline: true }
        )
        .setTimestamp()
        .setFooter({
          text: `Thống kê bởi ${message.author.username}`,
          iconURL: message.author.displayAvatarURL()
        });

      return message.reply({ embeds: [channelEmbed] });
    }

    // Thống kê tổng hợp tất cả kênh
    const allStats = await weeklyMessageDB.getAllWeeklyStats(message.guild.id);

    if (allStats.length === 0) {
      const noDataEmbed = new EmbedBuilder()
        .setAuthor({ name: 'NO DATA', iconURL: client.user.displayAvatarURL() })
        .setDescription('<error:1383005371542798346> | Chưa có dữ liệu tin nhắn nào tuần này!')        .setColor(client.embedColor || '#0099ff')
        .setTimestamp();
      return message.reply({ embeds: [noDataEmbed] });
    }

    // Nhóm theo kênh
    const channelGroups = {};
    let grandTotal = 0;
    const userTotals = {};

    allStats.forEach(stat => {
      if (!channelGroups[stat.channelId]) {
        channelGroups[stat.channelId] = [];
      }
      channelGroups[stat.channelId].push(stat);
      grandTotal += stat.messageCount;

      // Tính tổng cho mỗi user
      if (!userTotals[stat.userId]) {
        userTotals[stat.userId] = 0;
      }
      userTotals[stat.userId] += stat.messageCount;
    });

    // Tạo top users tổng hợp
    const topUsers = Object.entries(userTotals)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10);

    let globalLeaderboard = '';
    const medals = ['\` 1\`', '\` 2\`', '\` 3\`'];

    for (let i = 0; i < topUsers.length; i++) {
      const [userId, messageCount] = topUsers[i];
      const user = await client.users.fetch(userId).catch(() => null);
      const username = user ? user.username : `User ${userId}`;
      const medal = i < 3 ? medals[i] : `\`${(i + 1).toString().padStart(2, ' ')}.\``;

      globalLeaderboard += `${medal} **${username}** - ${messageCount.toLocaleString('vi-VN')} tin nhắn\n`;
    }

    // Tạo thống kê theo kênh
    let channelStats = '';
    const channelEntries = Object.entries(channelGroups);

    for (const [channelId, stats] of channelEntries.slice(0, 5)) {
      const channel = message.guild.channels.cache.get(channelId);
      const channelName = channel ? channel.name : 'Unknown';
      const channelTotal = stats.reduce((sum, stat) => sum + stat.messageCount, 0);

      channelStats += `**#${channelName}** - ${channelTotal.toLocaleString('vi-VN')} tin nhắn\n`;
    }

    const summaryEmbed = new EmbedBuilder()
      .setColor(client.embedColor || '#0099ff')
      .setAuthor({
        name: 'WEEKLY SERVER STATS',
        iconURL: client.user.displayAvatarURL()
      })
      .setTitle('Thống kê tuần toàn server')
      .addFields(
        { name: 'Top Users Toàn Server', value: globalLeaderboard || 'Không có dữ liệu', inline: false },
        { name: 'Top Channels', value: channelStats || 'Không có dữ liệu', inline: false },
        { name: 'Tuần', value: `${weekStart} đến ${weekEnd}`, inline: true },
        { name: 'Tổng tin nhắn', value: `${grandTotal.toLocaleString('vi-VN')}`, inline: true },
        { name: 'Kênh giám sát', value: `${Object.keys(channelGroups).length}`, inline: true }
      )
      .setTimestamp()
      .setFooter({
        text: `Thống kê bởi ${message.author.username} • Sử dụng "weeklycount stats #kênh" để xem chi tiết kênh`,
        iconURL: message.author.displayAvatarURL()
      });

    return message.reply({ embeds: [summaryEmbed] });

  } catch (error) {
    console.error('Lỗi khi hiển thị thống kê:', error);
    const errorEmbed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED', iconURL: client.user.displayAvatarURL() })
      .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi lấy thống kê!')
      .setColor(client.embedColor || '#0099ff')
      .setTimestamp();
    return message.reply({ embeds: [errorEmbed] });
  }
}
