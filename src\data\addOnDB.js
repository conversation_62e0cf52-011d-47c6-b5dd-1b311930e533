const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Tạo kết nối đến database
const db = new sqlite3.Database(path.join(__dirname, 'add_on.db'));

// Tạo bảng nếu chưa có
db.serialize(() => {
  db.run(`
    CREATE TABLE IF NOT EXISTS add_on_history (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      billId TEXT NOT NULL,
      khachId TEXT NOT NULL,
      playerId TEXT NOT NULL,
      type TEXT NOT NULL,
      amount INTEGER NOT NULL,
      description TEXT,
      time INTEGER NOT NULL,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);
});

/**
 * Thêm thông tin add-on mới
 * @param {Object} addOnData - Dữ liệu add-on
 * @param {string} addOnData.billId - ID của bill
 * @param {string} addOnData.khachId - ID khách hàng
 * @param {string} addOnData.playerId - ID player
 * @param {string} addOnData.type - Loại add-on ('svr' hoặc 'themnguoi')
 * @param {number} addOnData.amount - Số tiền add-on
 * @param {string} addOnData.description - Mô tả add-on
 * @param {number} addOnData.time - Timestamp
 * @returns {Promise<Object>} - Kết quả thêm add-on
 */
function addAddOn(addOnData) {
  return new Promise((resolve, reject) => {
    const { billId, khachId, playerId, type, amount, description, time } = addOnData;
    
    db.run(
      `INSERT INTO add_on_history (billId, khachId, playerId, type, amount, description, time) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [billId, khachId, playerId, type, amount, description, time],
      function(err) {
        if (err) {
          console.error('Lỗi khi thêm add-on:', err);
          reject(err);
        } else {
          resolve({ id: this.lastID, ...addOnData });
        }
      }
    );
  });
}

/**
 * Lấy tất cả add-on history
 * @returns {Promise<Array>} - Mảng tất cả add-on
 */
function getAllAddOns() {
  return new Promise((resolve, reject) => {
    db.all(
      `SELECT * FROM add_on_history ORDER BY time DESC`,
      [],
      (err, rows) => {
        if (err) {
          console.error('Lỗi khi lấy tất cả add-on:', err);
          reject(err);
        } else {
          resolve(rows || []);
        }
      }
    );
  });
}

/**
 * Lấy add-on history của một player
 * @param {string} playerId - ID của player
 * @returns {Promise<Array>} - Mảng add-on của player
 */
function getPlayerAddOns(playerId) {
  return new Promise((resolve, reject) => {
    db.all(
      `SELECT * FROM add_on_history WHERE playerId = ? ORDER BY time DESC`,
      [playerId],
      (err, rows) => {
        if (err) {
          console.error('Lỗi khi lấy add-on của player:', err);
          reject(err);
        } else {
          resolve(rows || []);
        }
      }
    );
  });
}

/**
 * Lấy add-on history của một khách hàng
 * @param {string} khachId - ID của khách hàng
 * @returns {Promise<Array>} - Mảng add-on của khách
 */
function getKhachAddOns(khachId) {
  return new Promise((resolve, reject) => {
    db.all(
      `SELECT * FROM add_on_history WHERE khachId = ? ORDER BY time DESC`,
      [khachId],
      (err, rows) => {
        if (err) {
          console.error('Lỗi khi lấy add-on của khách:', err);
          reject(err);
        } else {
          resolve(rows || []);
        }
      }
    );
  });
}

/**
 * Tính tổng add-on theo loại
 * @returns {Promise<Object>} - Object chứa tổng theo từng loại
 */
function getTotalAddOnsByType() {
  return new Promise((resolve, reject) => {
    db.all(
      `SELECT type, SUM(amount) as total, COUNT(*) as count 
       FROM add_on_history 
       GROUP BY type`,
      [],
      (err, rows) => {
        if (err) {
          console.error('Lỗi khi tính tổng add-on:', err);
          reject(err);
        } else {
          const result = {
            svr: { total: 0, count: 0 },
            themnguoi: { total: 0, count: 0 }
          };
          
          rows.forEach(row => {
            if (row.type === 'svr' || row.type === 'themnguoi') {
              result[row.type] = {
                total: row.total || 0,
                count: row.count || 0
              };
            }
          });
          
          resolve(result);
        }
      }
    );
  });
}

/**
 * Tính tổng tất cả add-on
 * @returns {Promise<Object>} - Object chứa tổng tiền và số lượng
 */
function getTotalAddOns() {
  return new Promise((resolve, reject) => {
    db.get(
      `SELECT SUM(amount) as totalAmount, COUNT(*) as totalCount 
       FROM add_on_history`,
      [],
      (err, row) => {
        if (err) {
          console.error('Lỗi khi tính tổng add-on:', err);
          reject(err);
        } else {
          resolve({
            totalAmount: row?.totalAmount || 0,
            totalCount: row?.totalCount || 0
          });
        }
      }
    );
  });
}

/**
 * Xóa add-on theo ID
 * @param {number} id - ID của add-on cần xóa
 * @returns {Promise<boolean>} - Kết quả xóa
 */
function removeAddOn(id) {
  return new Promise((resolve, reject) => {
    db.run(
      `DELETE FROM add_on_history WHERE id = ?`,
      [id],
      function(err) {
        if (err) {
          console.error('Lỗi khi xóa add-on:', err);
          reject(err);
        } else {
          resolve(this.changes > 0);
        }
      }
    );
  });
}

/**
 * Xóa tất cả add-on của một player
 * @param {string} playerId - ID của player
 * @returns {Promise<number>} - Số lượng add-on đã xóa
 */
function removePlayerAddOns(playerId) {
  return new Promise((resolve, reject) => {
    db.run(
      `DELETE FROM add_on_history WHERE playerId = ?`,
      [playerId],
      function(err) {
        if (err) {
          console.error('Lỗi khi xóa add-on của player:', err);
          reject(err);
        } else {
          resolve(this.changes);
        }
      }
    );
  });
}

/**
 * Lấy night bills của một player (các bill có type = 'giadem')
 * Kết hợp với thông tin từ playerHistoryDB để lấy số giờ
 * @param {string} playerId - ID của player
 * @returns {Promise<Array>} - Mảng night bills với thông tin đầy đủ
 */
function getPlayerNightBills(playerId) {
  return new Promise((resolve, reject) => {
    // Thêm cột isPaid nếu chưa có
    db.run(`ALTER TABLE add_on_history ADD COLUMN isPaid INTEGER DEFAULT 0`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Lỗi khi thêm cột isPaid:', err);
      }

      // Lấy night bills chưa thanh toán
      db.all(
        `SELECT * FROM add_on_history WHERE playerId = ? AND type = 'giadem' AND (isPaid = 0 OR isPaid IS NULL) ORDER BY time DESC`,
        [playerId],
        (err, rows) => {
          if (err) {
            console.error('Lỗi khi lấy night bills của player:', err);
            reject(err);
          } else {
            resolve(rows || []);
          }
        }
      );
    });
  });
}

/**
 * Đánh dấu night bills của player là đã thanh toán (để reset lương)
 * @param {string} playerId - ID của player
 * @returns {Promise<boolean>} - Kết quả thực hiện
 */
function markPlayerNightBillsPaid(playerId) {
  return new Promise((resolve, reject) => {
    // Đánh dấu các night bills là đã thanh toán
    db.run(
      `UPDATE add_on_history SET isPaid = 1 WHERE playerId = ? AND type = 'giadem' AND (isPaid = 0 OR isPaid IS NULL)`,
      [playerId],
      function(err) {
        if (err) {
          console.error('Lỗi khi đánh dấu night bills đã thanh toán:', err);
          reject(err);
        } else {
          console.log(`Đã đánh dấu ${this.changes} night bills của player ${playerId} là đã thanh toán`);
          resolve(true);
        }
      }
    );
  });
}

module.exports = {
  addAddOn,
  getAllAddOns,
  getPlayerAddOns,
  getKhachAddOns,
  getTotalAddOnsByType,
  getTotalAddOns,
  removeAddOn,
  removePlayerAddOns,
  getPlayerNightBills,
  markPlayerNightBillsPaid
};
