const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

// Đường dẫn đến file database
const dbPath = path.join(__dirname, 'cash.db');

// Tạo kết nối đến database
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('Lỗi khi kết nối đến database:', err.message);
  } else {
    console.log('Đã kết nối đến database cash.db');
    
    // Tạo bảng cash nếu chưa tồn tại
    db.run(`
      CREATE TABLE IF NOT EXISTS cash (
        userId TEXT PRIMARY KEY,
        amount INTEGER DEFAULT 0,
        updatedAt TEXT
      )
    `);
  }
});

const cashDB = {
  // Lấy số tiền của người dùng
  getCash: (userId) => {
    return new Promise((resolve, reject) => {
      db.get('SELECT * FROM cash WHERE userId = ?', [userId], (err, row) => {
        if (err) {
          console.error('Lỗi khi lấy thông tin cash:', err);
          reject(err);
        } else {
          resolve(row ? row : { userId, amount: 0, updatedAt: new Date().toISOString() });
        }
      });
    });
  },

  // Thêm tiền cho người dùng
  addCash: (userId, amount) => {
    return new Promise((resolve, reject) => {
      const now = new Date().toISOString();
      
      // Kiểm tra xem người dùng đã tồn tại chưa
      db.get('SELECT * FROM cash WHERE userId = ?', [userId], (err, row) => {
        if (err) {
          console.error('Lỗi khi kiểm tra người dùng:', err);
          reject(err);
          return;
        }
        
        if (row) {
          // Người dùng đã tồn tại, cập nhật số tiền
          const newAmount = row.amount + amount;
          db.run(
            'UPDATE cash SET amount = ?, updatedAt = ? WHERE userId = ?',
            [newAmount, now, userId],
            function(err) {
              if (err) {
                console.error('Lỗi khi cập nhật cash:', err);
                reject(err);
              } else {
                resolve({ userId, amount: newAmount, updatedAt: now });
              }
            }
          );
        } else {
          // Người dùng chưa tồn tại, thêm mới
          db.run(
            'INSERT INTO cash (userId, amount, updatedAt) VALUES (?, ?, ?)',
            [userId, amount, now],
            function(err) {
              if (err) {
                console.error('Lỗi khi thêm cash:', err);
                reject(err);
              } else {
                resolve({ userId, amount, updatedAt: now });
              }
            }
          );
        }
      });
    });
  },

  // Trừ tiền của người dùng
  subtractCash: (userId, amount) => {
    return new Promise((resolve, reject) => {
      const now = new Date().toISOString();
      
      // Kiểm tra xem người dùng đã tồn tại chưa
      db.get('SELECT * FROM cash WHERE userId = ?', [userId], (err, row) => {
        if (err) {
          console.error('Lỗi khi kiểm tra người dùng:', err);
          reject(err);
          return;
        }
        
        if (row) {
          // Người dùng đã tồn tại, kiểm tra số dư
          if (row.amount < amount) {
            reject(new Error('Số dư không đủ'));
            return;
          }
          
          // Cập nhật số tiền
          const newAmount = row.amount - amount;
          db.run(
            'UPDATE cash SET amount = ?, updatedAt = ? WHERE userId = ?',
            [newAmount, now, userId],
            function(err) {
              if (err) {
                console.error('Lỗi khi cập nhật cash:', err);
                reject(err);
              } else {
                resolve({ userId, amount: newAmount, updatedAt: now });
              }
            }
          );
        } else {
          // Người dùng chưa tồn tại
          reject(new Error('Người dùng không tồn tại hoặc không có tiền'));
        }
      });
    });
  },

  // Lấy danh sách tất cả người dùng có tiền cọc
  getAllCash: () => {
    return new Promise((resolve, reject) => {
      db.all('SELECT * FROM cash ORDER BY amount DESC', [], (err, rows) => {
        if (err) {
          console.error('Lỗi khi lấy danh sách cash:', err);
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  },

  // Đóng kết nối database khi tắt bot
  close: () => {
    return new Promise((resolve, reject) => {
      db.close((err) => {
        if (err) {
          console.error('Lỗi khi đóng kết nối database:', err);
          reject(err);
        } else {
          console.log('Đã đóng kết nối database cash.db');
          resolve();
        }
      });
    });
  }
};

module.exports = cashDB;