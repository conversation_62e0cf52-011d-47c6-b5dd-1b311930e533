const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

// <PERSON><PERSON><PERSON> bảo thư mục data tồn tại
const dataDir = path.join(__dirname);
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// Kết nối đến database
const db = new sqlite3.Database(path.join(dataDir, 'donate_history.db'));

// Khởi tạo bảng nếu chưa tồn tại
db.serialize(() => {
  db.run(`
    CREATE TABLE IF NOT EXISTS donate_history (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      khachId TEXT NOT NULL,
      playerId TEXT NOT NULL,
      amount INTEGER NOT NULL,
      received INTEGER NOT NULL,
      commission INTEGER NOT NULL,
      paymentMethod TEXT NOT NULL,
      time INTEGER NOT NULL,
      donateId TEXT NOT NULL
    )
  `);
  
  // Thê<PERSON> bảng tổng hợp donate
  db.run(`
    CREATE TABLE IF NOT EXISTS donate_summary (
      khachId TEXT NOT NULL,
      playerId TEXT NOT NULL,
      totalDonated INTEGER NOT NULL DEFAULT 0,
      PRIMARY KEY (khachId, playerId)
    )
  `);
});

// Thêm lịch sử donate mới
function addDonateHistory(donateData) {
  return new Promise((resolve, reject) => {
    const { khachId, playerId, amount, received, commission, paymentMethod, time, donateId } = donateData;
    
    db.run(
      `INSERT INTO donate_history (khachId, playerId, amount, received, commission, paymentMethod, time, donateId) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [khachId, playerId, amount, received, commission, paymentMethod, time, donateId],
      function(err) {
        if (err) {
          console.error('Lỗi khi thêm lịch sử donate:', err);
          reject(err);
        } else {
          // Cập nhật tổng donate
          updateDonateSummary(khachId, playerId, amount)
            .then(() => resolve({ id: this.lastID, ...donateData }))
            .catch(reject);
        }
      }
    );
  });
}

// Lấy lịch sử donate của một player
function getPlayerDonateHistory(playerId) {
  return new Promise((resolve, reject) => {
    db.all(
      'SELECT * FROM donate_history WHERE playerId = ? ORDER BY time DESC',
      [playerId],
      (err, rows) => {
        if (err) {
          console.error('Lỗi khi lấy lịch sử donate:', err);
          reject(err);
        } else {
          resolve(rows);
        }
      }
    );
  });
}

/**
 * Hàm tương thích: lấy lịch sử donate của một player (giống getPlayerDonateHistory)
 * @param {string} playerId
 * @returns {Promise<Array>}
 */
function getHistory(playerId) {
  return getPlayerDonateHistory(playerId);
}
module.exports.getHistory = getHistory;

// Lấy lịch sử donate của một khách
function getKhachDonateHistory(khachId) {
  return new Promise((resolve, reject) => {
    db.all(
      'SELECT * FROM donate_history WHERE khachId = ? ORDER BY time DESC',
      [khachId],
      (err, rows) => {
        if (err) {
          console.error('Lỗi khi lấy lịch sử donate:', err);
          reject(err);
        } else {
          resolve(rows);
        }
      }
    );
  });
}

// Lấy tất cả lịch sử donate
function getAllDonateHistory() {
  return new Promise((resolve, reject) => {
    db.all(
      'SELECT * FROM donate_history ORDER BY time DESC',
      (err, rows) => {
        if (err) {
          console.error('Lỗi khi lấy tất cả lịch sử donate:', err);
          reject(err);
        } else {
          resolve(rows);
        }
      }
    );
  });
}

// Đóng kết nối database
function close() {
  return new Promise((resolve, reject) => {
    db.close(err => {
      if (err) {
        console.error('Lỗi khi đóng kết nối database donate history:', err);
        reject(err);
      } else {
        console.log('Đã đóng kết nối database donate history');
        resolve();
      }
    });
  });
}

/**
 * Lấy lịch sử donate của một player
 * @param {string} playerId - ID của player
 * @returns {Promise<Array>} - Mảng các lịch sử donate
 */
function getDonateHistoryByPlayerId(playerId) {
  return new Promise((resolve, reject) => {
    db.all(
      `SELECT * FROM donate_history WHERE playerId = ? ORDER BY time DESC`,
      [playerId],
      (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      }
    );
  });
}

// Thêm hàm này vào file donateHistoryDB.js
// Thêm phương thức resetPlayerDonate
async function resetPlayerDonate(playerId) {
  return new Promise((resolve, reject) => {
    db.run(
      `DELETE FROM donate_history WHERE playerId = ?`,
      [playerId],
      function (err) {
        if (err) {
          console.error('Lỗi khi reset donate player:', err);
          reject(err);
        } else {
          console.log(`Đã reset donate của player ${playerId}`);
          resolve(true);
        }
      }
    );
  });
}
module.exports.resetPlayerDonate = resetPlayerDonate;

// Đừng quên export hàm này trong module.exports
/**
 * Reset lịch sử donate của một khách hàng
 * @param {string} khachId - ID của khách hàng
 * @returns {Promise<boolean>} - Kết quả thực hiện
 */
async function resetKhachDonateHistory(khachId) {
  return new Promise((resolve, reject) => {
    db.run(
      `DELETE FROM donate_history WHERE khachId = ?`,
      [khachId],
      function (err) {
        if (err) {
          console.error('Lỗi khi reset lịch sử donate của khách hàng:', err);
          reject(err);
        } else {
          console.log(`Đã reset lịch sử donate của khách hàng ${khachId}`);
          resolve(true);
        }
      }
    );
  });
}

// Xóa tất cả donate của một player
function removeAllDonateOfPlayer(playerId) {
  return new Promise((resolve, reject) => {
    db.run(
      `DELETE FROM donate_history WHERE playerId = ?`,
      [playerId],
      function (err) {
        if (err) reject(err);
        else resolve(this.changes);
      }
    );
  });
}
async function deleteLuongDonateByPlayer(playerId) {
  return new Promise((resolve, reject) => {
    db.run(
      `DELETE FROM donate_history WHERE playerId = ? AND paymentMethod = 'luong'`,
      [playerId],
      function (err) {
        if (err) reject(err);
        else resolve(this.changes);
      }
    );
  });
}
module.exports.deleteLuongDonateByPlayer = deleteLuongDonateByPlayer;
function updateDonateSummary(khachId, playerId, amount) {
  return new Promise((resolve, reject) => {
    db.run(
      `INSERT INTO donate_summary (khachId, playerId, totalDonated)
       VALUES (?, ?, ?)
       ON CONFLICT(khachId, playerId) DO UPDATE SET totalDonated = totalDonated + ?`,
      [khachId, playerId, amount, amount],
      function(err) {
        if (err) reject(err);
        else resolve(true);
      }
    );
  });
}
module.exports.updateDonateSummary = updateDonateSummary;

function getTotalDonated(khachId, playerId) {
  return new Promise((resolve, reject) => {
    db.get(
      `SELECT totalDonated FROM donate_summary WHERE khachId = ? AND playerId = ?`,
      [khachId, playerId],
      (err, row) => {
        if (err) reject(err);
        else resolve(row ? row.totalDonated : 0);
      }
    );
  });
}
module.exports.getTotalDonated = getTotalDonated;

module.exports = {
  addDonateHistory,
  getPlayerDonateHistory,
  getKhachDonateHistory,
  getAllDonateHistory,
  getDonateHistoryByPlayerId,
  resetPlayerDonate,
  resetKhachDonateHistory,
  close,
  removeAllDonateOfPlayer,
  deleteLuongDonateByPlayer,
  getTotalDonated,
  getHistory
};



