const fs = require('fs');
const path = require('path');

// Đường dẫn đến file lưu trữ thông tin giveaway
const giveawayFilePath = path.join(__dirname, 'giveaways.json');

/**
 * Hàm để tải danh sách giveaway
 * @returns {Array} <PERSON>h sách giveaway
 */
function loadGiveaways() {
  try {
    if (fs.existsSync(giveawayFilePath)) {
      const data = fs.readFileSync(giveawayFilePath, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error('Lỗi khi đọc file giveaway:', error);
  }
  return [];
}

/**
 * Hàm để lưu danh sách giveaway
 * @param {Array} giveaways - Danh sách giveaway
 */
function saveGiveaways(giveaways) {
  try {
    // Đảm bảo thư mục data tồn tại
    const dataDir = path.dirname(giveawayFilePath);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    fs.writeFileSync(giveawayFilePath, JSON.stringify(giveaways, null, 2), 'utf8');
  } catch (error) {
    console.error('Lỗi khi lưu file giveaway:', error);
  }
}

/**
 * Thêm giveaway mới
 * @param {Object} giveaway - Thông tin giveaway
 */
function addGiveaway(giveaway) {
  const giveaways = loadGiveaways();
  giveaways.push(giveaway);
  saveGiveaways(giveaways);
}

/**
 * Tìm giveaway theo message ID
 * @param {string} messageId - ID của tin nhắn giveaway
 * @returns {Object|null} Giveaway hoặc null nếu không tìm thấy
 */
function findGiveaway(messageId) {
  const giveaways = loadGiveaways();
  return giveaways.find(g => g.messageId === messageId) || null;
}

/**
 * Cập nhật giveaway
 * @param {string} messageId - ID của tin nhắn giveaway
 * @param {Object} updates - Thông tin cập nhật
 */
function updateGiveaway(messageId, updates) {
  const giveaways = loadGiveaways();
  const index = giveaways.findIndex(g => g.messageId === messageId);
  if (index !== -1) {
    giveaways[index] = { ...giveaways[index], ...updates };
    saveGiveaways(giveaways);
  }
}

/**
 * Lấy tất cả giveaway đang hoạt động
 * @returns {Array} Danh sách giveaway chưa kết thúc
 */
function getActiveGiveaways() {
  const giveaways = loadGiveaways();
  return giveaways.filter(g => !g.ended);
}

/**
 * Lấy tất cả giveaway đã kết thúc
 * @returns {Array} Danh sách giveaway đã kết thúc
 */
function getEndedGiveaways() {
  const giveaways = loadGiveaways();
  return giveaways.filter(g => g.ended);
}

/**
 * Xóa giveaway
 * @param {string} messageId - ID của tin nhắn giveaway
 */
function deleteGiveaway(messageId) {
  const giveaways = loadGiveaways();
  const filteredGiveaways = giveaways.filter(g => g.messageId !== messageId);
  saveGiveaways(filteredGiveaways);
}

module.exports = {
  loadGiveaways,
  saveGiveaways,
  addGiveaway,
  findGiveaway,
  updateGiveaway,
  getActiveGiveaways,
  getEndedGiveaways,
  deleteGiveaway
};
