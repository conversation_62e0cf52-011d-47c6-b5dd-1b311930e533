const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Đường dẫn đến file database
const dbPath = path.join(__dirname, 'love_points.db');

// Tạo kết nối đến database
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('Lỗi khi kết nối đến database love_points:', err.message);
  } else {
    console.log('Đ<PERSON> kết nối đến database love_points.db');
    
    // Tạo bảng love_points nếu chưa tồn tại
    db.run(`
      CREATE TABLE IF NOT EXISTS love_points (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        userId TEXT NOT NULL UNIQUE,
        points INTEGER DEFAULT 0,
        lastLuvTime TEXT,
        createdAt TEXT NOT NULL
      )
    `);
  }
});

const lovePointsDB = {
  // Lấy điểm yêu thương của user
  getLovePoints: (userId) => {
    return new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM love_points WHERE userId = ?',
        [userId],
        (err, row) => {
          if (err) {
            console.error('Lỗi khi lấy điểm yêu thương:', err);
            reject(err);
          } else {
            resolve(row || { userId, points: 0, lastLuvTime: null });
          }
        }
      );
    });
  },

  // Thêm điểm yêu thương
  addLovePoint: (userId) => {
    return new Promise((resolve, reject) => {
      const now = new Date().toISOString();
      
      db.run(
        `INSERT INTO love_points (userId, points, lastLuvTime, createdAt) 
         VALUES (?, 1, ?, ?) 
         ON CONFLICT(userId) DO UPDATE SET 
         points = points + 1, 
         lastLuvTime = ?`,
        [userId, now, now, now],
        function(err) {
          if (err) {
            console.error('Lỗi khi thêm điểm yêu thương:', err);
            reject(err);
          } else {
            resolve(true);
          }
        }
      );
    });
  },

  // Kiểm tra cooldown (1 tiếng = 3600000ms)
  canUseLuv: (userId) => {
    return new Promise((resolve, reject) => {
      db.get(
        'SELECT lastLuvTime FROM love_points WHERE userId = ?',
        [userId],
        (err, row) => {
          if (err) {
            console.error('Lỗi khi kiểm tra cooldown:', err);
            reject(err);
          } else {
            if (!row || !row.lastLuvTime) {
              resolve(true);
            } else {
              const lastTime = new Date(row.lastLuvTime).getTime();
              const now = Date.now();
              const cooldown = 60 * 60 * 1000; // 1 tiếng
              resolve(now - lastTime >= cooldown);
            }
          }
        }
      );
    });
  },

  // Lấy thời gian còn lại của cooldown
  getCooldownTime: (userId) => {
    return new Promise((resolve, reject) => {
      db.get(
        'SELECT lastLuvTime FROM love_points WHERE userId = ?',
        [userId],
        (err, row) => {
          if (err) {
            console.error('Lỗi khi lấy thời gian cooldown:', err);
            reject(err);
          } else {
            if (!row || !row.lastLuvTime) {
              resolve(0);
            } else {
              const lastTime = new Date(row.lastLuvTime).getTime();
              const now = Date.now();
              const cooldown = 60 * 60 * 1000; // 1 tiếng
              const remaining = cooldown - (now - lastTime);
              resolve(Math.max(0, remaining));
            }
          }
        }
      );
    });
  },

  // Reset điểm yêu thương về 0
  resetLovePoints: (userId) => {
    return new Promise((resolve, reject) => {
      db.run(
        'UPDATE love_points SET points = 0, lastLuvTime = NULL WHERE userId = ?',
        [userId],
        function(err) {
          if (err) {
            console.error('Lỗi khi reset điểm yêu thương:', err);
            reject(err);
          } else {
            resolve(this.changes > 0);
          }
        }
      );
    });
  },

  // Đóng kết nối database
  close: () => {
    return new Promise((resolve) => {
      db.close((err) => {
        if (err) {
          console.error('Lỗi khi đóng database love_points:', err.message);
        } else {
          console.log('Đã đóng kết nối database love_points.');
        }
        resolve();
      });
    });
  }
};

module.exports = lovePointsDB;