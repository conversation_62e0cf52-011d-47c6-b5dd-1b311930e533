const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Đường dẫn đến file database
const dbPath = path.join(__dirname, 'marriages.db');

// Tạo kết nối đến database
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('Lỗi khi kết nối đến database marriages:', err.message);
  } else {
    console.log('Đã kết nối đến database marriages.db');
    
    // Tạo bảng marriages nếu chưa tồn tại
    db.run(`
      CREATE TABLE IF NOT EXISTS marriages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user1Id TEXT NOT NULL,
        user2Id TEXT NOT NULL,
        ringId TEXT NOT NULL,
        marriedAt TEXT NOT NULL,
        status TEXT DEFAULT 'active',
        thumbnailUrl TEXT,
        UNIQUE(user1Id),
        UNIQUE(user2Id)
      )
    `);
    
    // Thêm cột thumbnailUrl nếu chưa có (cho database cũ)
    db.run(`ALTER TABLE marriages ADD COLUMN thumbnailUrl TEXT`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Lỗi khi thêm cột thumbnailUrl:', err);
      }
    });
    
    // Thêm cột imageUrl nếu chưa có (cho database cũ)
    db.run(`ALTER TABLE marriages ADD COLUMN imageUrl TEXT`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Lỗi khi thêm cột imageUrl:', err);
      }
    });
    
    // Thêm cột bioText nếu chưa có (cho database cũ)
    db.run(`ALTER TABLE marriages ADD COLUMN bioText TEXT`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Lỗi khi thêm cột bioText:', err);
      }
    });
  }
});

const marriageDB = {
  // Kiểm tra xem user đã kết hôn chưa
  isMarried: (userId) => {
    return new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM marriages WHERE (user1Id = ? OR user2Id = ?) AND status = "active"',
        [userId, userId],
        (err, row) => {
          if (err) {
            console.error('Lỗi khi kiểm tra trạng thái hôn nhân:', err);
            reject(err);
          } else {
            resolve(row ? true : false);
          }
        }
      );
    });
  },

  // Lấy thông tin hôn nhân của user
  getMarriage: (userId) => {
    return new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM marriages WHERE (user1Id = ? OR user2Id = ?) AND status = "active"',
        [userId, userId],
        (err, row) => {
          if (err) {
            console.error('Lỗi khi lấy thông tin hôn nhân:', err);
            reject(err);
          } else {
            resolve(row);
          }
        }
      );
    });
  },

  // Tạo hôn nhân mới
  createMarriage: (user1Id, user2Id, ringId) => {
    return new Promise((resolve, reject) => {
      const marriedAt = new Date().toISOString();
      
      db.run(
        'INSERT INTO marriages (user1Id, user2Id, ringId, marriedAt) VALUES (?, ?, ?, ?)',
        [user1Id, user2Id, ringId, marriedAt],
        function(err) {
          if (err) {
            console.error('Lỗi khi tạo hôn nhân:', err);
            reject(err);
          } else {
            resolve({ id: this.lastID, user1Id, user2Id, ringId, marriedAt });
          }
        }
      );
    });
  },

  // Hủy hôn nhân (divorce)
  divorceMarriage: (userId) => {
    return new Promise((resolve, reject) => {
      db.run(
        'DELETE FROM marriages WHERE (user1Id = ? OR user2Id = ?) AND status = "active"',
        [userId, userId],
        function(err) {
          if (err) {
            console.error('Lỗi khi hủy hôn nhân:', err);
            reject(err);
          } else {
            resolve(this.changes > 0);
          }
        }
      );
    });
  },

  // Cập nhật thumbnail cho marriage
  updateThumbnail: (userId, thumbnailUrl) => {
    return new Promise((resolve, reject) => {
      db.run(
        'UPDATE marriages SET thumbnailUrl = ? WHERE (user1Id = ? OR user2Id = ?) AND status = "active"',
        [thumbnailUrl, userId, userId],
        function(err) {
          if (err) {
            console.error('Lỗi khi cập nhật thumbnail:', err);
            reject(err);
          } else {
            resolve(this.changes > 0);
          }
        }
      );
    });
  },

  // Cập nhật image cho marriage
  updateImage: (userId, imageUrl) => {
    return new Promise((resolve, reject) => {
      db.run(
        'UPDATE marriages SET imageUrl = ? WHERE (user1Id = ? OR user2Id = ?) AND status = "active"',
        [imageUrl, userId, userId],
        function(err) {
          if (err) {
            console.error('Lỗi khi cập nhật image:', err);
            reject(err);
          } else {
            resolve(this.changes > 0);
          }
        }
      );
    });
  },

  // Cập nhật bio cho marriage
  updateBio: (userId, bioText) => {
    return new Promise((resolve, reject) => {
      db.run(
        'UPDATE marriages SET bioText = ? WHERE (user1Id = ? OR user2Id = ?) AND status = "active"',
        [bioText, userId, userId],
        function(err) {
          if (err) {
            console.error('Lỗi khi cập nhật bio:', err);
            reject(err);
          } else {
            resolve(this.changes > 0);
          }
        }
      );
    });
  },

  // Đóng kết nối database
  close: () => {
    return new Promise((resolve) => {
      db.close((err) => {
        if (err) {
          console.error('Lỗi khi đóng database marriages:', err.message);
        } else {
          console.log('Đã đóng kết nối database marriages.');
        }
        resolve();
      });
    });
  }
};

module.exports = marriageDB;