const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// T<PERSON><PERSON> kế<PERSON> n<PERSON><PERSON> đến database
const db = new sqlite3.Database(path.join(__dirname, 'player_bonus_salary.db'));

// T<PERSON>o bảng nếu chưa có
db.serialize(() => {
  db.run(`
    CREATE TABLE IF NOT EXISTS player_bonus_salary (
      playerId TEXT PRIMARY KEY,
      totalBonus INTEGER NOT NULL DEFAULT 0
    )
  `);
});

// Cộng thưởng lương cho player
function addPlayerBonusSalary(playerId, amount) {
  return new Promise((resolve, reject) => {
    db.run(
      `INSERT INTO player_bonus_salary (playerId, totalBonus)
       VALUES (?, ?)
       ON CONFLICT(playerId) DO UPDATE SET totalBonus = totalBonus + ?`,
      [playerId, amount, amount],
      function(err) {
        if (err) reject(err);
        else resolve(true);
      }
    );
  });
}

// <PERSON><PERSON>y tổng thưởng lương của player
function getPlayerBonusSalary(playerId) {
  return new Promise((resolve, reject) => {
    db.get(
      `SELECT totalBonus FROM player_bonus_salary WHERE playerId = ?`,
      [playerId],
      (err, row) => {
        if (err) reject(err);
        else resolve(row ? row.totalBonus : 0);
      }
    );
  });
}

// Reset thưởng lương của player về 0
function resetPlayerBonusSalary(playerId) {
  return new Promise((resolve, reject) => {
    db.run(
      `UPDATE player_bonus_salary SET totalBonus = 0 WHERE playerId = ?`,
      [playerId],
      function(err) {
        if (err) reject(err);
        else resolve(true);
      }
    );
  });
}

// Trừ thưởng lương của player (nếu cần)
function subtractPlayerBonusSalary(playerId, amount) {
  return new Promise((resolve, reject) => {
    db.run(
      `UPDATE player_bonus_salary SET totalBonus = MAX(0, totalBonus - ?) WHERE playerId = ?`,
      [amount, playerId],
      function(err) {
        if (err) reject(err);
        else resolve(true);
      }
    );
  });
}

// Lấy tất cả player có thưởng lương
function getAllPlayerBonusSalary() {
  return new Promise((resolve, reject) => {
    db.all(
      `SELECT * FROM player_bonus_salary WHERE totalBonus > 0 ORDER BY totalBonus DESC`,
      [],
      (err, rows) => {
        if (err) reject(err);
        else resolve(rows || []);
      }
    );
  });
}

// Reset tất cả thưởng lương về 0
function resetAllPlayerBonusSalary() {
  return new Promise((resolve, reject) => {
    db.run(
      `UPDATE player_bonus_salary SET totalBonus = 0`,
      [],
      function(err) {
        if (err) reject(err);
        else resolve(true);
      }
    );
  });
}

module.exports = {
  addPlayerBonusSalary,
  getPlayerBonusSalary,
  resetPlayerBonusSalary,
  subtractPlayerBonusSalary,
  getAllPlayerBonusSalary,
  resetAllPlayerBonusSalary
};
