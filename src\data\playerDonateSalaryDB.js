const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const db = new sqlite3.Database(path.join(__dirname, 'player_donate_salary.db'));

// Tạo bảng nếu chưa có
db.serialize(() => {
  db.run(`
    CREATE TABLE IF NOT EXISTS player_donate_salary (
      playerId TEXT PRIMARY KEY,
      totalDonate INTEGER NOT NULL DEFAULT 0
    )
  `);
});

// Cộng dồn donate cho player khi có donate mới
function addPlayerDonateSalary(playerId, amount) {
  return new Promise((resolve, reject) => {
    db.run(
      `INSERT INTO player_donate_salary (playerId, totalDonate)
       VALUES (?, ?)
       ON CONFLICT(playerId) DO UPDATE SET totalDonate = totalDonate + ?`,
      [playerId, amount, amount],
      function(err) {
        if (err) reject(err);
        else resolve(true);
      }
    );
  });
}

// L<PERSON>y tổng donate lương của player
function getPlayerDonateSalary(playerId) {
  return new Promise((resolve, reject) => {
    db.get(
      `SELECT totalDonate FROM player_donate_salary WHERE playerId = ?`,
      [playerId],
      (err, row) => {
        if (err) reject(err);
        else resolve(row ? row.totalDonate : 0);
      }
    );
  });
}

// Reset donate lương của player về 0
function resetPlayerDonateSalary(playerId) {
  return new Promise((resolve, reject) => {
    db.run(
      `UPDATE player_donate_salary SET totalDonate = 0 WHERE playerId = ?`,
      [playerId],
      function(err) {
        if (err) reject(err);
        else resolve(true);
      }
    );
  });
}

// Trừ donate lương của player (cho trường hợp hoàn bill)
function subtractPlayerDonateSalary(playerId, amount) {
  return new Promise((resolve, reject) => {
    db.run(
      `UPDATE player_donate_salary SET totalDonate = MAX(0, totalDonate - ?) WHERE playerId = ?`,
      [amount, playerId],
      function(err) {
        if (err) reject(err);
        else resolve(true);
      }
    );
  });
}

module.exports = {
  addPlayerDonateSalary,
  getPlayerDonateSalary,
  resetPlayerDonateSalary,
  subtractPlayerDonateSalary // Thêm dòng này
};