const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const dbPath = path.join(__dirname, 'playerHistory.sqlite');
const db = new sqlite3.Database(dbPath);
const topStarDB = require('./topStarDB');

// Tạo bảng nếu chưa có
db.serialize(() => {
  db.run(`CREATE TABLE IF NOT EXISTS history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    playerId TEXT,
    khach TEXT,
    hours INTEGER,
    total INTEGER,
    received INTEGER,
    type TEXT,
    pricePerHour INTEGER,
    time INTEGER,
    billId TEXT,
    isPaid INTEGER DEFAULT 0
  )`);
});

// Kiểm tra và thêm cột isPaid nếu chưa có
db.get("PRAGMA table_info(history)", (err, info) => {
  if (err) return;
  db.all("PRAGMA table_info(history)", (err, columns) => {
    if (err) return;
    const hasIsPaid = columns.some(col => col.name === "isPaid");
    if (!hasIsPaid) {
      db.run("ALTER TABLE history ADD COLUMN isPaid INTEGER DEFAULT 0");
    }
  });
});

// Cập nhật function addHistory
function addHistory(playerId, khach, hours, total, received, type, pricePerHour, billId) {
  return new Promise((resolve, reject) => {
    const time = Date.now();
    db.run(
      `INSERT INTO history (playerId, khach, hours, total, received, type, pricePerHour, time, billId, isPaid) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 0)`,
      [playerId, khach, hours, total, received, type, pricePerHour, time, billId],
      async function (err) {
        if (err) {
          console.error('Lỗi khi thêm lịch sử:', err);
          reject(err);
        } else {
          console.log(`Đã thêm lịch sử cho player ${playerId}`);
          
          // Cập nhật TopStar
          try {
            console.log(`🔄 Bắt đầu cập nhật TopStar cho player ${playerId} với ${hours} giờ`);
            await topStarDB.updatePlayerTopStar(playerId, hours);
            console.log(`✅ Đã cập nhật TopStar thành công cho player ${playerId}`);
          } catch (topStarError) {
            console.error('❌ Lỗi khi cập nhật TopStar:', topStarError);
          }
          
          resolve(this.lastID);
        }
      }
    );
  });
}


function getHistory(playerId) {
  return new Promise((resolve, reject) => {
    db.all(
      `SELECT * FROM history WHERE playerId = ? AND (isPaid = 0 OR isPaid IS NULL) ORDER BY time DESC`,
      [playerId],
      (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      }
    );
  });
}

/**
 * Lấy toàn bộ lịch sử book của một player (bao gồm cả đã thanh toán)
 * @param {string} playerId - ID của player
 * @returns {Promise<Array>} - Mảng các lịch sử book
 */
function getAllPlayerHistory(playerId) {
  return new Promise((resolve, reject) => {
    db.all(
      `SELECT * FROM history WHERE playerId = ? ORDER BY time DESC`,
      [playerId],
      (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      }
    );
  });
}

function removeHistory(playerId, bill) {
  return new Promise((resolve, reject) => {
    db.run(
      `DELETE FROM history WHERE playerId = ? AND khach = ? AND hours = ? AND total = ? AND (type = ? OR type IS NULL) AND (billId = ? OR billId IS NULL)`,
      [
        playerId,
        bill.khach,
        bill.hours,
        bill.total,
        bill.type || null,
        bill.billId || null
      ],
      function (err) {
        if (err) reject(err);
        else resolve(this.changes > 0);
      }
    );
  });
}

// Thêm hàm này vào file playerHistoryDB.js
/**
 * Reset lương của player bằng cách đánh dấu tất cả lịch sử là đã thanh toán
 * @param {string} playerId - ID của player
 * @returns {Promise<boolean>} - Kết quả thực hiện
 */
async function resetPlayerSalary(playerId) {
  return new Promise((resolve, reject) => {
    db.run(
      `UPDATE history SET isPaid = 1 WHERE playerId = ?`,
      [playerId],
      function (err) {
        if (err) {
          console.error('Lỗi khi reset lương player:', err);
          reject(err);
        } else {
          console.log(`Đã reset lương của player ${playerId}`);
          resolve(true);
        }
      }
    );
  });
}

// Thêm hàm để lấy lịch sử book của khách hàng
/**
 * Lấy lịch sử book của khách hàng
 * @param {string} customerId - ID của khách hàng
 * @param {boolean} includeAllHistory - Có lấy cả lịch sử đã thanh toán không
 * @returns {Promise<Array>} - Mảng các lịch sử book
 */
async function getCustomerHistory(customerId, includeAllHistory = false) {
  return new Promise((resolve, reject) => {
    let query = `SELECT * FROM history WHERE khach = ?`;
    if (!includeAllHistory) {
      query += ` AND (isPaid = 0 OR isPaid IS NULL)`;
    }
    query += ` ORDER BY time DESC`;
    
    db.all(
      query,
      [customerId],
      (err, rows) => {
        if (err) {
          console.error('Lỗi khi lấy lịch sử book của khách hàng:', err);
          reject(err);
        } else {
          console.log(`Tìm thấy ${rows.length} bản ghi cho khách hàng ${customerId}`);
          resolve(rows);
        }
      }
    );
  });
}

function getPlayerHistory(playerId) {
  return new Promise((resolve, reject) => {
    db.all(
      `SELECT * FROM history WHERE playerId = ? ORDER BY time DESC`,
      [playerId],
      (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      }
    );
  });
}

function removeAllHistoryOfPlayer(playerId) {
  return new Promise((resolve, reject) => {
    db.run(
      `DELETE FROM history WHERE playerId = ?`,
      [playerId],
      function (err) {
        if (err) reject(err);
        else resolve(this.changes);
      }
    );
  });
}
// Thêm hàm này vào cuối file, trước module.exports
function removeHistoryByBillId(playerId, billId) {
  return new Promise((resolve, reject) => {
    db.run(
      `DELETE FROM history WHERE playerId = ? AND billId = ?`,
      [playerId, billId],
      function (err) {
        if (err) {
          console.error('Lỗi khi xóa lịch sử theo billId:', err);
          reject(err);
        } else {
          console.log(`Đã xóa ${this.changes} record cho player ${playerId}, billId ${billId}`);
          resolve(this.changes > 0);
        }
      }
    );
  });
}

/**
 * Cập nhật trạng thái hoàn bill cho một lịch sử cụ thể
 * @param {string} playerId - ID của player
 * @param {string} billId - ID của bill
 * @param {boolean} isRefunded - Trạng thái hoàn bill
 * @returns {Promise<boolean>} - Kết quả thực hiện
 */
function updateHistoryRefunded(playerId, billId, isRefunded) {
  return new Promise((resolve, reject) => {
    // Kiểm tra và thêm cột isRefunded nếu chưa có
    db.get("PRAGMA table_info(history)", (err, info) => {
      if (err) {
        reject(err);
        return;
      }
      
      db.all("PRAGMA table_info(history)", (err, columns) => {
        if (err) {
          reject(err);
          return;
        }
        
        const hasIsRefunded = columns.some(col => col.name === "isRefunded");
        
        if (!hasIsRefunded) {
          // Thêm cột isRefunded nếu chưa có
          db.run("ALTER TABLE history ADD COLUMN isRefunded INTEGER DEFAULT 0", (err) => {
            if (err) {
              reject(err);
              return;
            }
            // Sau khi thêm cột, thực hiện update
            performUpdate();
          });
        } else {
          // Cột đã tồn tại, thực hiện update ngay
          performUpdate();
        }
      });
    });
    
    function performUpdate() {
      db.run(
        `UPDATE history SET isRefunded = ? WHERE playerId = ? AND billId = ?`,
        [isRefunded ? 1 : 0, playerId, billId],
        function (err) {
          if (err) {
            console.error('Lỗi khi cập nhật trạng thái hoàn bill:', err);
            reject(err);
          } else {
            console.log(`Đã cập nhật trạng thái hoàn bill cho player ${playerId}, billId ${billId}`);
            resolve(this.changes > 0);
          }
        }
      );
    }
  });
}

// Thêm hàm này trước module.exports
/**
 * Lấy top players được book nhiều nhất
 * @param {number} limit - Số lượng players cần lấy (mặc định 10, tối đa 50)
 * @returns {Promise<Array>} - Mảng các players với tổng giờ được book
 */
function getTopPlayersByBookedHours(limit = 10) {
  return new Promise((resolve, reject) => {
    // Đảm bảo limit không vượt quá 50
    const safeLimit = Math.min(Math.max(limit, 1), 50);
    
    db.all(
      `SELECT playerId, SUM(hours) as totalHours, COUNT(*) as totalBookings
       FROM history 
       WHERE (isRefunded IS NULL OR isRefunded = 0)
       GROUP BY playerId 
       HAVING totalHours > 0
       ORDER BY totalHours DESC 
       LIMIT ?`,
      [safeLimit],
      (err, rows) => {
        if (err) {
          console.error('Lỗi khi lấy top players được book:', err);
          reject(err);
        } else {
          resolve(rows);
        }
      }
    );
  });
}

/**
 * Lấy toàn bộ lịch sử book của tất cả players
 * @returns {Promise<Array>} - Mảng tất cả lịch sử book
 */
function getAllHistory() {
  return new Promise((resolve, reject) => {
    db.all(
      `SELECT * FROM history ORDER BY time DESC`,
      [],
      (err, rows) => {
        if (err) reject(err);
        else resolve(rows || []);
      }
    );
  });
}

/**
 * Lấy thông tin bill từ billId
 * @param {string} billId - ID của bill
 * @returns {Promise<Object|null>} - Thông tin bill hoặc null nếu không tìm thấy
 */
function getBillInfo(billId) {
  return new Promise((resolve, reject) => {
    db.get(
      `SELECT * FROM history WHERE billId = ? LIMIT 1`,
      [billId],
      (err, row) => {
        if (err) {
          console.error('Lỗi khi lấy thông tin bill:', err);
          reject(err);
        } else {
          resolve(row || null);
        }
      }
    );
  });
}

module.exports = {
  addHistory,
  getHistory,
  removeHistory,
  removeHistoryByBillId,
  getAllPlayerHistory,
  resetPlayerSalary,
  getCustomerHistory,
  getPlayerHistory,
  removeAllHistoryOfPlayer,
  updateHistoryRefunded,
  getTopPlayersByBookedHours, // Thêm hàm mới
  getAllHistory, // Thêm hàm getAllHistory
  getBillInfo, // Thêm hàm getBillInfo
};