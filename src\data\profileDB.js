const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const dbPath = path.join(__dirname, 'profiles.db');

// Kiểm tra và sao lưu file database nếu tồn tại
if (fs.existsSync(dbPath)) {
  const backupPath = path.join(__dirname, `profiles_backup_${Date.now()}.db`);
  try {
    fs.copyFileSync(dbPath, backupPath);
    console.log(`Đã sao lưu database vào: ${backupPath}`);
  } catch (err) {
    console.error('Lỗi khi sao lưu database:', err);
  }
}

// Khởi tạo database với các tùy chọn an toàn
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('Lỗi khi mở database:', err);
    // Nếu không mở được, thử xóa và tạo mới
    if (err.code === 'SQLITE_CORRUPT') {
      try {
        fs.unlinkSync(dbPath);
        console.log('Đã xóa file database bị hỏng');
        // Tạo lại database
        const newDb = new sqlite3.Database(dbPath);
        createTables(newDb);
        console.log('Đã tạo lại database mới');
      } catch (unlinkErr) {
        console.error('Không thể xóa file database bị hỏng:', unlinkErr);
      }
    }
  } else {
    console.log('Đã kết nối thành công đến database');
    createTables(db);
  }
});

// Hàm tạo bảng
function createTables(database) {
  database.serialize(() => {
    database.run(`CREATE TABLE IF NOT EXISTS profiles (
      playerType TEXT,
      playerNumber INTEGER,
      name TEXT,
      mention TEXT,
      location TEXT,
      game TEXT,
      cam TEXT,
      bio TEXT,
      thumbnail TEXT,
      image TEXT,
      color INTEGER,
      PRIMARY KEY (playerType, playerNumber)
    )`);

    // Thêm cột mention nếu chưa có (cho database cũ)
    database.run(`ALTER TABLE profiles ADD COLUMN mention TEXT`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Lỗi khi thêm cột mention:', err);
      }
    });
    console.log('Đã khởi tạo bảng profiles');
  });
}

// Các hàm thao tác với database
const profileDB = {
  // Thêm hoặc cập nhật profile
  addOrUpdateProfile: (profile) => {
    return new Promise((resolve, reject) => {
      // Làm sạch dữ liệu trước khi lưu
      const cleanProfile = {
        ...profile,
        mention: profile.mention ? profile.mention.trim() : null,
        thumbnail: profile.thumbnail ? profile.thumbnail.trim() : null,
        image: profile.image ? profile.image.trim() : null
      };
      
      db.run(
        `INSERT OR REPLACE INTO profiles (playerType, playerNumber, name, mention, location, game, cam, bio, thumbnail, image, color)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          cleanProfile.playerType,
          cleanProfile.playerNumber,
          cleanProfile.name,
          cleanProfile.mention,
          cleanProfile.location,
          cleanProfile.game,
          cleanProfile.cam,
          cleanProfile.bio,
          cleanProfile.thumbnail,
          cleanProfile.image,
          cleanProfile.color || null
        ],
        function(err) {
          if (err) {
            console.error('Lỗi khi thêm/cập nhật profile:', err);
            reject(err);
          } else {
            console.log(`Đã thêm/cập nhật profile ${cleanProfile.playerType} ${cleanProfile.playerNumber}`);
            resolve(this.lastID);
          }
        }
      );
    });
  },

  // Lấy profile theo loại và số
  getProfile: (playerType, playerNumber) => {
    return new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM profiles WHERE playerType = ? AND playerNumber = ?',
        [playerType, playerNumber],
        (err, row) => {
          if (err) {
            console.error('Lỗi khi lấy profile:', err);
            reject(err);
          } else {
            resolve(row);
          }
        }
      );
    });
  },

  // Lấy tất cả profile theo loại
  getAllProfilesByType: (playerType) => {
    return new Promise((resolve, reject) => {
      db.all(
        'SELECT * FROM profiles WHERE playerType = ? ORDER BY playerNumber',
        [playerType],
        (err, rows) => {
          if (err) {
            console.error('Lỗi khi lấy danh sách profile:', err);
            reject(err);
          } else {
            console.log(`Đã lấy ${rows.length} profile loại ${playerType}`);
            resolve(rows);
          }
        }
      );
    });
  },

  // Xóa profile
  deleteProfile: (playerType, playerNumber) => {
    return new Promise((resolve, reject) => {
      db.run(
        'DELETE FROM profiles WHERE playerType = ? AND playerNumber = ?',
        [playerType, playerNumber],
        function(err) {
          if (err) {
            console.error('Lỗi khi xóa profile:', err);
            reject(err);
          } else {
            console.log(`Đã xóa profile ${playerType} ${playerNumber}`);
            resolve(this.changes);
          }
        }
      );
    });
  },

  // Xóa tất cả profile theo loại
  deleteAllProfilesByType: (playerType) => {
    return new Promise((resolve, reject) => {
      db.run(
        'DELETE FROM profiles WHERE playerType = ?',
        [playerType],
        function(err) {
          if (err) {
            console.error('Lỗi khi xóa tất cả profile loại', playerType, ':', err);
            reject(err);
          } else {
            console.log(`Đã xóa ${this.changes} profile loại ${playerType}`);
            resolve(this.changes);
          }
        }
      );
    });
  },
  
  // Cập nhật ảnh profile
  updateProfileImage: (playerType, playerNumber, thumbnail, image) => {
    return new Promise((resolve, reject) => {
      // Đầu tiên lấy thông tin profile hiện tại
      db.get(
        'SELECT thumbnail, image FROM profiles WHERE playerType = ? AND playerNumber = ?',
        [playerType, playerNumber],
        (err, row) => {
          if (err) {
            reject(err);
            return;
          }
          
          // Nếu không tìm thấy profile
          if (!row) {
            reject(new Error(`Không tìm thấy profile ${playerType} ${playerNumber}`));
            return;
          }
          
          // Giữ nguyên giá trị cũ nếu không có giá trị mới
          const newThumbnail = thumbnail !== null ? thumbnail : row.thumbnail;
          const newImage = image !== null ? image : row.image;
          
          // Cập nhật với giá trị mới hoặc giữ nguyên giá trị cũ
          db.run(
            `UPDATE profiles SET thumbnail = ?, image = ? WHERE playerType = ? AND playerNumber = ?`,
            [newThumbnail, newImage, playerType, playerNumber],
            function (err) {
              if (err) reject(err);
              else resolve();
            }
          );
        }
      );
    });
  },
  
  // Cập nhật màu profile
  updateProfileColor: (playerType, playerNumber, color) => {
    return new Promise((resolve, reject) => {
      db.run(
        `UPDATE profiles SET color = ? WHERE playerType = ? AND playerNumber = ?`,
        [color, playerType, playerNumber],
        function (err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });
  },
  
  // Cập nhật thông tin profile
  updateProfileInfo: (playerType, playerNumber, name, location, bio, game, cam) => {
    return new Promise((resolve, reject) => {
      db.run(
        `UPDATE profiles SET name = ?, location = ?, bio = ?, game = ?, cam = ? WHERE playerType = ? AND playerNumber = ?`,
        [name, location, bio, game, cam, playerType, playerNumber],
        function (err) {
          if (err) {
            console.error('Lỗi khi cập nhật thông tin profile:', err);
            reject(err);
          } else {
            console.log(`Đã cập nhật thông tin profile ${playerType} ${playerNumber}`);
            resolve();
          }
        }
      );
    });
  },

  // Đóng kết nối database khi tắt bot
  close: () => {
    return new Promise((resolve, reject) => {
      db.close((err) => {
        if (err) {
          console.error('Lỗi khi đóng database:', err);
          reject(err);
        } else {
          console.log('Đã đóng kết nối database');
          resolve();
        }
      });
    });
  }
};

module.exports = profileDB;