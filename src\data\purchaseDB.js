const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const dbPath = path.join(__dirname, 'purchases.db');
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('Lỗi khi kết nối đến database:', err.message);
  } else {
    db.run(`
      CREATE TABLE IF NOT EXISTS purchases (
        userId TEXT,
        itemId TEXT,
        quantity INTEGER,
        purchasedAt TEXT
      )
    `);
  }
});

const purchaseDB = {
  addPurchase: (userId, itemId, quantity) => {
    return new Promise((resolve, reject) => {
      const purchasedAt = new Date().toISOString();
      db.run(
        'INSERT INTO purchases (userId, itemId, quantity, purchasedAt) VALUES (?, ?, ?, ?)',
        [userId, itemId, quantity, purchasedAt],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });
  },
  
  getInventory: (userId) => {
    return new Promise((resolve, reject) => {
      db.all('SELECT itemId, SUM(quantity) as total FROM purchases WHERE userId = ? GROUP BY itemId', [userId], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
  },

  // Lấy thông tin chi tiết inventory của user
  getUserPurchases: (userId) => {
    return new Promise((resolve, reject) => {
      db.all(
        'SELECT itemId as item_id, SUM(quantity) as quantity FROM purchases WHERE userId = ? GROUP BY itemId',
        [userId],
        (err, rows) => {
          if (err) {
            console.error('Lỗi khi lấy inventory:', err);
            reject(err);
          } else {
            resolve(rows);
          }
        }
      );
    });
  },

  // Trừ item từ inventory
  subtractItem: (userId, itemId, quantity) => {
    return new Promise((resolve, reject) => {
      const purchasedAt = new Date().toISOString();
      
      // Thêm record với quantity âm để trừ item
      db.run(
        'INSERT INTO purchases (userId, itemId, quantity, purchasedAt) VALUES (?, ?, ?, ?)',
        [userId, itemId, -quantity, purchasedAt],
        function(err) {
          if (err) {
            console.error('Lỗi khi trừ item:', err);
            reject(err);
          } else {
            resolve();
          }
        }
      );
    });
  },

  // Kiểm tra xem user có đủ item không
  hasItem: (userId, itemId, requiredQuantity = 1) => {
    return new Promise((resolve, reject) => {
      db.get(
        'SELECT SUM(quantity) as total FROM purchases WHERE userId = ? AND itemId = ?',
        [userId, itemId],
        (err, row) => {
          if (err) {
            console.error('Lỗi khi kiểm tra item:', err);
            reject(err);
          } else {
            const currentQuantity = row?.total || 0;
            resolve(currentQuantity >= requiredQuantity);
          }
        }
      );
    });
  }
};

module.exports = purchaseDB;