const fs = require('fs');
const path = require('path');

const dataPath = path.join(__dirname, 'react_bill_data.json');

// Khởi tạo file data nếu chưa tồn tại
if (!fs.existsSync(dataPath)) {
  fs.writeFileSync(dataPath, JSON.stringify({}, null, 2));
}

class ReactBillDB {
  // Lưu thông tin kênh gốc cho một message
  static saveOriginalChannel(messageId, originalChannelId, originalMessageId = null) {
    try {
      const data = this.loadData();
      data[messageId] = {
        originalChannelId: originalChannelId,
        originalMessageId: originalMessageId,
        createdAt: new Date().toISOString()
      };
      fs.writeFileSync(dataPath, JSON.stringify(data, null, 2));
      return true;
    } catch (error) {
      console.error('Lỗi khi lưu thông tin kênh gốc:', error);
      return false;
    }
  }

  // L<PERSON>y thông tin kênh gốc của một message
  static getOriginalChannel(messageId) {
    try {
      const data = this.loadData();
      return data[messageId] || null;
    } catch (error) {
      console.error('Lỗi khi lấy thông tin kênh gốc:', error);
      return null;
    }
  }

  // Cập nhật message ID gốc
  static updateOriginalMessageId(messageId, originalMessageId) {
    try {
      const data = this.loadData();
      if (data[messageId]) {
        data[messageId].originalMessageId = originalMessageId;
        data[messageId].updatedAt = new Date().toISOString();
        fs.writeFileSync(dataPath, JSON.stringify(data, null, 2));
        return true;
      }
      return false;
    } catch (error) {
      console.error('Lỗi khi cập nhật message ID gốc:', error);
      return false;
    }
  }

  // Xóa thông tin khi không cần thiết
  static removeData(messageId) {
    try {
      const data = this.loadData();
      delete data[messageId];
      fs.writeFileSync(dataPath, JSON.stringify(data, null, 2));
      return true;
    } catch (error) {
      console.error('Lỗi khi xóa dữ liệu:', error);
      return false;
    }
  }

  // Load dữ liệu từ file
  static loadData() {
    try {
      const rawData = fs.readFileSync(dataPath, 'utf8');
      return JSON.parse(rawData);
    } catch (error) {
      console.error('Lỗi khi đọc file data:', error);
      return {};
    }
  }

  // Dọn dẹp dữ liệu cũ (tùy chọn)
  static cleanup(daysOld = 30) {
    try {
      const data = this.loadData();
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);
      
      let cleaned = 0;
      Object.keys(data).forEach(messageId => {
        const createdAt = new Date(data[messageId].createdAt);
        if (createdAt < cutoffDate) {
          delete data[messageId];
          cleaned++;
        }
      });
      
      if (cleaned > 0) {
        fs.writeFileSync(dataPath, JSON.stringify(data, null, 2));
        console.log(`Đã dọn dẹp ${cleaned} bản ghi cũ`);
      }
      
      return cleaned;
    } catch (error) {
      console.error('Lỗi khi dọn dẹp dữ liệu:', error);
      return 0;
    }
  }
}

module.exports = ReactBillDB;