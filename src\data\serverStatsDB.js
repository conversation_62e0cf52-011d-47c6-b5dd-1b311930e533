const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Tạo kết nối đến database
const db = new sqlite3.Database(path.join(__dirname, 'server_stats.db'));

// Tạo bảng nếu chưa có
db.serialize(() => {
  // Bảng thống kê booking tổng
  db.run(`
    CREATE TABLE IF NOT EXISTS server_booking_stats (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      totalHours INTEGER NOT NULL DEFAULT 0,
      totalRevenue INTEGER NOT NULL DEFAULT 0,
      bookCount INTEGER NOT NULL DEFAULT 0,
      gameCount INTEGER NOT NULL DEFAULT 0,
      oncamCount INTEGER NOT NULL DEFAULT 0,
      lastUpdated INTEGER NOT NULL
    )
  `);

  // Bảng thống kê donate tổng
  db.run(`
    CREATE TABLE IF NOT EXISTS server_donate_stats (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      totalRevenue INTEGER NOT NULL DEFAULT 0,
      donateCount INTEGER NOT NULL DEFAULT 0,
      lastUpdated INTEGER NOT NULL
    )
  `);

  // Khởi tạo dữ liệu mặc định nếu chưa có
  db.get(`SELECT COUNT(*) as count FROM server_booking_stats`, (err, row) => {
    if (!err && row.count === 0) {
      db.run(`INSERT INTO server_booking_stats (totalHours, totalRevenue, bookCount, gameCount, oncamCount, lastUpdated) 
              VALUES (0, 0, 0, 0, 0, ?)`, [Date.now()]);
    }
  });

  db.get(`SELECT COUNT(*) as count FROM server_donate_stats`, (err, row) => {
    if (!err && row.count === 0) {
      db.run(`INSERT INTO server_donate_stats (totalRevenue, donateCount, lastUpdated) 
              VALUES (0, 0, ?)`, [Date.now()]);
    }
  });
});

/**
 * Cập nhật thống kê booking server
 * @param {number} hours - Số giờ booking
 * @param {number} revenue - Doanh thu (trước chiết khấu)
 * @param {string} type - Loại booking ('book', 'game', 'oncam')
 * @returns {Promise<boolean>}
 */
function updateBookingStats(hours, revenue, type) {
  return new Promise((resolve, reject) => {
    let countField = 'bookCount';
    if (type === 'game') countField = 'gameCount';
    else if (type === 'oncam') countField = 'oncamCount';

    db.run(
      `UPDATE server_booking_stats SET 
       totalHours = totalHours + ?,
       totalRevenue = totalRevenue + ?,
       ${countField} = ${countField} + 1,
       lastUpdated = ?
       WHERE id = 1`,
      [hours, revenue, Date.now()],
      function(err) {
        if (err) {
          console.error('Lỗi khi cập nhật thống kê booking:', err);
          reject(err);
        } else {
          resolve(true);
        }
      }
    );
  });
}

/**
 * Cập nhật thống kê donate server
 * @param {number} revenue - Doanh thu donate (trước chiết khấu)
 * @returns {Promise<boolean>}
 */
function updateDonateStats(revenue) {
  return new Promise((resolve, reject) => {
    db.run(
      `UPDATE server_donate_stats SET 
       totalRevenue = totalRevenue + ?,
       donateCount = donateCount + 1,
       lastUpdated = ?
       WHERE id = 1`,
      [revenue, Date.now()],
      function(err) {
        if (err) {
          console.error('Lỗi khi cập nhật thống kê donate:', err);
          reject(err);
        } else {
          resolve(true);
        }
      }
    );
  });
}

/**
 * Lấy thống kê booking server
 * @returns {Promise<Object>}
 */
function getBookingStats() {
  return new Promise((resolve, reject) => {
    db.get(
      `SELECT * FROM server_booking_stats WHERE id = 1`,
      [],
      (err, row) => {
        if (err) {
          console.error('Lỗi khi lấy thống kê booking:', err);
          reject(err);
        } else {
          resolve(row || {
            totalHours: 0,
            totalRevenue: 0,
            bookCount: 0,
            gameCount: 0,
            oncamCount: 0
          });
        }
      }
    );
  });
}

/**
 * Lấy thống kê donate server
 * @returns {Promise<Object>}
 */
function getDonateStats() {
  return new Promise((resolve, reject) => {
    db.get(
      `SELECT * FROM server_donate_stats WHERE id = 1`,
      [],
      (err, row) => {
        if (err) {
          console.error('Lỗi khi lấy thống kê donate:', err);
          reject(err);
        } else {
          resolve(row || {
            totalRevenue: 0,
            donateCount: 0
          });
        }
      }
    );
  });
}

/**
 * Đồng bộ dữ liệu từ database hiện tại (chạy 1 lần để migration)
 * @param {Array} bookingHistory - Lịch sử booking
 * @param {Array} donateHistory - Lịch sử donate
 * @returns {Promise<boolean>}
 */
function syncFromExistingData(bookingHistory, donateHistory) {
  return new Promise(async (resolve, reject) => {
    try {
      // Reset về 0
      await new Promise((res, rej) => {
        db.run(`UPDATE server_booking_stats SET totalHours = 0, totalRevenue = 0, bookCount = 0, gameCount = 0, oncamCount = 0, lastUpdated = ? WHERE id = 1`, 
               [Date.now()], (err) => err ? rej(err) : res());
      });

      await new Promise((res, rej) => {
        db.run(`UPDATE server_donate_stats SET totalRevenue = 0, donateCount = 0, lastUpdated = ? WHERE id = 1`, 
               [Date.now()], (err) => err ? rej(err) : res());
      });

      // Đồng bộ booking
      for (const entry of bookingHistory) {
        const originalAmount = Math.round(entry.received / 0.8); // Tính ngược doanh thu gốc
        await updateBookingStats(entry.hours, originalAmount, entry.type);
      }

      // Đồng bộ donate
      for (const entry of donateHistory) {
        if (entry.amount > 0 && entry.paymentMethod !== 'luong') {
          const originalAmount = Math.round(entry.received / 0.9); // Tính ngược doanh thu gốc
          await updateDonateStats(originalAmount);
        }
      }

      resolve(true);
    } catch (error) {
      console.error('Lỗi khi đồng bộ dữ liệu:', error);
      reject(error);
    }
  });
}

/**
 * Trừ thống kê booking server (khi hoàn bill)
 * @param {number} hours - Số giờ booking cần trừ
 * @param {number} revenue - Doanh thu cần trừ (trước chiết khấu)
 * @param {string} type - Loại booking ('book', 'game', 'oncam')
 * @returns {Promise<boolean>}
 */
function subtractBookingStats(hours, revenue, type) {
  return new Promise((resolve, reject) => {
    let countField = 'bookCount';
    if (type === 'game') countField = 'gameCount';
    else if (type === 'oncam') countField = 'oncamCount';

    db.run(
      `UPDATE server_booking_stats SET
       totalHours = MAX(0, totalHours - ?),
       totalRevenue = MAX(0, totalRevenue - ?),
       ${countField} = MAX(0, ${countField} - 1),
       lastUpdated = ?
       WHERE id = 1`,
      [hours, revenue, Date.now()],
      function(err) {
        if (err) {
          console.error('Lỗi khi trừ thống kê booking:', err);
          reject(err);
        } else {
          console.log(`✅ Đã trừ thống kê server: -${hours}h, -${revenue} VNĐ, type: ${type}`);
          resolve(true);
        }
      }
    );
  });
}

/**
 * Reset toàn bộ thống kê server (chỉ dành cho admin)
 * @returns {Promise<boolean>}
 */
function resetAllStats() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      db.run(`UPDATE server_booking_stats SET totalHours = 0, totalRevenue = 0, bookCount = 0, gameCount = 0, oncamCount = 0, lastUpdated = ? WHERE id = 1`,
             [Date.now()]);
      db.run(`UPDATE server_donate_stats SET totalRevenue = 0, donateCount = 0, lastUpdated = ? WHERE id = 1`,
             [Date.now()], function(err) {
        if (err) {
          console.error('Lỗi khi reset thống kê:', err);
          reject(err);
        } else {
          resolve(true);
        }
      });
    });
  });
}

module.exports = {
  updateBookingStats,
  updateDonateStats,
  getBookingStats,
  getDonateStats,
  syncFromExistingData,
  resetAllStats,
  subtractBookingStats
};
