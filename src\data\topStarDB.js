const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Tạo đường dẫn đến file database
const dbPath = path.join(__dirname, 'topstar.db');

// Tạo kết nối database
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('Lỗi khi kết nối database TopStar:', err.message);
  } else {
    console.log('Đã kết nối thành công đến database TopStar');
  }
});

// Tạo bảng topstar nếu chưa tồn tại
db.serialize(() => {
  db.run(`CREATE TABLE IF NOT EXISTS topstar (
    playerId TEXT PRIMARY KEY,
    totalHours INTEGER DEFAULT 0,
    totalBookings INTEGER DEFAULT 0,
    lastUpdated INTEGER DEFAULT (strftime('%s', 'now'))
  )`);
});

/**
 * Cậ<PERSON> nhật giờ book cho player trong topstar
 * @param {string} playerId - ID của player
 * @param {number} hours - <PERSON><PERSON> giờ được book
 * @returns {Promise<boolean>} - <PERSON><PERSON><PERSON> quả thực hiện
 */
function updatePlayerTopStar(playerId, hours) {
  return new Promise((resolve, reject) => {
    console.log(`🔍 DEBUG: Bắt đầu cập nhật TopStar - Player: ${playerId}, Hours: ${hours}`);
    
    db.run(
      `INSERT INTO topstar (playerId, totalHours, totalBookings, lastUpdated) 
       VALUES (?, ?, 1, strftime('%s', 'now'))
       ON CONFLICT(playerId) DO UPDATE SET 
       totalHours = COALESCE(totalHours, 0) + ?,
       totalBookings = totalBookings + 1,
       lastUpdated = strftime('%s', 'now')`,
      [playerId, hours, hours],
      function (err) {
        if (err) {
          console.error('❌ Lỗi khi cập nhật TopStar:', err);
          reject(err);
        } else {
          console.log(`✅ Đã cập nhật TopStar cho player ${playerId}: +${hours} giờ`);
          console.log(`🔍 DEBUG: Changes: ${this.changes}, LastID: ${this.lastID}`);
          
          // Kiểm tra lại dữ liệu ngay sau khi cập nhật
          db.get(
            `SELECT * FROM topstar WHERE playerId = ?`,
            [playerId],
            (selectErr, row) => {
              if (selectErr) {
                console.error('❌ Lỗi khi kiểm tra dữ liệu sau cập nhật:', selectErr);
              } else {
                console.log(`🔍 DEBUG: Dữ liệu sau cập nhật:`, row);
              }
            }
          );
          
          resolve(true);
        }
      }
    );
  });
}

/**
 * Lấy danh sách top players theo giờ book
 * @param {number} limit - Số lượng players cần lấy
 * @returns {Promise<Array>} - Mảng các players với tổng giờ được book
 */
function getTopStarPlayers(limit = 10) {
  return new Promise((resolve, reject) => {
    const safeLimit = Math.min(Math.max(limit, 1), 50);
    
    db.all(
      `SELECT playerId, totalHours, totalBookings
       FROM topstar 
       WHERE totalHours > 0
       ORDER BY totalHours DESC 
       LIMIT ?`,
      [safeLimit],
      (err, rows) => {
        if (err) {
          console.error('Lỗi khi lấy TopStar players:', err);
          reject(err);
        } else {
          resolve(rows);
        }
      }
    );
  });
}

/**
 * Reset toàn bộ dữ liệu TopStar
 * @returns {Promise<boolean>} - Kết quả thực hiện
 */
function resetAllTopStar() {
  return new Promise((resolve, reject) => {
    db.run(
      `DELETE FROM topstar`,
      [],
      function (err) {
        if (err) {
          console.error('Lỗi khi reset TopStar:', err);
          reject(err);
        } else {
          console.log('Đã reset toàn bộ dữ liệu TopStar');
          resolve(true);
        }
      }
    );
  });
}

/**
 * Trừ giờ book cho player trong topstar (dùng khi hoàn bill)
 * @param {string} playerId - ID của player
 * @param {number} hours - Số giờ cần trừ
 * @returns {Promise<boolean>} - Kết quả thực hiện
 */
function subtractPlayerTopStar(playerId, hours) {
  return new Promise((resolve, reject) => {
    console.log(`🔍 DEBUG: Bắt đầu trừ TopStar - Player: ${playerId}, Hours: ${hours}`);

    db.run(
      `UPDATE topstar
       SET totalHours = CASE
         WHEN totalHours - ? <= 0 THEN 0
         ELSE totalHours - ?
       END,
       totalBookings = CASE
         WHEN totalBookings - 1 <= 0 THEN 0
         ELSE totalBookings - 1
       END,
       lastUpdated = strftime('%s', 'now')
       WHERE playerId = ?`,
      [hours, hours, playerId],
      function (err) {
        if (err) {
          console.error('❌ Lỗi khi trừ TopStar:', err);
          reject(err);
        } else {
          console.log(`✅ Đã trừ TopStar cho player ${playerId}: -${hours} giờ`);
          console.log(`🔍 DEBUG: Changes: ${this.changes}`);
          resolve(this.changes > 0);
        }
      }
    );
  });
}

/**
 * Reset TopStar của một player cụ thể
 * @param {string} playerId - ID của player
 * @returns {Promise<boolean>} - Kết quả thực hiện
 */
function resetPlayerTopStar(playerId) {
  return new Promise((resolve, reject) => {
    db.run(
      `DELETE FROM topstar WHERE playerId = ?`,
      [playerId],
      function (err) {
        if (err) {
          console.error('Lỗi khi reset TopStar player:', err);
          reject(err);
        } else {
          console.log(`Đã reset TopStar của player ${playerId}`);
          resolve(this.changes > 0);
        }
      }
    );
  });
}

module.exports = {
  updatePlayerTopStar,
  subtractPlayerTopStar,
  getTopStarPlayers,
  resetAllTopStar,
  resetPlayerTopStar
};