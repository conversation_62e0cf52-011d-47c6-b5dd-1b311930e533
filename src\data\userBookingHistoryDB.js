const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Đ<PERSON><PERSON> bảo thư mục data tồn tại
const dataDir = path.join(__dirname);
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// Kết nối đến cơ sở dữ liệu
const db = new Database(path.join(dataDir, 'userBookingHistory.db'));

// Khởi tạo bảng nếu chưa tồn tại
db.exec(`
  CREATE TABLE IF NOT EXISTS userBookingHistory (
    userId TEXT PRIMARY KEY,
    totalHours INTEGER DEFAULT 0,
    lastUpdated INTEGER
  )
`);

// Tự động thêm cột totalMoney nếu chưa có
const columns = db.prepare(`PRAGMA table_info(userBookingHistory)`).all();
if (!columns.some(col => col.name === 'totalBookedMoney')) {
  db.exec(`ALTER TABLE userBookingHistory ADD COLUMN totalBookedMoney INTEGER DEFAULT 0`);
}
if (!columns.some(col => col.name === 'totalSalary')) {
  db.exec(`ALTER TABLE userBookingHistory ADD COLUMN totalSalary INTEGER DEFAULT 0`);
}

// Hàm lấy lịch sử booking của người dùng
function getUserBookingHistory(userId) {
  const stmt = db.prepare('SELECT * FROM userBookingHistory WHERE userId = ?');
  const result = stmt.get(userId);
  return result || { userId, totalHours: 0, lastUpdated: Date.now() };
}

// Hàm cập nhật số giờ booking của người dùng
function updateUserBookingHours(userId, hours) {
  const userHistory = getUserBookingHistory(userId);
  const newTotalHours = userHistory.totalHours + hours;
  
  const stmt = db.prepare(`
    INSERT INTO userBookingHistory (userId, totalHours, lastUpdated)
    VALUES (?, ?, ?)
    ON CONFLICT(userId) DO UPDATE SET
    totalHours = ?,
    lastUpdated = ?
  `);
  
  stmt.run(userId, newTotalHours, Date.now(), newTotalHours, Date.now());
  return { userId, totalHours: newTotalHours, lastUpdated: Date.now() };
}

// Hàm cập nhật tổng số tiền của người dùng
function updateUserBookingTotal(userId, amount) {
  return new Promise((resolve, reject) => {
    db.run(
      `UPDATE userBookingHistory SET totalMoney = totalMoney + ? WHERE userId = ?`,
      [amount, userId],
      function (err) {
        if (err) reject(err);
        else resolve(this.changes > 0);
      }
    );
  });
}
module.exports.updateUserBookingTotal = updateUserBookingTotal;

// Hàm cập nhật thông tin booking của người dùng
function updateUserBooking(userId, hours, money) {
  const userHistory = getUserBookingHistory(userId);
  const newTotalHours = userHistory.totalHours + hours;
  const newTotalMoney = (userHistory.totalMoney || 0) + money;

  const stmt = db.prepare(`
    INSERT INTO userBookingHistory (userId, totalHours, totalMoney, lastUpdated)
    VALUES (?, ?, ?, ?)
    ON CONFLICT(userId) DO UPDATE SET
      totalHours = ?,
      totalMoney = ?,
      lastUpdated = ?
  `);

  stmt.run(
    userId, newTotalHours, newTotalMoney, Date.now(),
    newTotalHours, newTotalMoney, Date.now()
  );
  return { userId, totalHours: newTotalHours, totalMoney: newTotalMoney, lastUpdated: Date.now() };
}
module.exports.updateUserBooking = updateUserBooking;

// Thêm hoặc cập nhật tổng giờ và tổng tiền đã book
function addUserBooking(userId, hours, money) {
  const userHistory = getUserBookingHistory(userId);
  const newTotalHours = userHistory.totalHours + hours;
  const newTotalBookedMoney = (userHistory.totalBookedMoney || 0) + money;

  const stmt = db.prepare(`
    INSERT INTO userBookingHistory (userId, totalHours, totalBookedMoney, lastUpdated)
    VALUES (?, ?, ?, ?)
    ON CONFLICT(userId) DO UPDATE SET
      totalHours = ?,
      totalBookedMoney = ?,
      lastUpdated = ?
  `);

  stmt.run(
    userId, newTotalHours, newTotalBookedMoney, Date.now(),
    newTotalHours, newTotalBookedMoney, Date.now()
  );
  return { userId, totalHours: newTotalHours, totalBookedMoney: newTotalBookedMoney, lastUpdated: Date.now() };
}
module.exports.addUserBooking = addUserBooking;

// Reset lương (totalSalary) mà không reset tổng tiền đã book
function resetUserSalary(userId) {
  db.run(
    `UPDATE userBookingHistory SET totalSalary = 0 WHERE userId = ?`,
    [userId]
  );
}
module.exports.resetUserSalary = resetUserSalary;

// Lấy danh sách người dùng có tổng tiền booking cao nhất
function getTopBookers(limit = 10) {
  const stmt = db.prepare('SELECT * FROM userBookingHistory ORDER BY totalBookedMoney DESC LIMIT ?');
  return stmt.all(limit);
}
module.exports.getTopBookers = getTopBookers;

function setTotalBookedMoney(userId, value) {
  const stmt = db.prepare(`UPDATE userBookingHistory SET totalBookedMoney = ? WHERE userId = ?`);
  const info = stmt.run(value, userId);
  return info.changes;
}
module.exports.setTotalBookedMoney = setTotalBookedMoney;

// Lấy danh sách người dùng có tổng giờ booking cao nhất
function getTopBookersByHours(limit = 10) {
  // Đảm bảo limit không vượt quá 50 để tránh spam
  const safeLimit = Math.min(Math.max(limit, 1), 50);
  const stmt = db.prepare('SELECT * FROM userBookingHistory WHERE totalHours > 0 ORDER BY totalHours DESC LIMIT ?');
  return stmt.all(safeLimit);
}
module.exports.getTopBookersByHours = getTopBookersByHours;

module.exports = {
  getUserBookingHistory,
  updateUserBookingHours,
  updateUserBookingTotal,
  updateUserBooking,
  addUserBooking,
  resetUserSalary,
  setTotalBookedMoney,
  getTopBookers,
  getTopBookersByHours
};