const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Tạo kết nối đến database
const db = new sqlite3.Database(path.join(__dirname, 'weekly_messages.db'));

// Tạo bảng nếu chưa có
db.serialize(() => {
  // Bảng lưu tin nhắn hàng tuần
  db.run(`
    CREATE TABLE IF NOT EXISTS weekly_messages (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      userId TEXT NOT NULL,
      channelId TEXT NOT NULL,
      guildId TEXT NOT NULL,
      messageCount INTEGER NOT NULL DEFAULT 1,
      weekStart TEXT NOT NULL,
      weekEnd TEXT NOT NULL,
      lastUpdated INTEGER NOT NULL,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Bảng cấu hình kênh giám sát
  db.run(`
    CREATE TABLE IF NOT EXISTS monitored_channels (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      channelId TEXT NOT NULL UNIQUE,
      channelName TEXT NOT NULL,
      guildId TEXT NOT NULL,
      isActive INTEGER NOT NULL DEFAULT 1,
      createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Index để tăng hiệu suất
  db.run(`CREATE INDEX IF NOT EXISTS idx_weekly_messages_user_week ON weekly_messages(userId, weekStart)`);
  db.run(`CREATE INDEX IF NOT EXISTS idx_weekly_messages_channel_week ON weekly_messages(channelId, weekStart)`);
});

/**
 * Lấy ngày đầu tuần (thứ 2) và cuối tuần (chủ nhật)
 * @returns {Object} - {weekStart, weekEnd}
 */
function getCurrentWeekRange() {
  const now = new Date();
  const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, ...
  
  // Tính ngày thứ 2 của tuần hiện tại
  const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
  const monday = new Date(now);
  monday.setDate(now.getDate() + mondayOffset);
  monday.setHours(0, 0, 0, 0);
  
  // Tính ngày chủ nhật của tuần hiện tại
  const sunday = new Date(monday);
  sunday.setDate(monday.getDate() + 6);
  sunday.setHours(23, 59, 59, 999);
  
  return {
    weekStart: monday.toISOString().split('T')[0], // YYYY-MM-DD
    weekEnd: sunday.toISOString().split('T')[0]
  };
}

/**
 * Thêm hoặc cập nhật số lượng tin nhắn của user trong tuần
 * @param {string} userId - ID của user
 * @param {string} channelId - ID của kênh
 * @param {string} guildId - ID của server
 * @returns {Promise<boolean>}
 */
function addMessage(userId, channelId, guildId) {
  return new Promise((resolve, reject) => {
    const { weekStart, weekEnd } = getCurrentWeekRange();
    const now = Date.now();
    
    // Kiểm tra xem đã có record cho user trong tuần này chưa
    db.get(
      `SELECT id, messageCount FROM weekly_messages 
       WHERE userId = ? AND channelId = ? AND weekStart = ?`,
      [userId, channelId, weekStart],
      (err, row) => {
        if (err) {
          console.error('Lỗi khi kiểm tra tin nhắn tuần:', err);
          reject(err);
          return;
        }
        
        if (row) {
          // Cập nhật số lượng tin nhắn
          db.run(
            `UPDATE weekly_messages 
             SET messageCount = messageCount + 1, lastUpdated = ?
             WHERE id = ?`,
            [now, row.id],
            function(err) {
              if (err) {
                console.error('Lỗi khi cập nhật tin nhắn tuần:', err);
                reject(err);
              } else {
                resolve(true);
              }
            }
          );
        } else {
          // Tạo record mới
          db.run(
            `INSERT INTO weekly_messages (userId, channelId, guildId, messageCount, weekStart, weekEnd, lastUpdated)
             VALUES (?, ?, ?, 1, ?, ?, ?)`,
            [userId, channelId, guildId, weekStart, weekEnd, now],
            function(err) {
              if (err) {
                console.error('Lỗi khi thêm tin nhắn tuần mới:', err);
                reject(err);
              } else {
                resolve(true);
              }
            }
          );
        }
      }
    );
  });
}

/**
 * Lấy thống kê tin nhắn tuần hiện tại theo kênh
 * @param {string} channelId - ID của kênh
 * @returns {Promise<Array>}
 */
function getWeeklyStats(channelId) {
  return new Promise((resolve, reject) => {
    const { weekStart } = getCurrentWeekRange();
    
    db.all(
      `SELECT userId, messageCount, weekStart, weekEnd
       FROM weekly_messages 
       WHERE channelId = ? AND weekStart = ?
       ORDER BY messageCount DESC`,
      [channelId, weekStart],
      (err, rows) => {
        if (err) {
          console.error('Lỗi khi lấy thống kê tuần:', err);
          reject(err);
        } else {
          resolve(rows || []);
        }
      }
    );
  });
}

/**
 * Lấy thống kê tất cả kênh trong tuần
 * @param {string} guildId - ID của server
 * @returns {Promise<Array>}
 */
function getAllWeeklyStats(guildId) {
  return new Promise((resolve, reject) => {
    const { weekStart } = getCurrentWeekRange();
    
    db.all(
      `SELECT channelId, userId, messageCount, weekStart, weekEnd
       FROM weekly_messages 
       WHERE guildId = ? AND weekStart = ?
       ORDER BY channelId, messageCount DESC`,
      [guildId, weekStart],
      (err, rows) => {
        if (err) {
          console.error('Lỗi khi lấy tất cả thống kê tuần:', err);
          reject(err);
        } else {
          resolve(rows || []);
        }
      }
    );
  });
}

/**
 * Thêm kênh vào danh sách giám sát
 * @param {string} channelId - ID của kênh
 * @param {string} channelName - Tên kênh
 * @param {string} guildId - ID của server
 * @returns {Promise<boolean>}
 */
function addMonitoredChannel(channelId, channelName, guildId) {
  return new Promise((resolve, reject) => {
    db.run(
      `INSERT OR REPLACE INTO monitored_channels (channelId, channelName, guildId, isActive)
       VALUES (?, ?, ?, 1)`,
      [channelId, channelName, guildId],
      function(err) {
        if (err) {
          console.error('Lỗi khi thêm kênh giám sát:', err);
          reject(err);
        } else {
          resolve(true);
        }
      }
    );
  });
}

/**
 * Xóa kênh khỏi danh sách giám sát
 * @param {string} channelId - ID của kênh
 * @returns {Promise<boolean>}
 */
function removeMonitoredChannel(channelId) {
  return new Promise((resolve, reject) => {
    db.run(
      `UPDATE monitored_channels SET isActive = 0 WHERE channelId = ?`,
      [channelId],
      function(err) {
        if (err) {
          console.error('Lỗi khi xóa kênh giám sát:', err);
          reject(err);
        } else {
          resolve(true);
        }
      }
    );
  });
}

/**
 * Lấy danh sách kênh đang được giám sát
 * @param {string} guildId - ID của server
 * @returns {Promise<Array>}
 */
function getMonitoredChannels(guildId) {
  return new Promise((resolve, reject) => {
    db.all(
      `SELECT channelId, channelName FROM monitored_channels 
       WHERE guildId = ? AND isActive = 1`,
      [guildId],
      (err, rows) => {
        if (err) {
          console.error('Lỗi khi lấy danh sách kênh giám sát:', err);
          reject(err);
        } else {
          resolve(rows || []);
        }
      }
    );
  });
}

/**
 * Reset dữ liệu tuần cũ (chạy tự động mỗi tuần)
 * @returns {Promise<boolean>}
 */
function resetOldWeekData() {
  return new Promise((resolve, reject) => {
    const { weekStart } = getCurrentWeekRange();
    
    db.run(
      `DELETE FROM weekly_messages WHERE weekStart < ?`,
      [weekStart],
      function(err) {
        if (err) {
          console.error('Lỗi khi reset dữ liệu tuần cũ:', err);
          reject(err);
        } else {
          console.log(`✅ Đã xóa ${this.changes} records tuần cũ`);
          resolve(true);
        }
      }
    );
  });
}

module.exports = {
  addMessage,
  getWeeklyStats,
  getAllWeeklyStats,
  addMonitoredChannel,
  removeMonitoredChannel,
  getMonitoredChannels,
  resetOldWeekData,
  getCurrentWeekRange
};
