const { REST, Routes, PermissionFlagsBits } = require('discord.js');
const { clientId, guildId, token } = require('./config.json');
const fs = require('node:fs');
const path = require('node:path');

const commands = [];
// L<PERSON>y tất cả các file lệnh từ thư mục commands
const commandsPath = path.join(__dirname, 'commands');
const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));

// L<PERSON>y tất cả các file slash command từ thư mục slashCommands
const slashCommandsPath = path.join(__dirname, 'slashCommands');
const slashCommandFiles = fs.readdirSync(slashCommandsPath).filter(file => file.endsWith('.js'));

// Thêm các slash command vào mảng commands
for (const file of slashCommandFiles) {
  const filePath = path.join(slashCommandsPath, file);
  const command = require(filePath);
  if ('data' in command && 'execute' in command) {
    // <PERSON>hông bắt buộc quyền admin - để code trong file slash command tự kiểm tra
    commands.push(command.data.toJSON());
  } else {
    console.log(`[CẢNH BÁO] Slash command tại ${filePath} thiếu thuộc tính "data" hoặc "execute".`);
  }
}

// Khởi tạo REST API client
const rest = new REST().setToken(token);

// Đăng ký các slash command
(async () => {
  try {
    console.log(`Bắt đầu đăng ký ${commands.length} lệnh slash.`);

    // Đăng ký các lệnh cho guild cụ thể
    const data = await rest.put(
      Routes.applicationGuildCommands(clientId, guildId),
      { body: commands },
    );

    console.log(`Đã đăng ký thành công ${data.length} lệnh slash.`);
  } catch (error) {
    console.error(error);
  }
})();