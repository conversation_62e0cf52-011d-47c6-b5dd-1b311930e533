const fs = require('fs');
const path = require('path');
const keywordsPath = path.join(__dirname, '../data/keywords.json');
const profileDB = require('../data/profileDB');
const { EmbedBuilder } = require('discord.js');
const axios = require('axios');
const https = require('https');
const { createWriteStream } = require('fs');
const crypto = require('crypto');
const { getVipEmbed } = require('../utils/sendServiceInfo');
const { getShopRingsEmbed } = require('../utils/sendShoprings');

// Tạo thư mục lưu trữ hình ảnh nếu chưa tồn tại
const imageDir = path.join(__dirname, '../../images');
if (!fs.existsSync(imageDir)) {
  fs.mkdirSync(imageDir, { recursive: true });
  console.log(`<PERSON><PERSON> tạ<PERSON> thư mục lưu trữ hình ảnh: ${imageDir}`);
}

// Hàm kiểm tra URL có phải là URL hình ảnh Discord không
function isDiscordImageUrl(url) {
  if (!url) return false;
  return url.match(/^https?:\/\/cdn\.discordapp\.com\/attachments\/\d+\/\d+\/[^?]+(\?.*)?$/i) !== null;
}

function toClassicFont(str) {
  const classicMap = {
    A: '𝐀', B: '𝐁', C: '𝐂', D: '𝐃', E: '𝐄', F: '𝐅', G: '𝐆', H: '𝐇', I: '𝐈', J: '𝐉', K: '𝐊', L: '𝐋', M: '𝐌',
    N: '𝐍', O: '𝐎', P: '𝐏', Q: '𝐐', R: '𝐑', S: '𝐒', T: '𝐓', U: '𝐔', V: 'V', W: '𝐖', X: '𝐗', Y: '𝐘', Z: '𝐙',
    a: 'a', b: '𝐛', c: '𝐜', d: '𝐝', e: '𝐞', f: '𝐟', g: '𝐠', h: '𝐡', i: '𝐢', j: '𝐣', k: '𝐤', l: '𝐥', m: '𝐦',
    n: '𝐧', o: '𝐨', p: '𝐩', q: '𝐪', r: '𝐫', s: '𝐬', t: '𝐭', u: '𝐮', v: '𝐯', w: '𝐰', x: '𝐱', y: '𝐲', z: '𝐳',
    '0': '𝟎', '1': '𝟏', '2': '𝟐', '3': '𝟑', '4': '𝟒', '5': '𝟓', '6': '𝟔', '7': '𝟕', '8': '𝟖', '9': '𝟗'
  };
  return str.split('').map(ch => classicMap[ch] || ch).join('');
}

// Thêm hàm capitalizeFirst
function capitalizeFirst(str) {
  if (!str) return str;
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

module.exports = (client) => {
  client.on('messageCreate', async (message) => {
    if (message.author.bot) return;
    const content = message.content.toLowerCase().trim();
    
    // Xử lý vip và tshop trực tiếp
    if (content === 'vip') {
      const embed = getVipEmbed(client);
      return message.channel.send({ embeds: [embed] });
    }
    
    if (content === 'tshop') {
      const embed = getShopRingsEmbed(client);
      return message.channel.send({ embeds: [embed] });
    }
    
    // Xử lý keywords từ file
    if (fs.existsSync(keywordsPath)) {
      const keywords = JSON.parse(fs.readFileSync(keywordsPath, 'utf8'));
      for (const item of keywords) {
        if (content === item.keyword.toLowerCase()) {
          // Nếu là từ khóa profile
          if (item.type && item.number) {
            // Lấy profile từ DB
            const profiles = await profileDB.getAllProfilesByType(item.type);
            const p = profiles.find(pr => pr.playerNumber == item.number);
            if (p) {
              let emoji, color, titleEmoji;
              
              if (/^murph/i.test(p.playerType)) {
                emoji = '<:mu:1385677279065280603>';
                color = '#BFAEE3';
                titleEmoji = '<:mur:1378445368689164290>';
              } else if (/^joseph/i.test(p.playerType)) {
                emoji = '<:jo:1385684426976923659>';
                color = '#0C5776';
                titleEmoji = '<:jos:1378445302381674547>';
              } else if (/^(owner|staff|support)/i.test(p.playerType)) {
                emoji = '<a:white:1376852619632316517>';
                color = '#c02626';
                titleEmoji = '<a:ad:1385687143015383100>';
              }

              let embedDesc = '';
              if (p.name) embedDesc += `** ## ${emoji} ${toClassicFont(p.name)}**\n`;

              // Thêm mention player từ database
              if (p.mention) {
                embedDesc += `${emoji} ${p.mention}\n`;
              }

              if (p.location) embedDesc += `${emoji} ${p.location}\n`;
              if (p.bio) embedDesc += `${emoji} ${p.bio}\n`;
              if (p.game) embedDesc += `${emoji} Game: ${p.game}\n`;
              if (p.cam) embedDesc += `${emoji} Giá cam: ${p.cam}\n`;

              const modernTitle = `${toClassicFont(capitalizeFirst(p.playerType))} ${toClassicFont(p.playerNumber !== null ? p.playerNumber.toString() : '')}`;
              const embed = new EmbedBuilder()
                .setColor(color) // Sử dụng color đã được set thay vì client.embedColor
                .setTitle(`${titleEmoji} **${modernTitle}** ${titleEmoji}`) // Sử dụng titleEmoji thay vì sparkles cố định
                .setDescription(embedDesc);

              if (p.thumbnail && /^https?:\/\/.+\..+/.test(p.thumbnail)) embed.setThumbnail(p.thumbnail);
              if (p.image && /^https?:\/\/.+\..+/.test(p.image)) embed.setImage(p.image);

                return message.channel.send({ embeds: [embed] });
            } else {
              return message.channel.send('Không tìm thấy profile!');
            }
          }
          
            // Xử lý autoresponse với image và/hoặc thumbnail
            if (item.image || item.thumbnail || item.response) {
              // Thay thế biến {user} trong response
              let processedResponse = item.response || '';
              if (processedResponse.includes('{user}')) {
                processedResponse = processedResponse.replace(/{user}/g, `<@${message.author.id}>`);
              }

              const embed = new EmbedBuilder()
                .setColor(client.embedColor || '#0099ff');

              let files = [];

              // Xử lý image
              if (item.image && typeof item.image === 'string') {
                if (item.image.startsWith('http')) {
                  embed.setImage(item.image);
                } else if (fs.existsSync(path.join(__dirname, '../', item.image))) {
                  embed.setImage('attachment://' + path.basename(item.image));
                  files.push(path.join(__dirname, '../', item.image));
                }
              }

              // Xử lý thumbnail
              if (item.thumbnail && typeof item.thumbnail === 'string') {
                if (item.thumbnail.startsWith('http')) {
                  embed.setThumbnail(item.thumbnail);
                } else if (fs.existsSync(path.join(__dirname, '../', item.thumbnail))) {
                  embed.setThumbnail('attachment://' + path.basename(item.thumbnail));
                  files.push(path.join(__dirname, '../', item.thumbnail));
                }
              }

              // Nếu có embed (image hoặc thumbnail), gửi với embed
              if (item.image || item.thumbnail) {
                // Đảm bảo embed có ít nhất một field để tránh lỗi
                if (!processedResponse && !embed.data.title && !embed.data.description) {
                  embed.setDescription(' '); // Thêm description trống để tránh lỗi
                }

                return message.channel.send({
                  content: processedResponse || undefined,
                  embeds: [embed],
                  files: files.length > 0 ? files : undefined
                });
              }
              // Nếu chỉ có response, gửi text thường
              else if (processedResponse) {
                return message.channel.send(processedResponse);
              }
            }
        }
      }
    }
  });
};