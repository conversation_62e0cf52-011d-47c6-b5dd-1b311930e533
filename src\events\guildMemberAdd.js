const { EmbedBuilder } = require('discord.js');

module.exports = {
  name: 'guildMemberAdd',
  async execute(member) {
    const AUTO_ROLE_ID = '1376621462458335252';
    try {
      await member.roles.add(AUTO_ROLE_ID);
      // Bạn có thể log hoặc gửi thông báo nếu muốn
    } catch (error) {
      console.error(`Không thể gán role tự động cho ${member.user.tag}:`, error);
    }

    try {
      // Kênh để gửi tin nhắn chào mừng
      const welcomeChannelId = '1341447664666939412'; 
      
      const welcomeChannel = member.guild.channels.cache.get(welcomeChannelId);
      
      if (!welcomeChannel) {
        console.error('Không tìm thấy kênh chào mừng!');
        return;
      }
      
      // Tạo tin nhắn chào mừng
      const welcomeMessage = `
      <a:KittyPaw23:1376849763332390933> **Welcome to 𝐓𝐡𝐞 𝐈𝐧𝐭𝐞𝐫𝐬𝐭𝐞𝐥𝐥𝐚𝐫**
      
Cám ơn bạn đã đến với server của chúng mình. Hy vọng bạn sẽ có những trải nghiệm thật tuyệt vời tại server.
      `;
      
      // Tạo embed chào mừng (tùy chọn)
      const welcomeEmbed = new EmbedBuilder()
        .setColor('#13293d')
        .setDescription(`${welcomeMessage}`);
      
      // Gửi tin nhắn chào mừng với tiêu đề ở ngoài embed
      await welcomeChannel.send({
        content: `
        Hi <@${member.id}>,
        `,
        embeds: [welcomeEmbed]
      });     
    } catch (error) {
      console.error('Lỗi khi chào mừng thành viên mới:', error);
    }
  },
};