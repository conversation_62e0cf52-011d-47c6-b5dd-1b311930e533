const { Events, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, ChannelType, PermissionFlagsBits, MessageFlags, StringSelectMenuBuilder, StringSelectMenuOptionBuilder } = require('discord.js');
const TranscriptManager = require('../utils/transcriptManager');
const ReactBillDB = require('../data/reactBillDB');
const { logSlashCommand } = require('../utils/commandLogger');
const ticketAutoReply = require('../utils/ticketAutoReply');

const ticketLogMessages = new Map();

module.exports = (client) => {

client.on(Events.InteractionCreate, async (interaction) => {
  console.log('Interaction received:', interaction.type, interaction.customId || 'no customId');

  if (interaction.isButton()) {
    let topic, embedColor;
    if (interaction.customId === 'ticket_booking') {
      topic = 'Booking';
      embedColor = 0x3498db;
    } else if (interaction.customId === 'ticket_support') {
      topic = 'Support';
      embedColor = 0x2ecc71;
    } else if (interaction.customId === 'ticket_recruitment') {
      topic = 'Tuyển dụng';
      embedColor = 0xe67e22;
    }

    if (topic) {
      const guild = interaction.guild;
      const user = interaction.user;
      // Kiểm tra xem user đã có ticket thread cho loại này chưa
      const existingThread = guild.channels.cache.find(c =>
        c.isThread() && c.name.includes(`${topic} - ${user.username}`)
      );
      if (existingThread) {
        return interaction.reply({ content: 'Bạn đã có một ticket đang mở cho chủ đề này!', ephemeral: true });
      }

      // Tìm đúng category ★Ticket
      console.log('🔍 Đang tìm kiếm category ★Ticket...');

      // List tất cả categories để debug
      const allCategories = guild.channels.cache.filter(c => c.type === ChannelType.GuildCategory);
      console.log('📋 Tất cả categories:');
      allCategories.forEach(cat => {
        console.log(`  - ${cat.name} (ID: ${cat.id})`);
      });

      // Tìm category với nhiều pattern khác nhau
      const ticketCategory = guild.channels.cache.find(c => {
        if (c.type !== ChannelType.GuildCategory) return false;

        const name = c.name.toLowerCase();
        return (
          name.includes('ticket') ||
          name.includes('★ticket') ||
          name === '★ticket' ||
          name === 'ticket' ||
          c.name.includes('★Ticket') ||
          c.name.includes('Ticket') ||
          c.name.includes('𝘽𝙤𝙤𝙠𝙞𝙣𝙜') || // Fallback to Booking category
          c.name.includes('booking')
        );
      });

      let ticketCategoryId = null;

      if (!ticketCategory) {
        console.log('❌ Không tìm thấy category ticket!');
        console.log('💡 Hãy sử dụng lệnh "debug-categories" để xem tất cả categories');

        // Tạo ticket ở ngoài (không có parent) như fallback
        console.log('⚠️ Tạo ticket ở ngoài category (fallback)');
        ticketCategoryId = null; // Không có parent
      } else {
        ticketCategoryId = ticketCategory.id;
        console.log(`✅ Tìm thấy category: ${ticketCategory.name} (ID: ${ticketCategoryId})`);
      }


      // Tìm kênh để tạo thread (sử dụng kênh ticket panel hoặc kênh chung)
      let parentChannel;
      try {
        // Thử tìm kênh ticket panel trước
        parentChannel = await guild.channels.fetch('1341447407933722635');
        console.log(`📍 Sử dụng kênh ticket panel: ${parentChannel.name}`);
      } catch (error) {
        // Nếu không tìm thấy, tìm kênh chung trong category booking
        const bookingCategory = guild.channels.cache.get('1341446877425438752');
        if (bookingCategory) {
          parentChannel = bookingCategory.children.cache.find(c =>
            c.type === ChannelType.GuildText &&
            (c.name.includes('ticket') || c.name.includes('support') || c.name.includes('general'))
          );
          if (parentChannel) {
            console.log(`📍 Sử dụng kênh trong booking category: ${parentChannel.name}`);
          }
        }

        // Nếu vẫn không tìm thấy, sử dụng kênh đầu tiên có quyền tạo thread
        if (!parentChannel) {
          parentChannel = guild.channels.cache.find(c =>
            c.type === ChannelType.GuildText &&
            c.permissionsFor(interaction.client.user).has([PermissionFlagsBits.CreatePrivateThreads, PermissionFlagsBits.SendMessages])
          );
          if (parentChannel) {
            console.log(`📍 Sử dụng kênh fallback: ${parentChannel.name}`);
          }
        }
      }

      if (!parentChannel) {
        return interaction.reply({
          content: '❌ | Không tìm thấy kênh phù hợp để tạo ticket thread!',
          ephemeral: true
        });
      }

      // Defer reply để có thêm thời gian xử lý
      try {
        await interaction.deferReply({ ephemeral: true });
      } catch (error) {
        console.log('Lỗi khi defer interaction:', error.message);
        return;
      }

      // Tạo private thread trực tiếp (không cần tin nhắn startMessage)
      const ticketThread = await parentChannel.threads.create({
        name: `${topic} - ${user.username}`,
        autoArchiveDuration: 1440, // 24 giờ trước khi tự động archive
        reason: `${topic} ticket created by ${user.username}`,
        type: 12, // PRIVATE_THREAD
        invitable: false, // Chỉ moderators mới có thể invite thêm người
      });

      console.log(`Private thread created: ${ticketThread.name} (ID: ${ticketThread.id})`);

      // Thêm user vào private thread
      try {
        await ticketThread.members.add(user.id);
        console.log(`Da them user ${user.username} vao private thread`);
      } catch (error) {
        console.log(`Khong the them user ${user.username} vao thread:`, error.message);
      }

      // Đợi một chút để thread được khởi tạo hoàn toàn
      await new Promise(resolve => setTimeout(resolve, 300));

      console.log(`Private thread created successfully. Staff and supporters can access via role permissions.`);

      const closeRow = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
          .setCustomId('ticket_close')
          .setLabel('Đóng ticket')
          .setStyle(ButtonStyle.Danger)
      );



      // Gửi tin nhắn chào mừng trong private thread
      await ticketThread.send({
        content: `${user} Cám ơn bạn đã mở ticket **${topic}**!\n\n<@&1376884726232514620> và <@&1376500798896214108> sẽ hỗ trợ bạn sớm nhất có thể.`,
        components: [closeRow]
      });

      // Bắt đầu tracking ticket cho auto reply
      ticketAutoReply.startTracking({
        threadId: ticketThread.id,
        userId: user.id,
        topic: topic,
        createdAt: Date.now()
      });

      // Đợi một chút rồi gửi tin nhắn hướng dẫn
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Hoàn thành interaction với editReply
      try {
        await interaction.editReply({
          content: `<:done:1383009630581424250> | Đã tạo ticket **${topic}** thành công! Thread: ${ticketThread}`
        });
      } catch (error) {
        console.log('Lỗi khi edit reply interaction:', error.message);
      }
      
      const logChannel = client.channels.cache.get('1388072567901913152');
      if (logChannel) {
        const createLogEmbed = new EmbedBuilder()
          .setColor(embedColor)
          .setTitle('TICKET ĐƯỢC TẠO')
          .setThumbnail(user.displayAvatarURL())
          .setDescription(
            `**Người tạo:** ${user} (${user.tag})\n\n` +
            `**Loại ticket:** ${topic}\n\n` +
            `**Thread:** ${ticketThread}\n\n` + // Fixed: using ticketThread instead of ticketChannel
            `**Thời gian tạo:** <t:${Math.floor(Date.now() / 1000)}:F>\n\n` +
            `**Trạng thái:** 🟢 Đang mở`
          )
          .setTimestamp()
          .setFooter({ text: `User ID: ${user.id}` });
        
        const logMessage = await logChannel.send({ embeds: [createLogEmbed] });

        // Lưu thông tin ticket với tên thread thực tế
        const actualThreadName = ticketThread.name;
        ticketLogMessages.set(actualThreadName, {
          messageId: logMessage.id,
          channelId: logChannel.id,
          creatorId: user.id,
          creatorTag: user.tag,
          ticketType: topic,
          ticketChannel: ticketThread.toString(),
          ticketCategory: ticketCategoryId,
          embedColor: embedColor,
          createdAt: Math.floor(Date.now() / 1000)
        });
      }
      
      return;
    }

    // Đóng ticket khi bấm nút
    const transcriptManager = new TranscriptManager();
    if (interaction.customId === 'ticket_close') {
      // Lấy tên channel từ interaction.channel.name
      const channelName = interaction.channel.name;
      
      // Kiểm tra quyền - chỉ admin, staff hoặc supporters mới có thể đóng ticket
      const hasPermission = interaction.member.permissions.has(PermissionFlagsBits.Administrator) ||
                           interaction.member.roles.cache.has('1376884726232514620') || // Special role 1
                           interaction.member.roles.cache.has('1376500798896214108'); // Special role 2
      
      if (!hasPermission) {
        await interaction.reply({ 
          content: 'Bạn không có quyền đóng ticket.', 
          flags: [MessageFlags.Ephemeral] 
        });
        return;
      }
      
      // TẠO TRANSCRIPT TRƯỚC KHI ĐÓNG
      let transcriptFile = null;
      try {
        const ticketData = ticketLogMessages.get(channelName);
        transcriptFile = await transcriptManager.createTranscript(interaction.channel, ticketData);
        console.log(`Đã tạo transcript: ${transcriptFile.fileName}`);
      } catch (error) {
        console.error('Lỗi khi tạo transcript:', error);
      }
      
      // TÀM VÀ UPDATE LOG EMBED
      const ticketData = ticketLogMessages.get(channelName);
      
      if (ticketData) {
        try {
          const logChannel = client.channels.cache.get(ticketData.channelId);
          if (logChannel) {
            // Lấy username từ format mới: ticket-type-username
            const parts = channelName.split('-');
            const ownerUsername = parts.length >= 3 ? parts[2] : parts[0];
            
            // Thay vì edit embed cũ, gửi embed mới với transcript
            const newCloseEmbed = new EmbedBuilder()
              .setColor(0xff0000)
              .setTitle('TICKET ĐƯỢC ĐÓNG')
              .setThumbnail(interaction.user.displayAvatarURL())
              .setDescription(
                `**Người tạo:** <@${ticketData.creatorId}> (${ticketData.creatorTag})\n\n` +
                `**Loại ticket:** ${ticketData.ticketType}\n\n` +
                `**Thread:** ${ticketData.ticketChannel}\n\n` +
                `**Owner ticket:** ${ownerUsername}\n\n` +
                `**Thời gian tạo:** <t:${ticketData.createdAt}:F>\n\n` +
                `**Người đóng:** ${interaction.user} (${interaction.user.tag})\n\n` +
                `**Thời gian đóng:** <t:${Math.floor(Date.now() / 1000)}:F>\n\n` +
                `**Trạng thái:** 🔴 Đã đóng\n\n` +
                `**Transcript:** ${transcriptFile ? '✅ Đã tạo' : '❌ Lỗi tạo file'}`
              )
              .setTimestamp()
              .setFooter({ text: `Closed by: ${interaction.user.id}` });
            
            // Gửi embed mới với transcript file
            const newLogContent = { embeds: [newCloseEmbed] };
            if (transcriptFile) {
              newLogContent.files = [transcriptFile.attachment];
            }
            
            await logChannel.send(newLogContent);
            ticketLogMessages.delete(channelName);
          }
        } catch (error) {
          console.error('Lỗi khi gửi log embed mới:', error);
          // Fallback: gửi log mới với transcript
          const logChannel = client.channels.cache.get('1380335203364503644');
          if (logChannel) {
            const ownerUsername = channelName.split('-')[0];
            
            const closeLogEmbed = new EmbedBuilder()
              .setColor(0xff0000)
              .setTitle('TICKET ĐƯỢC ĐÓNG')
              .setThumbnail(interaction.user.displayAvatarURL())
              .setDescription(
                `**Người đóng:** ${interaction.user} (${interaction.user.tag})\n\n` +
                `**Loại ticket:** ${ticketData?.ticketType || 'Unknown'}\n\n` +
                `**Kênh:** #${channelName}\n\n` +
                `**Owner ticket:** ${ownerUsername}\n\n` +
                `**Thời gian:** <t:${Math.floor(Date.now() / 1000)}:F>\n\n` +
                `**Transcript:** ${transcriptFile ? '✅ Đã tạo' : '❌ Lỗi tạo file'}`
              )
              .setTimestamp()
              .setFooter({ text: `Closed by: ${interaction.user.id}` });
            
            // Gửi kèm transcript file
            const fallbackLogContent = { embeds: [closeLogEmbed] };
            if (transcriptFile) {
              fallbackLogContent.files = [transcriptFile.attachment];
            }
            
            await logChannel.send(fallbackLogContent);
          }
        }
      }
      
      await interaction.reply({ content: 'Ticket sẽ được đóng sau 5 giây...', flags: [MessageFlags.Ephemeral] });

      setTimeout(async () => {
        try {
          const channel = interaction.channel;

          // Dừng tracking ticket auto reply
          ticketAutoReply.stopTracking(channel.id);

          // Xử lý đóng thread ticket
          if (channel.isThread()) {
            try {
              // Lock thread trước để user không thể gửi tin nhắn
              await channel.setLocked(true);
              console.log(`🔒 Đã khóa thread: ${channel.name}`);

              // Sau đó archive thread
              await channel.setArchived(true);
              console.log(`📁 Đã archive thread: ${channel.name}`);
            } catch (error) {
              console.log(`❌ Không thể khóa hoặc archive thread: ${channel.name}`);
            }
          } else {
            console.log(`❌ Channel không phải là thread: ${channel.name}`);
          }
        } catch (error) {
          console.error('Lỗi khi đóng ticket:', error);
        }
      }, 5000);
    }

    // Xử lý các nút hỗ trợ từ auto reply
    if (interaction.customId === 'ticket_available') {
      await interaction.reply({
        content: '🟢 **Available Players**\n\nDanh sách những player đang online có thể nhận bill bây giờ:\n\n*Đang cập nhật danh sách...*',
        ephemeral: true
      });
    }

    if (interaction.customId === 'ticket_rent') {
      await interaction.reply({
        content: '<a:emoji_99:1378452250321752095> **Rent Player**\n\nBạn muốn thuê player cụ thể? Vui lòng cho biết:\n\n• Tên player bạn muốn thuê\n• Thời gian thuê\n• Loại dịch vụ (Book/Game/Oncam)',
        ephemeral: true
      });
    }

    if (interaction.customId === 'ticket_support') {
      await interaction.reply({
        content: '<a:support:1400748127514001489> **Support Request**\n\nĐang ping Staff và Support để hỗ trợ bạn...',
        ephemeral: true
      });

      // Ping staff và support
      await interaction.followUp({
        content: `<a:support:1400748127514001489> **YÊU CẦU HỖ TRỢ**\n<@&1376884726232514620> <@&1376500798896214108>\n\n${interaction.user} cần hỗ trợ trong ticket này. Vui lòng kiểm tra và phản hồi!`
      });
    }
  }
  // kết thúc xử lý ticket

  // Xử lý react bill
  if (interaction.isButton()) {
    if (interaction.customId === 'react_bill') {
  
  if (interaction.customId === 'react_bill') {
    try {      
      // Lấy tin nhắn gốc
      const message = await interaction.channel.messages.fetch(interaction.message.id);
      const currentEmbed = message.embeds[0];
      
      let reactList = currentEmbed.description;
      const user = interaction.user;
      const userName = user.username;
      const userId = user.id;
      let newReactList;
      
      // Kiểm tra xem người dùng đã react chưa
      if (reactList.includes(`<@${userId}>`)) {
        await interaction.reply({
          content: 'Bạn đã react rồi!',
          flags: [MessageFlags.Ephemeral]
        });
        return;
      }
      
      // Đếm số người đã react
      const reactCount = (reactList.match(/\d+\./g) || []).length;
      
      // Thêm người dùng vào danh sách react
      if (reactList === 'Danh sách React:') {
        newReactList = `Danh sách React:\n\n01. ${userName}\n└─ <@${userId}>`;
      } else {
        const nextNumber = (reactCount + 1).toString().padStart(2, '0');
        newReactList = reactList + `\n\n${nextNumber}. ${userName}\n└─ <@${userId}>`;
      }
      
      // Tạo embed mới với danh sách đã cập nhật
      const updatedEmbed = EmbedBuilder.from(currentEmbed)
        .setDescription(newReactList);
      
      // Cập nhật tin nhắn với embed mới
      await message.edit({
        embeds: [updatedEmbed],
        files: []
      });
      
      // Lấy thông tin kênh gốc từ database
      let originalChannelData = ReactBillDB.getOriginalChannel(message.id);
      
      // Trong phần xử lý react_bill (khoảng dòng 275-300)
      try {
        if (originalChannelData) {
          // Đã có thông tin kênh gốc
          const originalChannel = interaction.client.channels.cache.get(originalChannelData.originalChannelId);
          
          if (originalChannel) {
            try {
              // Lấy danh sách người dùng đã react từ newReactList
              const reactUsers = [];
              const lines = newReactList.split('\n');

              for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();
                // Tìm dòng chứa số thứ tự (ví dụ: "01. username")
                if (line.match(/^\d+\./)) {
                  const nextLine = i + 1 < lines.length ? lines[i + 1].trim() : '';
                  // Tìm dòng chứa mention user (ví dụ: "└─ <@userId>")
                  if (nextLine.includes('<@') && nextLine.includes('>')) {
                    const userIdMatch = nextLine.match(/<@(\d+)>/);
                    if (userIdMatch) {
                      const userId = userIdMatch[1];
                      const displayName = line.replace(/^\d+\.\s*/, ''); // Tên hiển thị trong embed
                      const orderNumber = line.match(/^(\d+)\./)[1]; // Lấy số thứ tự

                      // Lấy username thực tế từ Discord API
                      try {
                        const discordUser = await interaction.client.users.fetch(userId);
                        const realUsername = discordUser.username; // Username thực tế
                        reactUsers.push({ userId, username: realUsername, displayName, orderNumber });
                        console.log(`User ${displayName} -> Real username: ${realUsername}`);
                      } catch (error) {
                        console.error(`Không thể fetch user ${userId}:`, error);
                        // Fallback: sử dụng displayName
                        reactUsers.push({ userId, username: displayName, displayName, orderNumber });
                      }
                    }
                  }
                }
              }
              
              // Tạo placeholder động
              let placeholder = 'Chọn để xem profile player';
              if (reactUsers.length > 0) {
                const firstPlayer = reactUsers[0].orderNumber;
                const lastPlayer = reactUsers[reactUsers.length - 1].orderNumber;
                placeholder = `Chọn để xem profile player ( ${firstPlayer} - ${lastPlayer} )`;
              }
              
              // Tạo menu thả xuống với danh sách player
              const selectMenuOptions = [];
              
              // Thêm các player vào menu (tối đa 25 options)
              console.log('reactUsers length:', reactUsers.length);
              for (let i = 0; i < Math.min(reactUsers.length, 23); i++) {
                const user = reactUsers[i];
                console.log('Adding user to menu:', user);
                selectMenuOptions.push(
                  new StringSelectMenuOptionBuilder()
                    .setLabel(`${user.orderNumber}. ${user.displayName}`) // Hiển thị displayName
                    .setValue(`profile_${user.userId}`)
                );
              }
              console.log('selectMenuOptions length:', selectMenuOptions.length);

              // Kiểm tra nếu không có options thì không tạo menu
              if (selectMenuOptions.length === 0) {
                console.log('No menu options, skipping menu creation');
                return;
              }

              const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('react_bill_actions')
                .setPlaceholder(placeholder)
                .addOptions(selectMenuOptions);

              const actionRow = new ActionRowBuilder().addComponents(selectMenu);
              console.log('Created menu with customId: react_bill_actions');
              
              if (originalChannelData.originalMessageId) {
                // Đã có tin nhắn trong kênh gốc, cập nhật nó
                try {
                  const originalMessage = await originalChannel.messages.fetch(originalChannelData.originalMessageId);
                  
                  const updatedOriginalEmbed = EmbedBuilder.from(originalMessage.embeds[0])
                    .setDescription(newReactList)
                    .setTimestamp();
                  
                  await originalMessage.edit({ 
                    embeds: [updatedOriginalEmbed],
                    components: [actionRow]
                  });
                  
                  console.log('Đã cập nhật embed trong kênh gốc');
                } catch (updateError) {
                  console.error('Lỗi khi cập nhật tin nhắn gốc:', updateError);
                  
                  // Nếu tin nhắn gốc bị xóa, tạo tin nhắn mới
                  if (updateError.code === 10008) {
                    const newOriginalEmbed = new EmbedBuilder()
                      .setTitle('REACT BILL')
                      .setDescription(newReactList)
                      .setColor(client.embedColor || '#0099ff')
                      .setTimestamp();
                    
                    const sentMessage = await originalChannel.send({ 
                      embeds: [newOriginalEmbed],
                      components: [actionRow]
                    });
                    
                    ReactBillDB.updateOriginalMessageId(message.id, sentMessage.id);
                  }
                }
              } else {
                // Chưa có tin nhắn trong kênh gốc, tạo mới
                const newOriginalEmbed = new EmbedBuilder()
                  .setTitle('REACT BILL')
                  .setDescription(newReactList)
                  .setColor(client.embedColor || '#0099ff')
                  .setTimestamp();
                
                const sentMessage = await originalChannel.send({ 
                  embeds: [newOriginalEmbed],
                  components: [actionRow]
                });
                
                ReactBillDB.updateOriginalMessageId(message.id, sentMessage.id);
              }
            } catch (error) {
              console.error('Lỗi khi xử lý kênh gốc:', error);
            }
          }
        } else {
          // Lần đầu tiên, lưu thông tin kênh hiện tại làm kênh gốc
          ReactBillDB.saveOriginalChannel(message.id, interaction.channel.id, null);
          console.log('Đã lưu thông tin kênh gốc');
        }
        
        await interaction.reply({
          content: `Đã thêm bạn vào danh sách react!`,
          flags: [MessageFlags.Ephemeral]
        });
      } catch (error) {
        console.error('Lỗi khi xử lý nút React bill:', error);
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({
            content: 'Đã xảy ra lỗi khi xử lý yêu cầu của bạn.',
            flags: [MessageFlags.Ephemeral]
          });
        }
      }
    } catch (error) {
      console.error('Lỗi khi xử lý nút React bill (outer):', error);
      if (!interaction.replied && !interaction.deferred) {
        try {
          await interaction.reply({
            content: 'Đã xảy ra lỗi khi xử lý yêu cầu của bạn.',
            flags: [MessageFlags.Ephemeral]
          });
        } catch (replyError) {
          console.error('Không thể reply interaction:', replyError);
        }
      }
    }
  }
    }

    // Xử lý nút Hủy react
    if (interaction.customId === 'cancel_react') {
    try {
      // Lấy thông tin người dùng
      const user = interaction.user;
      const userName = user.username;
      const userId = user.id;
      
      // Lấy tin nhắn gốc
      const message = interaction.message;
      
      // Lấy embed hiện tại
      const currentEmbed = message.embeds[0];
      
      // Lấy danh sách react hiện tại
      let reactList = currentEmbed.description;
      
      // Kiểm tra xem người dùng có trong danh sách không
      if (!reactList.includes(`<@${userId}>`)) {
        await interaction.reply({ 
          content: 'Bạn chưa react nên không thể hủy!', 
          flags: [MessageFlags.Ephemeral]
        });
        return;
      }
      
      // Tách danh sách thành các dòng
      const lines = reactList.split('\n');
      let newList = ['Danh sách React:'];
      let counter = 1;
      
      // Duyệt qua từng dòng và loại bỏ người dùng cần hủy
      for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        
        // Bỏ qua dòng trống
        if (line === '') continue;
        
        // Kiểm tra nếu là dòng chứa tên người dùng (có số thứ tự)
        if (line.match(/^\d+\./)) {
          const nextLine = i + 1 < lines.length ? lines[i + 1].trim() : '';
          
          // Kiểm tra nếu dòng tiếp theo chứa mention của người dùng cần xóa
          if (nextLine.includes(`<@${userId}>`)) {
            // Bỏ qua cả hai dòng (tên và mention)
            i++; // Skip mention line
            continue;
          } else {
            // Thêm người dùng khác với số thứ tự mới
            const numberStr = counter.toString().padStart(2, '0');
            const userLine = line.replace(/^\d+\./, `${numberStr}.`);
            newList.push('');
            newList.push(userLine);
            if (nextLine.startsWith('└─')) {
              newList.push(nextLine);
              i++; // Skip mention line
            }
            counter++;
          }
        }
      }
      
      // Nếu danh sách trống sau khi xóa
      if (counter === 1) {
        reactList = 'Danh sách React:';
      } else {
        reactList = newList.join('\n');
      }
      
      // Tạo embed mới với danh sách đã cập nhật
      const updatedEmbed = EmbedBuilder.from(currentEmbed)
        .setDescription(reactList);
      
      // Cập nhật tin nhắn với embed mới
      await message.edit({ 
        embeds: [updatedEmbed],
        files: []
      });
      
      // Cập nhật embed trong kênh gốc nếu có
      const originalChannelData = ReactBillDB.getOriginalChannel(message.id);
      
      if (originalChannelData) {
        const originalChannel = interaction.client.channels.cache.get(originalChannelData.originalChannelId);
        
        if (originalChannel) {
          try {
            if (originalChannelData.originalMessageId) {
              // Cập nhật tin nhắn đã tồn tại
              const originalMessage = await originalChannel.messages.fetch(originalChannelData.originalMessageId);
              
              const updatedOriginalEmbed = EmbedBuilder.from(originalMessage.embeds[0])
                .setDescription(reactList)
                .setTimestamp();
              
              await originalMessage.edit({ 
                embeds: [updatedOriginalEmbed] 
              });
              
              console.log('Đã cập nhật embed trong kênh gốc sau khi hủy react');
            } else {
              // Tạo tin nhắn mới nếu chưa có
              const newOriginalEmbed = new EmbedBuilder()
                .setTitle('REACT BILL')
                .setDescription(reactList)
                .setColor(client.embedColor || '#0099ff')
                .setTimestamp();
              
              const sentMessage = await originalChannel.send({ 
                embeds: [newOriginalEmbed] 
              });
              
              ReactBillDB.updateOriginalMessageId(message.id, sentMessage.id);
            }
          } catch (error) {
            console.error('Lỗi khi cập nhật embed trong kênh gốc:', error);
            // Nếu tin nhắn gốc bị xóa, tạo mới
            if (error.code === 10008) {
              const newOriginalEmbed = new EmbedBuilder()
                .setTitle('REACT BILL')
                .setDescription(reactList)
                .setColor(client.embedColor || '#0099ff')
                .setTimestamp();
              
              const sentMessage = await originalChannel.send({ 
                embeds: [newOriginalEmbed] 
              });
              
              ReactBillDB.updateOriginalMessageId(message.id, sentMessage.id);
            }
          }
        }
      }
      
      // Phản hồi người dùng
      await interaction.reply({ 
        content: 'Đã hủy react của bạn!', 
        flags: [MessageFlags.Ephemeral]
      });
    } catch (error) {
      console.error('Lỗi khi xử lý nút Hủy react:', error);
      await interaction.reply({ 
        content: 'Đã xảy ra lỗi khi xử lý yêu cầu của bạn.', 
        flags: [MessageFlags.Ephemeral]
      });
    }
    }
  } // Đóng block isButton

  // Xử lý StringSelectMenu cho react bill actions
  if (interaction.isStringSelectMenu()) {
    console.log('StringSelectMenu detected, customId:', interaction.customId);
    if (interaction.customId === 'react_bill_actions') {
      console.log('Processing react_bill_actions menu');
      try {
        const selectedValue = interaction.values[0];

        // Kiểm tra nếu là profile action
        if (selectedValue.startsWith('profile_')) {
          const userId = selectedValue.replace('profile_', '');

          // Import các module cần thiết
          const fs = require('fs');
          const path = require('path');
          const profileDB = require('../data/profileDB');
          const keywordsPath = path.join(__dirname, '../data/keywords.json');
          const { getUserProfileMapping } = require('../commands/es_set');

          // Lấy thông tin user để lấy username
          const targetUser = await interaction.client.users.fetch(userId);
          if (!targetUser) {
            return interaction.reply({
              content: 'Không tìm thấy người dùng này!',
              ephemeral: true
            });
          }

          const username = targetUser.username.toLowerCase();
          console.log(`🔍 Tìm profile cho user: ${targetUser.username} (${username})`);

          // Tìm profile player dựa trên mapping
          let playerProfile = null;

          // Ưu tiên 1: Kiểm tra mapping từ es_set
          const userMapping = getUserProfileMapping(userId);
          if (userMapping) {
            console.log(`🎯 Tìm thấy mapping từ es_set:`, userMapping);
            const profiles = await profileDB.getAllProfilesByType(userMapping.playerType);
            playerProfile = profiles.find(p => p.playerNumber === userMapping.playerNumber);
            console.log(`✅ Profile từ mapping:`, playerProfile ? `${playerProfile.playerType} ${playerProfile.playerNumber}` : 'Không tìm thấy');
          }

          if (!playerProfile) {
            console.log(`<:error:1383005371542798346> Không tìm thấy profile cho user: ${targetUser.username} (${userId})`);
            return interaction.reply({
              content: `<:error:1383005371542798346> | Không tìm thấy profile player cho **${targetUser.username}**.\n\n` +
                      `💡 **Hướng dẫn:**\n` +
                      `• Admin có thể gán profile bằng: \`tes set <player_type> <number> | @${targetUser.username}\`\n` +
                      `• Hoặc thêm username vào keywords.json`,
              ephemeral: true
            });
          }

          console.log(`✅ Sẽ hiển thị profile: ${playerProfile.playerType} ${playerProfile.playerNumber} cho user ${targetUser.username}`);

          // Tạo embed profile player (giống như autorespond)
          let emoji, color, titleEmoji;

          if (/^murph/i.test(playerProfile.playerType)) {
            emoji = '<:mu:1385677279065280603>';
            color = '#BFAEE3';
            titleEmoji = '<:mur:1378445368689164290>';
          } else if (/^joseph/i.test(playerProfile.playerType)) {
            emoji = '<:jo:1385684426976923659>';
            color = '#0C5776';
            titleEmoji = '<:jos:1378445302381674547>';
          } else if (/^owner/i.test(playerProfile.playerType)) {
            emoji = '<a:white:1376852619632316517>';
            color = '#c02626';
            titleEmoji = '<a:ad:1385687143015383100>';
          } else if (/^staff/i.test(playerProfile.playerType)) {
            emoji = '<a:white:1376852619632316517>';
            color = '#c02626';
            titleEmoji = '<a:ad:1385687143015383100>';
          } else if (/^support/i.test(playerProfile.playerType)) {
            emoji = '<a:white:1376852619632316517>';
            color = '#c02626';
            titleEmoji = '<a:ad:1385687143015383100>';
          } else {
            // Default fallback
            emoji = '🎭';
            color = '#0099ff';
            titleEmoji = '🎭';
          }

          // Hàm chuyển đổi font (copy từ autorespond.js)
          function toClassicFont(text) {
            const fontMap = {
              'A': '𝐀', 'B': '𝐁', 'C': '𝐂', 'D': '𝐃', 'E': '𝐄', 'F': '𝐅', 'G': '𝐆', 'H': '𝐇', 'I': '𝐈', 'J': '𝐉',
              'K': '𝐊', 'L': '𝐋', 'M': '𝐌', 'N': '𝐍', 'O': '𝐎', 'P': '𝐏', 'Q': '𝐐', 'R': '𝐑', 'S': '𝐒', 'T': '𝐓',
              'U': '𝐔', 'V': '𝐕', 'W': '𝐖', 'X': '𝐗', 'Y': '𝐘', 'Z': '𝐙',
              'a': '𝐚', 'b': '𝐛', 'c': '𝐜', 'd': '𝐝', 'e': '𝐞', 'f': '𝐟', 'g': '𝐠', 'h': '𝐡', 'i': '𝐢', 'j': '𝐣',
              'k': '𝐤', 'l': '𝐥', 'm': '𝐦', 'n': '𝐧', 'o': '𝐨', 'p': '𝐩', 'q': '𝐪', 'r': '𝐫', 's': '𝐬', 't': '𝐭',
              'u': '𝐮', 'v': '𝐯', 'w': '𝐰', 'x': '𝐱', 'y': '𝐲', 'z': '𝐳',
              '0': '𝟎', '1': '𝟏', '2': '𝟐', '3': '𝟑', '4': '𝟒', '5': '𝟓', '6': '𝟔', '7': '𝟕', '8': '𝟖', '9': '𝟗'
            };
            return text.split('').map(char => fontMap[char] || char).join('');
          }

          function capitalizeFirst(str) {
            if (!str) return str;
            return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
          }

          let embedDesc = '';
          if (playerProfile.name) embedDesc += `** ## ${emoji} ${toClassicFont(playerProfile.name)}**\n`;

          // Thêm mention player từ database
          if (playerProfile.mention) {
            embedDesc += `${emoji} ${playerProfile.mention}\n`;
          }

          if (playerProfile.location) embedDesc += `${emoji} ${playerProfile.location}\n`;
          if (playerProfile.bio) embedDesc += `${emoji} ${playerProfile.bio}\n`;
          if (playerProfile.game) embedDesc += `${emoji} Game: ${playerProfile.game}\n`;
          if (playerProfile.cam) embedDesc += `${emoji} Giá cam: ${playerProfile.cam}\n`;

          const modernTitle = `${toClassicFont(capitalizeFirst(playerProfile.playerType))} ${toClassicFont(playerProfile.playerNumber !== null ? playerProfile.playerNumber.toString() : '')}`;
          const embed = new EmbedBuilder()
            .setColor(color)
            .setTitle(`${titleEmoji} **${modernTitle}** ${titleEmoji}`)
            .setDescription(embedDesc);

          if (playerProfile.thumbnail && /^https?:\/\/.+\..+/.test(playerProfile.thumbnail)) embed.setThumbnail(playerProfile.thumbnail);
          if (playerProfile.image && /^https?:\/\/.+\..+/.test(playerProfile.image)) embed.setImage(playerProfile.image);

          // Gửi embed profile player
          await interaction.channel.send({ embeds: [embed] });
        }
      } catch (error) {
        console.error('Lỗi khi xử lý StringSelectMenu:', error);
        await interaction.reply({
          content: 'Đã xảy ra lỗi khi xử lý yêu cầu của bạn.',
          ephemeral: true
        });
      }
    }

    // Xử lý shop select menu store
    if (interaction.customId === 'shop_select') {
      const selectedValue = interaction.values[0];
      let productEmbed;

      switch (selectedValue) {
        case 'nitro':
          productEmbed = new EmbedBuilder()
            .setColor(client.embedColor || '#0099ff')
            .setTitle('<:Nicho:1392668118551232563> Bảng giá Nitro Boost')
            .setThumbnail(client.user.displayAvatarURL({ dynamic: true }))
            .setDescription(
              '<:cham:1391893252029808682> Nitro Boost Login Trial 3 tháng <a:Emoji048:1376863038413738064> 50k\n\n' + 
              '<:cham:1391893252029808682> Nitro Boost Login 1 tháng <a:Emoji048:1376863038413738064> 95k\n\n' +
              '<:cham:1391893252029808682> Nitro Boost Login 2 tháng <a:Emoji048:1376863038413738064> 110k\n\n' +
              '<:cham:1391893252029808682> Nitro Boost Login 6 tháng <a:Emoji048:1376863038413738064> 320k\n\n' +
              '<:cham:1391893252029808682> Nitro Boost Login 1 năm <a:Emoji048:1376863038413738064> 770k\n\n' +
              '**Lưu ý**: Nitro Boost Trial chỉ dành cho acc mới tạo trên 1 tháng và chưa dùng nitro bao giờ hoặc 12 tháng chưa xài nitro.\n\n' +
              '<a:Emoji048:1376863038413738064> Tạo <#1341447407933722635> để được hỗ trợ mua hàng.'
            )
            .setFooter({ text: 'Nitro Boost • Done trong 2h - 3h' })
            .setTimestamp();
          break;

        case 'boost':
          productEmbed = new EmbedBuilder()
            .setColor(client.embedColor || '#0099ff')
            .setTitle('<a:Emoji047:1376865715818070108> Server Boosts')
            .setThumbnail(client.user.displayAvatarURL({ dynamic: true }))
            .setDescription(
              '<:cham:1391893252029808682> Boost Server Lv3 (1 tháng) <a:Emoji048:1376863038413738064> 110k\n\n' +
              '<:cham:1391893252029808682> Boost Server Lv3 (2 tháng) <a:Emoji048:1376863038413738064> 150k\n\n' +
              '<:cham:1391893252029808682> Boost Server Lv3 (3 tháng) <a:Emoji048:1376863038413738064> 350k\n\n' +
              '<a:Emoji048:1376863038413738064> Tạo <#1341447407933722635> để được hỗ trợ mua hàng.'
            )
            .setFooter({ text: 'Server Boosts • Done trong ngày' })
            .setTimestamp();
          break;

        case 'spotify':
          productEmbed = new EmbedBuilder()
            .setColor(client.embedColor || '#0099ff')
            .setTitle('<:Spotify:1392669668334108683> Spotify Premium')
            .setThumbnail(client.user.displayAvatarURL({ dynamic: true }))
            .setDescription(
              '<:cham:1391893252029808682> Spotify Individual 2 Tháng - Cấp acc <a:Emoji048:1376863038413738064> 45k\n\n' +
              '<:cham:1391893252029808682> Spotify Individual 3 Tháng - Chính chủ <a:Emoji048:1376863038413738064> 122k\n\n' +
              '<:cham:1391893252029808682> Spotify Individual 6 Tháng - Chính chủ <a:Emoji048:1376863038413738064> 195k\n\n' +
              '<:cham:1391893252029808682> Spotify Individual 12 Tháng - Chính chủ <a:Emoji048:1376863038413738064> 300k\n\n' +
              '<a:Emoji048:1376863038413738064> Tạo <#1341447407933722635> để được hỗ trợ mua hàng.'
            )
            .setFooter({ text: 'Spotify Premium • Done trong ngày' })
            .setTimestamp();
          break;

        case 'netflix':
          productEmbed = new EmbedBuilder()
            .setColor(client.embedColor || '#0099ff')
            .setTitle('<a:Netflix:1392669992658538576> Netflix Premium')
            .setThumbnail(client.user.displayAvatarURL({ dynamic: true }))
            .setDescription(
              '<:cham:1391893252029808682> Netflix Private 1 tháng <a:Emoji048:1376863038413738064> 55k\n\n' +
              '<:cham:1391893252029808682> Netflix Private 3 tháng <a:Emoji048:1376863038413738064> 160k\n\n' +
              '<:cham:1391893252029808682> Netflix Private 6 tháng <a:Emoji048:1376863038413738064> 310k\n\n' +
              '<:cham:1391893252029808682> Netflix Private 12 tháng <a:Emoji048:1376863038413738064> 600k\n\n' +
              '<:cham:1391893252029808682> Netflix Share 1 tháng <a:Emoji048:1376863038413738064> 40k\n\n' +
              '<:cham:1391893252029808682> Netflix Share 3 tháng <a:Emoji048:1376863038413738064> 110k\n\n' +
              '<:cham:1391893252029808682> Netflix Share 6 tháng <a:Emoji048:1376863038413738064> 160k\n\n' +
              '<:cham:1391893252029808682> Netflix Share 12 tháng <a:Emoji048:1376863038413738064> 250k\n\n' +
              '<a:Emoji048:1376863038413738064> Bạn sẽ được cung cấp 1 profile khi mua và bảo hành toàn thời gian sử dụng.\n' +
              '**Lưu ý**: Đối với Netflix Share thi thoảng sẽ bị sẽ Full Screenn\n\n' +
              '<a:Emoji048:1376863038413738064> Tạo <#1341447407933722635> để được hỗ trợ mua hàng.'

            )
            .setFooter({ text: 'Netflix Premium • Done trong ngày' })
            .setTimestamp();
          break;

        case 'youtube':
          productEmbed = new EmbedBuilder()
            .setColor(client.embedColor || '#0099ff')
            .setTitle('<:youtube:1392669664751910913> Youtube Premium')
            .setThumbnail(client.user.displayAvatarURL({ dynamic: true }))
            .setDescription(
              '<:cham:1391893252029808682> Youtube Premium 1 tháng <a:Emoji048:1376863038413738064> 20k\n\n' +
              '<:cham:1391893252029808682> Youtube Premium 3 tháng <a:Emoji048:1376863038413738064> 100k\n\n' +
              '<:cham:1391893252029808682> Youtube Premium 6 tháng <a:Emoji048:1376863038413738064> 190k\n\n' +
              '<:cham:1391893252029808682> Youtube Premium 12 tháng <a:Emoji048:1376863038413738064> 280k\n\n' +
              '<a:Emoji048:1376863038413738064> Add mail chính chủ. Bảo hành toàn thời gian sử dụng.\n\n\n' +
              '<:cham:1391893252029808682> Youtube Farm 1 tháng ( own ) <a:Emoji048:1376863038413738064> 40k\n\n' +
              '<a:Emoji048:1376863038413738064> Add được 5 thành viên. Bảo hành 30 ngày.\n\n' +
              '<a:Emoji048:1376863038413738064> Tạo <#1341447407933722635> để được hỗ trợ mua hàng.'
            )
            .setFooter({ text: 'Youtube Premium • Done trong ngày' })
            .setTimestamp();
          break;
        case 'ring':
          productEmbed = new EmbedBuilder()
            .setColor(client.embedColor || '#0099ff')
            .setTitle('<:Emoji009:1382956926979018782> Nhẫn marry 𝐓𝐡𝐞 𝐈𝐧𝐭𝐞𝐫𝐬𝐭𝐞𝐥𝐥𝐚𝐫')
            .setThumbnail(client.user.displayAvatarURL({ dynamic: true }))
            .setDescription(
              `\`01.\` <:firstdate:1395218976115789925> **First date**\n• Giá: 100.000 VNĐ\n\n` +
              `\`02.\` <:eternia:1395218967681040527> **Eternia**\n• Giá: 200.000 VNĐ\n\n` +
              `\`03.\` <:amora:1395218962815651924> **Amora**\n• Giá: 300.000 VNĐ\n\n` +
              `\`04.\` <:serenity:1395218985401716867> **Serenity**\n• Giá: 400.000 VNĐ\n\n` +
              `\`05.\` <:unity:1395218993220026418> **Unity**\n• Giá: 500.000 VNĐ`
            )
            .setFooter({ text: 'Nhẫn marry 𝐓𝐡𝐞 𝐈𝐧𝐭𝐞𝐫𝐬𝐭𝐞𝐥𝐥𝐚𝐫' })
            .setTimestamp();
          break;

        case 'bot':
          productEmbed = new EmbedBuilder()
            .setColor(client.embedColor || '#0099ff')
            .setTitle('<:bot:1392670240797622282> Custom Bot')
            .setThumbnail(client.user.displayAvatarURL({ dynamic: true }))
            .setDescription(
              '<:cham:1391893252029808682> Thuê Bot Booking <a:Emoji048:1376863038413738064> Deal\n\n' +
              '<:cham:1391893252029808682> Custome Bot riêng cho server <a:Emoji048:1376863038413738064> Deal\n\n' +
              '<a:Emoji048:1376863038413738064> Tạo <#1341447407933722635> để được hỗ trợ tư vấn thêm.'
            )
            .setFooter({ text: 'Custom Bot' })
            .setTimestamp();
          break;

        default:
          productEmbed = new EmbedBuilder()
            .setColor(client.embedColor || '#0099ff')
            .setTitle('<:error:1383005371542798346> Lỗi')
            .setDescription('Sản phẩm không tồn tại!');
      }

      await interaction.reply({ embeds: [productEmbed], ephemeral: true });
    }
  }

  // Xử lý slash command
  if (interaction.isChatInputCommand()) {
    console.log('Processing slash command:', interaction.commandName);
    const command = client.slashCommands.get(interaction.commandName);
    if (!command) {
      console.log('Command not found:', interaction.commandName);
      return interaction.reply({ content: 'Lệnh không tồn tại hoặc đã bị xóa.', ephemeral: true });
    }

    // Log slash command usage
    await logSlashCommand(client, interaction, interaction.commandName);

    try {
      await command.execute(interaction);
    } catch (error) {
      console.error(error);
      
      // Kiểm tra trạng thái interaction trước khi reply
      try {
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({ content: 'Đã xảy ra lỗi khi thực thi lệnh này!', ephemeral: true });
        } else if (interaction.deferred) {
          await interaction.editReply({ content: 'Đã xảy ra lỗi khi thực thi lệnh này!' });
        } else {
          await interaction.followUp({ content: 'Đã xảy ra lỗi khi thực thi lệnh này!', ephemeral: true });
        }
      } catch (replyError) {
        console.error('Lỗi khi gửi thông báo lỗi:', replyError);
      }
    }
  }
});

};

// Export ticketLogMessages để các file khác có thể sử dụng
module.exports.ticketLogMessages = ticketLogMessages;