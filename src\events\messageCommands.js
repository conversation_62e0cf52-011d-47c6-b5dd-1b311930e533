const { EmbedBuilder } = require('discord.js');
const cashDB = require('../data/cashDB');
const userBookingHistoryDB = require('../data/userBookingHistoryDB');
const playerHistoryDB = require('../data/playerHistoryDB');
const { formatNumber } = require('../utils/formatUtils');
const { logPrefixCommand } = require('../utils/commandLogger');
const fs = require('fs');
const path = require('path');
const topStarDB = require('../data/topStarDB');

// Đọc prefix từ file cấu hình hoặc biến môi trường
let PREFIX;
try {
  // Thử đọc từ file config.json nếu có
  const configPath = path.join(__dirname, '..', 'config.json');
  if (fs.existsSync(configPath)) {
    const config = require(configPath);
    PREFIX = config.prefix;
  } else {
    // Nếu không có file config, sử dụng biến môi trường hoặc giá trị mặc định
    PREFIX = process.env.BOT_PREFIX || '?';
  }
} catch (error) {
  console.error('Lỗi khi đọc prefix:', error);
  PREFIX = 'h'; // Prefix mặc định nếu không đọc được
}

// Hàm parse số tiền từ chuỗi (hỗ trợ 1k, 10k, 100k, 1m, ...)
function parseAmount(str) {
  str = str.toLowerCase().replace(/,/g, '').replace(/đ|vnđ/g, '').trim();
  let match = str.match(/^(\d+(\.\d+)?)([km]?)$/);
  if (!match) return NaN;
  let num = parseFloat(match[1]);
  let unit = match[3];
  if (unit === 'k') num *= 1000;
  if (unit === 'm') num *= 1000000;
  return Math.round(num);
}

// Xử lý lệnh cash
async function handleCashCommand(message, args) {
  try {
    let targetUser = message.author;
    let isCheckingOther = false;
    
    // Kiểm tra xem có mention người dùng khác không
    if (message.mentions.users.size > 0) {
      targetUser = message.mentions.users.first();
      isCheckingOther = true;
    }
    
    const cashInfo = await cashDB.getCash(targetUser.id);
    
    // Tạo embed hiển thị số dư
    const embed = new EmbedBuilder()
      .setColor(message.client.embedColor || '#0099ff')
      .setAuthor({
        name: 'ACCOUNT BALANCE',
        iconURL: message.client.user.displayAvatarURL() 
      });
      
    if (isCheckingOther) {
      embed.setDescription(`<a:01:1378452250321752095> | Số dư hiện tại của ${targetUser} là **${formatNumber(cashInfo.amount)}** VND.`);
    } else {
      embed.setDescription(`<a:01:1378452250321752095> | Số dư hiện tại của bạn là **${formatNumber(cashInfo.amount)}** VND.`);
    }
    
    embed.setFooter({ text: `Hôm nay lúc: ${new Date().toLocaleString('vi-VN')}` });
    
    // Gửi embed
    await message.reply({ embeds: [embed] });
  } catch (error) {
    console.error('Lỗi khi kiểm tra tiền cọc:', error);
    const embed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED' })
      .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi kiểm tra số dư. Vui lòng thử lại sau.')
      .setColor(message.client.embedColor || '#0099ff')
      .setTimestamp();
    await message.reply({ embeds: [embed] });
  }
}

// Xử lý lệnh topcash
async function handleTopCashCommand(message) {
  try {
    // Kiểm tra quyền hạn 
    const hasAdminPermission = message.member.permissions.has('Administrator');
    
    if (!hasAdminPermission && !hasRequiredRole) {
      const embed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED' })
        .setDescription('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!')
        .setColor(message.client.embedColor || '#0099ff')
        .setTimestamp();
      return message.reply({ embeds: [embed] });
    }
    
    // Lấy danh sách tất cả người dùng có tiền cọc
    const allCash = await cashDB.getAllCash();
    
    // Lọc chỉ những người có số dư > 0
    const usersWithCash = allCash.filter(user => user.amount > 0);
    
    // Kiểm tra nếu không có ai có cash
    if (usersWithCash.length === 0) {
      const embed = new EmbedBuilder()
        .setColor(message.client.embedColor || '#0099ff')
        .setAuthor({
          name: 'TOP CASH RANKING',
          iconURL: message.client.user.displayAvatarURL() 
        })
        .setDescription('<:error:1383005371542798346> | Hiện chưa có user nào nạp cash trong server.')
        .setTimestamp()
        .setFooter({ text: `Yêu cầu bởi: ${message.author.tag}` });
      
      return await message.reply({ embeds: [embed] });
    }
    
    // Sắp xếp theo số tiền giảm dần
    usersWithCash.sort((a, b) => b.amount - a.amount);
    
    // Lấy top 10 người dùng
    const top10 = usersWithCash.slice(0, 10);
    
    // Tạo danh sách hiển thị
    let description = '';
    
    for (let i = 0; i < top10.length; i++) {
      try {
        const user = await message.client.users.fetch(top10[i].userId);
        description += `**${i + 1}.** ${user} - **${formatNumber(top10[i].amount)}** VND\n\n`;
      } catch (error) {
        // Nếu không tìm thấy user, hiển thị ID
        description += `**${i + 1}.** ID: ${top10[i].userId} - **${formatNumber(top10[i].amount)}** VND\n\n`;
      }
    }
    
    // Tạo embed hiển thị top cash
    const embed = new EmbedBuilder()
      .setColor(message.client.embedColor || '#0099ff')
      .setAuthor({
        name: 'TOP CASH RANKING',
        iconURL: message.client.user.displayAvatarURL() 
      })
      .setDescription(description)
      .setTimestamp()
      .setFooter({ text: `Yêu cầu bởi: ${message.author.tag}` });
    
    // Gửi embed
    await message.reply({ embeds: [embed] });
  } catch (error) {
    console.error('Lỗi khi kiểm tra top cash:', error);
    const embed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED' })
      .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi kiểm tra top cash. Vui lòng thử lại sau.')
      .setColor(message.client.embedColor || '#0099ff')
      .setTimestamp();
    await message.reply({ embeds: [embed] });
  }
}

// Xử lý lệnh topbook
async function handleTopBookCommand(message, args) {
  try {
    // Kiểm tra quyền hạn 
    const hasAdminPermission = message.member.permissions.has('Administrator');
    const hasRequiredRole = message.member.roles.cache.has('1376500798896214108');
      
    if (!hasAdminPermission && !hasRequiredRole) {
      const embed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED' })
        .setDescription('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!')
        .setColor(message.client.embedColor || '#0099ff')
        .setTimestamp();
      return message.reply({ embeds: [embed] });
    }
    
    // Xử lý tham số số lượng (mặc định là 10, tối đa 50)
    let limit = 10;
    if (args.length > 0) {
      const inputLimit = parseInt(args[0]);
      if (!isNaN(inputLimit) && inputLimit > 0 && inputLimit <= 50) {
        limit = inputLimit;
      } else if (!isNaN(inputLimit) && inputLimit > 50) {
        const embed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED' })
          .setDescription('<:error:1383005371542798346> | Số lượng tối đa là 50!')
          .setColor(message.client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [embed] });
      } else if (!isNaN(inputLimit) && inputLimit <= 0) {
        const embed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED' })
          .setDescription('<:error:1383005371542798346> | Số lượng phải lớn hơn 0!')
          .setColor(message.client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [embed] });
      }
    }
    
    // Lấy danh sách người dùng có giờ book
    const allBookers = await userBookingHistoryDB.getTopBookersByHours(limit);
    
    // Kiểm tra nếu không có ai có giờ book
    if (allBookers.length === 0) {
      const embed = new EmbedBuilder()
        .setColor(message.client.embedColor || '#0099ff')
        .setAuthor({
          name: 'TOP BOOKING RANKING',
          iconURL: message.client.user.displayAvatarURL() 
        })
        .setDescription('<:error:1383005371542798346> | Hiện chưa có user nào book trong server.')
        .setTimestamp()
        .setFooter({ text: `Yêu cầu bởi: ${message.author.tag}` });
      
      return await message.reply({ embeds: [embed] });
    }
    
    // Tạo danh sách hiển thị
    let description = '';
    
    for (let i = 0; i < allBookers.length; i++) {
      try {
        const user = await message.client.users.fetch(allBookers[i].userId);
        description += `**${i + 1}.** ${user} - **${allBookers[i].totalHours}** giờ\n\n`;
      } catch (error) {
        // Nếu không tìm thấy user, hiển thị ID
        description += `**${i + 1}.** ID: ${allBookers[i].userId} - **${allBookers[i].totalHours}** giờ\n\n`;
      }
    }
    
    // Tạo embed hiển thị top booking
    const embed = new EmbedBuilder()
      .setColor(message.client.embedColor || '#0099ff')
      .setAuthor({
        name: `TOP ${limit} BOOKING RANKING`,
        iconURL: message.client.user.displayAvatarURL() 
      })
      .setDescription(description)
      .setTimestamp()
      .setFooter({ text: `Yêu cầu bởi: ${message.author.tag}` });
    
    // Gửi embed
    await message.reply({ embeds: [embed] });
  } catch (error) {
    console.error('Lỗi khi kiểm tra top booking:', error);
    const embed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED' })
      .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi kiểm tra top booking. Vui lòng thử lại sau.')
      .setColor(message.client.embedColor || '#0099ff')
      .setTimestamp();
    await message.reply({ embeds: [embed] });
  }
}

// Thêm hàm xử lý lệnh topstar
async function handleTopPlayerBookCommand(message, args) {
  try {
    // Kiểm tra quyền hạn 
    const hasAdminPermission = message.member.permissions.has('Administrator');
    const hasRequiredRole = message.member.roles.cache.has('1376905574519799900');
    
    if (!hasAdminPermission && !hasRequiredRole) {
      const embed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED' })
        .setDescription('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!')
        .setColor(message.client.embedColor || '#0099ff')
        .setTimestamp();
      return message.reply({ embeds: [embed] });
    }
    
    // Xử lý tham số số lượng (mặc định là 10, tối đa 50)
    let limit = 10;
    if (args.length > 0) {
      const inputLimit = parseInt(args[0]);
      if (!isNaN(inputLimit) && inputLimit > 0 && inputLimit <= 50) {
        limit = inputLimit;
      } else if (!isNaN(inputLimit) && inputLimit > 50) {
        const embed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED' })
          .setDescription('<:error:1383005371542798346> | Số lượng tối đa là 50!')
          .setColor(message.client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [embed] });
      } else if (!isNaN(inputLimit) && inputLimit <= 0) {
        const embed = new EmbedBuilder()
          .setAuthor({ name: 'ACTION FAILED' })
          .setDescription('<:error:1383005371542798346> | Số lượng phải lớn hơn 0!')
          .setColor(message.client.embedColor || '#0099ff')
          .setTimestamp();
        return message.reply({ embeds: [embed] });
      }
    }
    
    // Lệnh topstar
    const topPlayers = await topStarDB.getTopStarPlayers(limit);
    // Thêm debug log
    console.log('Debug - TopStar players found:', topPlayers.length);
    console.log('Debug - TopStar data:', topPlayers);
    
    // Kiểm tra nếu không có player nào được book
    if (topPlayers.length === 0) {
      const embed = new EmbedBuilder()
        .setColor(message.client.embedColor || '#0099ff')
        .setAuthor({
          name: 'TOP STAR',
          iconURL: message.client.user.displayAvatarURL() 
        })
        .setDescription('<:error:1383005371542798346> | Hiện chưa có player nào được book trong server.')
        .setTimestamp()
        .setFooter({ text: `Yêu cầu bởi: ${message.author.tag}` });
      
      return await message.reply({ embeds: [embed] });
    }
    
    // Tạo danh sách top players
    let description = '';
    for (let i = 0; i < topPlayers.length; i++) {
      const player = topPlayers[i];
      const user = await message.client.users.fetch(player.playerId).catch(() => null);
      const playerName = user ? user.username : `Player ${player.playerId}`;
      
      description += `**${i + 1}.** ${playerName} **${player.totalHours}** giờ\n`;
    }
    
    const embed = new EmbedBuilder()
      .setColor(message.client.embedColor || '#0099ff')
      .setAuthor({
        name: `TOP ${limit} STAR`,
        iconURL: message.client.user.displayAvatarURL()
      })
      .setDescription(description)
      .setTimestamp()
      .setFooter({ text: `Yêu cầu bởi: ${message.author.tag}` });

    // Gửi embed
    await message.reply({ embeds: [embed] });
  } catch (error) {
    console.error('Lỗi khi kiểm tra top players được book:', error);
    const embed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED' })
      .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi kiểm tra top players được book. Vui lòng thử lại sau.')
      .setColor(message.client.embedColor || '#0099ff')
      .setTimestamp();
    await message.reply({ embeds: [embed] });
  }
}

  // Lệnh chuyển tiền
async function handleGiveCommand(message, args) {
  if (args.length < 2) {
    const embed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED' })
      .setDescription('<:error:1383005371542798346> | Cú pháp: ' + PREFIX + 'give @user <số tiền>')
      .setColor(message.client.embedColor || '#0099ff')
      .setTimestamp();
    return message.reply({ embeds: [embed] });
  }
  const mention = message.mentions.users.first();
  if (!mention || mention.id === message.author.id) {
    const embed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED' })
      .setDescription('<:error:1383005371542798346> | Bạn phải tag người nhận hợp lệ và không phải chính mình!')
      .setColor(message.client.embedColor || '#0099ff')
      .setTimestamp();
    return message.reply({ embeds: [embed] });
  }
  const amount = parseAmount(args[1]);
  if (!amount || amount < 1000) {
    const embed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED' })
      .setDescription('<:error:1383005371542798346> | Số tiền chuyển phải từ 1.000 VNĐ trở lên!')
      .setColor(message.client.embedColor || '#0099ff')
      .setTimestamp();
    return message.reply({ embeds: [embed] });
  }

  // Kiểm tra số dư
  const sender = message.author;
  const receiver = mention;
  const senderCash = await cashDB.getCash(sender.id);
  if (!senderCash || senderCash.amount < amount) {
    const embed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED' })
      .setDescription('<:error:1383005371542798346> | Số dư của bạn không đủ để chuyển!')
      .setColor(message.client.embedColor || '#0099ff')
      .setTimestamp();
    return message.reply({ embeds: [embed] });
  }

  // Trừ tiền người gửi, cộng tiền người nhận
  await cashDB.addCash(sender.id, -amount);
  await cashDB.addCash(receiver.id, amount);

  // Tạo embed xác nhận cho người gửi
  const embed = new EmbedBuilder()
    .setColor(message.client.embedColor || '#0099ff')
    .setAuthor({
      name: 'MONEY TRANSFERRED',
      iconURL: message.client.user.displayAvatarURL()
    })
    .setDescription(`| Bạn vừa chuyển **${formatNumber(amount)} VNĐ** cho ${receiver} thành công.`)
    .setFooter({ text: `Hôm nay lúc: ${new Date().toLocaleString('vi-VN')}` });

  await message.reply({ embeds: [embed] });

  // Tạo embed log cho kênh cash-log (thay "Bạn" thành user chuyển)
  const logEmbed = new EmbedBuilder()
    .setColor(message.client.embedColor || '#0099ff')
    .setAuthor({
      name: 'MONEY TRANSFERRED',
      iconURL: message.client.user.displayAvatarURL()
    })
    .setDescription(`| <@${sender.id}> vừa chuyển **${formatNumber(amount)} VNĐ** cho ${receiver} thành công.`)
    .setFooter({ text: `Hôm nay lúc: ${new Date().toLocaleString('vi-VN')}` });

  // Ghi log vào kênh cash-log
  const logChannel = await message.guild.channels.fetch('1377061589362020503').catch(() => null);
  if (logChannel) {
    await logChannel.send({ embeds: [logEmbed] });
  }
}

// Xử lý lệnh hac (add cash)
async function handleAddCashCommand(message, args) {
  // Kiểm tra quyền admin
  if (!message.member.permissions.has('ADMINISTRATOR')) {
    const embed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED' })
      .setDescription('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!')
      .setColor(message.client.embedColor || '#0099ff')
      .setTimestamp();
    return message.reply({ embeds: [embed] });
  }

  if (args.length < 2) {
    const embed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED' })
      .setDescription('<:error:1383005371542798346> | Cú pháp: ' + PREFIX + 'ac @user <số tiền cần thêm>')
      .setColor(message.client.embedColor || '#0099ff')
      .setTimestamp();
    return message.reply({ embeds: [embed] });
  }

  const mention = message.mentions.users.first();
  if (!mention) {
    const embed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED' })
      .setDescription('<:error:1383005371542798346> | Bạn phải tag người dùng hợp lệ!')
      .setColor(message.client.embedColor || '#0099ff')
      .setTimestamp();
    return message.reply({ embeds: [embed] });
  }

  const amount = parseAmount(args[1]);
  if (!amount || amount <= 0) {
    const embed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED' })
      .setDescription('<:error:1383005371542798346> | Số tiền phải là số dương hợp lệ!')
      .setColor(message.client.embedColor || '#0099ff')
      .setTimestamp();
    return message.reply({ embeds: [embed] });
  }

  try {
    const oldCash = await cashDB.getCash(mention.id);
    const newCash = await cashDB.addCash(mention.id, amount);

    // Tạo embed xác nhận
    const embed = new EmbedBuilder()
      .setColor(message.client.embedColor || '#0099ff')
      .setAuthor({
        name: 'ADD CASH',
        iconURL: message.client.user.displayAvatarURL()
      })
      .setDescription(`| Đã thêm **${formatNumber(amount)}** VNĐ cho ${mention}\n\nSố dư cũ: **${formatNumber(oldCash.amount)}**đ\nSố dư mới: **${formatNumber(newCash.amount)}**đ`)
      .setFooter({ text: `Thực hiện bởi: ${message.author.tag}` })
      .setTimestamp();

    await message.reply({ embeds: [embed] });

    // Ghi log vào kênh cash-log
    const logChannel = await message.guild.channels.fetch('1377061589362020503').catch(() => null);
    if (logChannel) {
      const logEmbed = new EmbedBuilder()
        .setColor(message.client.embedColor || '#0099ff')
        .setAuthor({
          name: 'ADD CASH',
          iconURL: message.client.user.displayAvatarURL()
        })
        .setDescription(`**Admin:** ${message.author}\n**Người nhận:** ${mention}\n**Số tiền thêm:** +${formatNumber(amount)}đ\n\nSố dư cũ: **${formatNumber(oldCash.amount)}**đ\nSố dư mới: **${formatNumber(newCash.amount)}**đ`)
        .setFooter({ text: `Hôm nay lúc: ${new Date().toLocaleString('vi-VN')}` });

      await logChannel.send({ embeds: [logEmbed] });
    }
  } catch (error) {
    console.error('Lỗi khi thêm cash:', error);
    const embed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED' })
      .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi thêm cash. Vui lòng thử lại sau.')
      .setColor(message.client.embedColor || '#0099ff')
      .setTimestamp();
    await message.reply({ embeds: [embed] });
  }
}

// Xử lý lệnh hsc (subtract cash)
async function handleSubtractCashCommand(message, args) {
  // Kiểm tra quyền admin
  if (!message.member.permissions.has('ADMINISTRATOR')) {
    const embed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED' })
      .setDescription('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!')
      .setColor(message.client.embedColor || '#0099ff')
      .setTimestamp();
    return message.reply({ embeds: [embed] });
  }

  if (args.length < 2) {
    const embed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED' })
      .setDescription('<:error:1383005371542798346> | Cú pháp: ' + PREFIX + 'sc @user <số tiền cần trừ>')
      .setColor(message.client.embedColor || '#0099ff')
      .setTimestamp();
    return message.reply({ embeds: [embed] });
  }

  const mention = message.mentions.users.first();
  if (!mention) {
    const embed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED' })
      .setDescription('<:error:1383005371542798346> | Bạn phải tag người dùng hợp lệ!')
      .setColor(message.client.embedColor || '#0099ff')
      .setTimestamp();
    return message.reply({ embeds: [embed] });
  }

  const amount = parseAmount(args[1]);
  if (!amount || amount <= 0) {
    const embed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED' })
      .setDescription('<:error:1383005371542798346> | Số tiền phải là số dương hợp lệ!')
      .setColor(message.client.embedColor || '#0099ff')
      .setTimestamp();
    return message.reply({ embeds: [embed] });
  }

  try {
    const oldCash = await cashDB.getCash(mention.id);
    
    // Kiểm tra số dư
    if (oldCash.amount < amount) {
      const embed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED' })
        .setDescription(`<:error:1383005371542798346> | Số dư không đủ! Người dùng chỉ có **${formatNumber(oldCash.amount)}**đ.`)
        .setColor(message.client.embedColor || '#0099ff')
        .setTimestamp();
      return message.reply({ embeds: [embed] });
    }

    const newCash = await cashDB.subtractCash(mention.id, amount);

    // Tạo embed xác nhận
    const embed = new EmbedBuilder()
      .setColor(message.client.embedColor || '#0099ff')
      .setAuthor({
        name: 'SUBTRACT CASH',
        iconURL: message.client.user.displayAvatarURL()
      })
      .setDescription(`| Đã trừ **${formatNumber(amount)}** VNĐ của ${mention}\n\nSố dư cũ: **${formatNumber(oldCash.amount)}**đ\nSố dư mới: **${formatNumber(newCash.amount)}**đ`)
      .setFooter({ text: `Thực hiện bởi: ${message.author.tag}` })
      .setTimestamp();

    await message.reply({ embeds: [embed] });

    // Ghi log vào kênh cash-log
    const logChannel = await message.guild.channels.fetch('1377061589362020503').catch(() => null);
    if (logChannel) {
      const logEmbed = new EmbedBuilder()
        .setColor(message.client.embedColor || '#0099ff')
        .setAuthor({
          name: 'SUBTRACT CASH',
          iconURL: message.client.user.displayAvatarURL()
        })
        .setDescription(`**Admin:** ${message.author}\n**Người bị trừ:** ${mention}\n**Số tiền trừ:** -${formatNumber(amount)}đ\n\nSố dư cũ: **${formatNumber(oldCash.amount)}**đ\nSố dư mới: **${formatNumber(newCash.amount)}**đ`)
        .setFooter({ text: `Hôm nay lúc: ${new Date().toLocaleString('vi-VN')}` });

      await logChannel.send({ embeds: [logEmbed] });
    }
  } catch (error) {
    console.error('Lỗi khi trừ cash:', error);
    const embed = new EmbedBuilder()
      .setAuthor({ name: 'ACTION FAILED' })
      .setDescription('<:error:1383005371542798346> | Đã xảy ra lỗi khi trừ cash. Vui lòng thử lại sau.')
      .setColor(message.client.embedColor || '#0099ff')
      .setTimestamp();
    await message.reply({ embeds: [embed] });
  }


}

module.exports = (client) => {
  // Lấy prefix từ client nếu có
  PREFIX = client.prefix || PREFIX;
  
  console.log(`Đã đăng ký lệnh cash với prefix: ${PREFIX}`);
  
  client.on('messageCreate', async (message) => {
    // Bỏ qua tin nhắn từ bot
    if (message.author.bot) return;
    
    // Kiểm tra xem tin nhắn có bắt đầu bằng prefix không (không phân biệt hoa/thường)
    if (!message.content.toLowerCase().startsWith(PREFIX.toLowerCase())) return;
    
    // Lấy lệnh từ tin nhắn
    const args = message.content.slice(PREFIX.length).trim().split(/ +/);
    const command = args.shift().toLowerCase();
    
    // Xử lý lệnh cash
    if (command === 'cash') {
      await logPrefixCommand(client, message, 'cash', args);
      await handleCashCommand(message);
    }
    // Xử lý lệnh topcash
    else if (command === 'topcash') {
      await logPrefixCommand(client, message, 'topcash', args);
      await handleTopCashCommand(message);
    }
    // Xử lý lệnh topbook
    else if (command === 'topbook') {
      await logPrefixCommand(client, message, 'topbook', args);
      await handleTopBookCommand(message, args);
    }
    // Xử lý lệnh topstar
    else if (command === 'topstar') {
      await logPrefixCommand(client, message, 'topstar', args);
      await handleTopPlayerBookCommand(message, args);
    }
    else if (command === 'give') {
      await logPrefixCommand(client, message, 'give', args);
      await handleGiveCommand(message, args);
    }
    // Xử lý lệnh ac (add cash)
    else if (command === 'ac') {
      await logPrefixCommand(client, message, 'ac', args);
      await handleAddCashCommand(message, args);
    }
    // Xử lý lệnh sc (subtract cash)
    else if (command === 'sc') {
      await logPrefixCommand(client, message, 'sc', args);
      await handleSubtractCashCommand(message, args);
    }
  });
  
  console.log('Đã đăng ký sự kiện xử lý lệnh tin nhắn thành công');
};