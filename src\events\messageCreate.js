const { logPrefixCommand } = require('../utils/commandLogger');

module.exports = (client) => {
  client.on('messageCreate', async (message) => {
    // Sửa dòng này để không phân biệt chữ hoa chữ thường khi kiểm tra prefix
    if (message.author.bot || !message.content.toLowerCase().startsWith(client.prefix.toLowerCase())) return;

    // Tách lệnh và các tham số
    const args = message.content.slice(client.prefix.length).trim().split(/ +/);
    const commandName = args.shift().toLowerCase();

    // Kiểm tra xem lệnh có tồn tại không
    const command = client.commands.get(commandName) ||
                    client.commands.find(cmd => cmd.aliases && cmd.aliases.includes(commandName));

    if (!command) return;

    // Log command usage
    await logPrefixCommand(client, message, commandName, args);

    // Th<PERSON><PERSON> thi lệnh
    try {
      await command.execute(client, message, args);
    } catch (error) {
      console.error(error);
      message.reply('Đ<PERSON> xảy ra lỗi khi thực hiện lệnh này!');
    }

    if (command === 'cash') {
      await handleCashCommand(message, args);
    }
    
    const autoRespond = client.commands.get('es_autorespond');
    if (autoRespond && typeof autoRespond.autoRespond === 'function') {
      autoRespond.autoRespond(message);
    } 
  });
};