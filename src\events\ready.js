const TranscriptManager = require('../utils/transcriptManager');

module.exports = {
  name: 'ready',
  once: true,
  execute(client) {

    console.log(`Đã đăng nhập với tên ${client.user.tag}`);
    console.log(`Bot đang hoạt động trên ${client.guilds.cache.size} server`);

    // Thiết lập trạng thái của bot
    client.user.setActivity(`${client.prefix}help`, { type: 'PLAYING' });

    // Thêm vào event ready
    const transcriptManager = new TranscriptManager();
    setInterval(async () => {
        await transcriptManager.cleanupOldTranscripts(30); // Xóa transcript cũ hơn 30 ngày
    }, 24 * 60 * 60 * 1000); // Chạy mỗi ngày
  },
};