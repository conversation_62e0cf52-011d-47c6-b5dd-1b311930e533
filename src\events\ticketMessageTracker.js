const { Events } = require('discord.js');
const ticketAutoReply = require('../utils/ticketAutoReply');

module.exports = {
  name: Events.MessageCreate,
  async execute(message) {
    try {
      // Bỏ qua tin nhắn từ bot
      if (message.author.bot) return;
      
      // Bỏ qua tin nhắn DM
      if (!message.guild) return;
      
      // Chỉ xử lý tin nhắn trong thread
      if (!message.channel.isThread()) return;
      
      // Kiểm tra xem có phải ticket thread không (dựa vào tên)
      const isTicketThread = message.channel.name.includes('Booking') || 
                            message.channel.name.includes('Support') || 
                            message.channel.name.includes('Tuyển dụng');
      
      if (!isTicketThread) return;
      
      // Xử lý tin nhắn trong ticket
      ticketAutoReply.handleMessage(message);
      
    } catch (error) {
      console.error('Lỗi trong ticket message tracker:', error);
    }
  }
};
