const { Events } = require('discord.js');
const weeklyMessageDB = require('../data/weeklyMessageDB');

module.exports = {
  name: Events.MessageCreate,
  async execute(message) {
    try {
      // Bỏ qua tin nhắn từ bot
      if (message.author.bot) return;
      
      // Bỏ qua tin nhắn DM
      if (!message.guild) return;
      
      // Kiểm tra xem kênh có đang được giám sát không
      const monitoredChannels = await weeklyMessageDB.getMonitoredChannels(message.guild.id);
      const isMonitored = monitoredChannels.some(channel => channel.channelId === message.channel.id);
      
      if (!isMonitored) return;
      
      // Thêm tin nhắn vào database
      await weeklyMessageDB.addMessage(
        message.author.id,
        message.channel.id,
        message.guild.id
      );
      
    } catch (error) {
      console.error('Lỗi trong weekly message tracker:', error);
    }
  }
};
