const { Client, GatewayIntentBits, Collection } = require('discord.js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();
const config = require('./config.json');

// Import Bank Notification System
const BankNotificationSystem = require('./utils/bankNotification');

// Import Weekly Scheduler
const { initWeeklyScheduler } = require('./utils/weeklyScheduler');


// Khởi tạo client Discord
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.GuildMembers,
    GatewayIntentBits.GuildVoiceStates,
    GatewayIntentBits.GuildMessageReactions, // Cần cho giveaway reactions
  ],
});

// Thiết lập prefix cho bot
client.prefix = process.env.PREFIX || 't';
client.embedColor = config.embedColor || '#0099ff'; // <PERSON><PERSON> ở đây, sau khi đã tạo client

// Tạo collection để lưu trữ lệnh
client.commands = new Collection();
client.slashCommands = new Collection(); 

// Đọc tất cả các file lệnh
const commandsPath = path.join(__dirname, 'commands');
const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));

for (const file of commandFiles) {
  const filePath = path.join(commandsPath, file);
  const command = require(filePath);
  
  if ('name' in command && 'execute' in command) {
    client.commands.set(command.name, command);
    console.log(`Đã tải lệnh: ${command.name}`);
  } else {
    console.log(`[CẢNH BÁO] Lệnh tại ${filePath} thiếu thuộc tính "name" hoặc "execute".`);
  }
}

// Đọc tất cả các file slash command
const slashCommandsPath = path.join(__dirname, 'slashCommands');
if (fs.existsSync(slashCommandsPath)) {
  const slashCommandFiles = fs.readdirSync(slashCommandsPath).filter(file => file.endsWith('.js'));
  
  for (const file of slashCommandFiles) {
    const filePath = path.join(slashCommandsPath, file);
    const command = require(filePath);
    
    if ('data' in command && 'execute' in command) {
      client.slashCommands.set(command.data.name, command);
      console.log(`Đã tải slash command: ${command.data.name}`);
    } else {
      console.log(`[CẢNH BÁO] Slash command tại ${filePath} thiếu thuộc tính "data" hoặc "execute".`);
    }
  }
} else {
  console.log('Thư mục slashCommands không tồn tại. Tạo thư mục này trước khi tiếp tục.');
}

// Đọc tất cả các file sự kiện
const eventsPath = path.join(__dirname, 'events');
const eventFiles = fs.readdirSync(eventsPath).filter(file => file.endsWith('.js'));

const { sendServiceInfo } = require('./utils/sendServiceInfo');

for (const file of eventFiles) {
  const filePath = path.join(eventsPath, file);
  const event = require(filePath);
  
  // Chỉ load các event file có cấu trúc đúng
  if (event.once) {
    client.once(event.name, (...args) => event.execute(...args));
  } else if (event.name) {
    client.on(event.name, (...args) => event.execute(...args));
  } else {
    // Đối với các file event không có cấu trúc standard (như messageCreate.js)
    if (typeof event === 'function') {
      event(client);
    }
  }
}

const snipes = new Collection();

client.on('messageDelete', message => {
    if (message.partial || !message.guild || !message.author) return;
    snipes.set(message.channel.id, {
        content: message.content,
        author: message.author,
        member: message.member,
        time: message.createdAt,
        id: message.author.id,
        avatar: message.author.displayAvatarURL()
    });
    // Lưu channelId ra biến riêng để tránh lỗi khi channel bị null sau này
    const channelId = message.channel.id;
    setTimeout(() => snipes.delete(channelId), 60000);
});

client.snipes = new Map();

const sendTicketPanel = require('./utils/sendTicketPanel');


// Kết nối đến Discord
client.login(process.env.TOKEN);


// Event handler cho interactionCreate đã được chuyển sang src/events/interactionCreate.js
// để tránh xử lý trùng lặp

// Gọi hàm này khi cần gửi panel (chỉ cần gọi 1 lần)
client.once('ready', () => {
  console.log(`🤖 Bot ${client.user.tag} đã sẵn sàng!`);

  // Khởi tạo Bank Notification System
  client.bankNotificationSystem = new BankNotificationSystem(client);
  console.log('🏦 Bank Notification System đã được khởi tạo!');

  // Khởi tạo Giveaway System
  const giveawayManager = require('./utils/giveawayManager');
  giveawayManager.initializeGiveawayTimers(client);
  console.log('🎉 Giveaway System đã được khởi tạo!');

  // Khởi tạo Notification System
  const notiCommand = require('./commands/noti');
  notiCommand.initializeNotifications(client);
  console.log('🔔 Notification System đã được khởi tạo!');

  // Khởi tạo Weekly Scheduler
  initWeeklyScheduler();
  console.log('📅 Weekly Message Scheduler đã được khởi tạo!');

  sendTicketPanel(client);
  sendServiceInfo(client);
});

// Đã disable sendShopRings vì shop rings đã được tích hợp vào menu embed
// const { sendShopRings } = require('./utils/sendShoprings');
// client.on('ready', () => {
//   sendShopRings(client);
// });

// Đóng kết nối database khi bot tắt
process.on('SIGINT', async () => {
  console.log('Đang đóng kết nối database...');
  try {
    const cashDB = require('./data/cashDB');
    await cashDB.close();
  } catch (error) {
    console.error('Lỗi khi đóng kết nối database:', error);
  }
  process.exit(0);
});