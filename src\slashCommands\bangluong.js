const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, PermissionFlagsBits } = require('discord.js');
const playerHistoryDB = require('../data/playerHistoryDB');
const donateHistoryDB = require('../data/donateHistoryDB');
const { formatNumber } = require('../utils/formatUtils');
const playerDonateSalaryDB = require('../data/playerDonateSalaryDB');
const playerBonusSalaryDB = require('../data/playerBonusSalaryDB');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('bangluong')
    .setDescription('Hiển thị bảng lương của tất cả player'),

  async execute(interaction) {
    try {
      // Chỉ admin mới được dùng lệnh
      if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
        return interaction.reply({ 
          content: '<:error:1383005371542798346> | <PERSON>ạn không có quyền sử dụng lệnh này!', 
          ephemeral: true 
        });
      }
      // Báo với Discord rằng bot đang xử lý
      await interaction.deferReply();
      
      // Thu thập dữ liệu lương của mỗi player
      const playerStats = [];
      
      // Lấy danh sách tất cả player (người có role Player)
      const PLAYER_ROLE_ID = '1376905574519799900';
      const guild = interaction.guild;
      const members = await guild.members.fetch();
      const playersWithRole = members.filter(member => member.roles.cache.has(PLAYER_ROLE_ID));

      // Lấy danh sách playerId đã từng nhận donate
      const allDonateHistory = await donateHistoryDB.getAllDonateHistory();
      const donatePlayerIds = new Set(allDonateHistory.map(entry => entry.playerId));

      // Gộp danh sách playerId từ role và playerId từng nhận donate
      const allPlayerIds = new Set([
        ...playersWithRole.map(member => member.id),
        ...donatePlayerIds
      ]);

      if (allPlayerIds.size === 0) {
        return interaction.editReply({ content: '<:error:1383005371542798346> | Không tìm thấy player nào trong server!', ephemeral: true });
      }

      // Duyệt qua từng player
      for (const playerId of allPlayerIds) {
        const member = members.get(playerId);
        
        // DEBUG: Log player ID đang xử lý
        console.log(`Đang xử lý player: ${playerId} (${member ? member.user.username : 'Unknown'})`);

        // Lấy lịch sử book
        const bookHistory = await playerHistoryDB.getPlayerHistory(playerId);
        
        // DEBUG: Log dữ liệu lịch sử
        console.log(`Lịch sử của ${playerId}:`, bookHistory);
        
        // Phân loại và tính toán theo loại book
        let normalBookHours = 0;
        let normalBookTotal = 0;
        let gameBookHours = 0;
        let gameBookTotal = 0;
        let oncamBookHours = 0;
        let oncamBookTotal = 0;

        if (bookHistory && bookHistory.length > 0) {
          bookHistory.forEach(entry => {
            if (entry.type === 'book') {
              normalBookHours += entry.hours;
              normalBookTotal += entry.received;
            } else if (entry.type === 'game') {
              gameBookHours += entry.hours;
              gameBookTotal += entry.received;
            } else if (entry.type === 'oncam') {
              oncamBookHours += entry.hours;
              oncamBookTotal += entry.received;
            }
          });
        }

        // Tính tổng tiền book (đã trừ chiết khấu)
        const totalBookReceived = normalBookTotal + gameBookTotal + oncamBookTotal;

        // Lấy lịch sử donate (nếu cần tính deductedMoney)
        const donateHistory = allDonateHistory.filter(entry => entry.playerId === playerId);

        // Lịch sử donate
        const totalDonateRaw = await playerDonateSalaryDB.getPlayerDonateSalary(playerId) || 0;
        // Trừ hoa hồng 10% từ donate
        const totalDonate = totalDonateRaw * 0.9;
        
        // Tính số tiền đã bị trừ (từ lệnh /luong hoặc hoàn bill/donate)
        let deductedMoney = 0;
        donateHistory.forEach(entry => {
          if (entry.amount < 0) {
            deductedMoney += entry.amount; // amount đã là số âm
          }
        });
        
        // Lấy thưởng lương (cộng lương)
        const bonusSalary = await playerBonusSalaryDB.getPlayerBonusSalary(playerId);

        // Tính tổng thu nhập (book + donate + cộng lương + trừ lương)
        const totalEarned = totalBookReceived + totalDonate + bonusSalary + deductedMoney;
        
        // DEBUG: Log các giá trị tính toán
        console.log(`Player ${playerId}: bookReceived=${totalBookReceived}, donate=${totalDonate}, deducted=${deductedMoney}, total=${totalEarned}`);

        // Nếu player không có book, donate, cộng lương và trừ lương thì bỏ qua
        if (
          totalBookReceived === 0 &&
          totalDonate === 0 &&
          bonusSalary === 0 &&
          deductedMoney === 0
        ) {
          console.log(`Bỏ qua player ${playerId} vì không có dữ liệu`);
          continue;
        }

        playerStats.push({
          id: playerId,
          name: member ? member.user.username : `Unknown (${playerId})`,
          displayName: member ? member.displayName : `Unknown (${playerId})`,
          normalBookHours,
          normalBookTotal,
          gameBookHours,
          gameBookTotal,
          oncamBookHours,
          oncamBookTotal,
          totalBookReceived,
          totalDonate,
          bonusSalary,
          deductedMoney,
          totalEarned
        });
      }
      
      // Kiểm tra nếu không có player nào có lịch sử book
      if (playerStats.length === 0) {
        return interaction.editReply({ content: '<:error:1383005371542798346> | Không tìm thấy player nào có lịch sử book!', ephemeral: true });
      }
      
      // Sắp xếp theo tổng lương từ cao đến thấp
      playerStats.sort((a, b) => b.totalEarned - a.totalEarned);
      
      // Phân trang, mỗi trang 5 player
      const ITEMS_PER_PAGE = 5;
      const totalPages = Math.ceil(playerStats.length / ITEMS_PER_PAGE);
      
      // Hàm tạo embed cho một trang
      const generateEmbed = (page) => {
        const startIndex = (page - 1) * ITEMS_PER_PAGE;
        const endIndex = Math.min(startIndex + ITEMS_PER_PAGE, playerStats.length);
        const playersOnPage = playerStats.slice(startIndex, endIndex);
        
        // Tạo các trường cho embed thay vì dùng description
        const fields = [];
        
        playersOnPage.forEach(player => {
          fields.push({
            name: `**@${player.displayName}**`,
            value: 
                   `Book: ${player.normalBookHours}h - ${formatNumber(player.normalBookTotal)} VND\n` +
                   `Game: ${player.gameBookHours}h - ${formatNumber(player.gameBookTotal)} VND\n` +
                   `Oncam: ${player.oncamBookHours}h - ${formatNumber(player.oncamBookTotal)} VND\n\n` +
                   `Tổng giờ: ${player.normalBookHours + player.gameBookHours + player.oncamBookHours}h\n` +
                   `Tổng tiền: ${formatNumber(player.totalBookReceived)} VND\n` +
                   `Donate: ${formatNumber(player.totalDonate)} VND\n\n` +
                   `Cộng lương: ${formatNumber(player.bonusSalary)} VND\n\n` +
                   `Trừ lương: ${formatNumber(player.deductedMoney)} VND\n\n` +
                   `**Tổng lương**: ${formatNumber(player.totalEarned)} VND`,
            inline: false
          });
        });
        
        // Tính tổng cho tất cả player
        const totalAllBook = playerStats.reduce((sum, player) => sum + player.totalBookReceived, 0);
        const totalAllDonate = playerStats.reduce((sum, player) => sum + player.totalDonate, 0);
        const totalAllBonus = playerStats.reduce((sum, player) => sum + player.bonusSalary, 0);
        const totalAllDeducted = playerStats.reduce((sum, player) => sum + player.deductedMoney, 0);
        const totalAllEarned = playerStats.reduce((sum, player) => sum + player.totalEarned, 0);
        const totalAllHours = playerStats.reduce((sum, player) =>
          sum + player.normalBookHours + player.gameBookHours + player.oncamBookHours, 0);
        
        // Tính tổng chiết khấu (20% từ book và 10% từ donate)
        const totalBookBeforeDiscount = playerStats.reduce((sum, player) => 
          sum + player.normalBookTotal / 0.8 + player.gameBookTotal / 0.8 + player.oncamBookTotal / 0.8, 0);
        const totalDonateBeforeDiscount = playerStats.reduce((sum, player) => 
          sum + (player.totalDonate / 0.9), 0); // player.totalDonate đã được trừ 10%
        const totalDiscount = (totalBookBeforeDiscount - totalAllBook) + (totalDonateBeforeDiscount - totalAllDonate);
        
        // Thêm trường tổng tiền - chỉ hiển thị tổng số tiền phải trả cho player
        fields.push({
          name: '\u200B', // Dấu cách vô nghĩa
          value: `**Tổng tiền: ${formatNumber(totalAllEarned)} VND**\n` +
                 `**Tổng chiết khấu: ${formatNumber(totalDiscount)} VND**\n` +
                 `Trang ${page}/${totalPages}\n` +
                 `𝐓𝐡𝐞 𝐈𝐧𝐭𝐞𝐫𝐬𝐭𝐞𝐥𝐥𝐚𝐫 𝐁𝐨𝐨𝐤𝐢𝐧𝐠 - Hôm nay lúc ${new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}`,
          inline: false
        });
        
        return new EmbedBuilder()
          .setColor(interaction.client.embedColor || '#0099ff')
          .setTitle('BẢNG LƯƠNG')
          .addFields(fields)
          .setThumbnail(interaction.client.user.displayAvatarURL());
      };
      
      // Tạo các nút điều hướng
      const createButtons = (currentPage) => {
        const row = new ActionRowBuilder();
        
        // Nút Previous
        row.addComponents(
          new ButtonBuilder()
            .setCustomId(`bangluong_prev_${currentPage}`)
            .setLabel('◀️')
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(currentPage <= 1)
        );
        
        // Nút Next
        row.addComponents(
          new ButtonBuilder()
            .setCustomId(`bangluong_next_${currentPage}`)
            .setLabel('▶️')
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(currentPage >= totalPages)
        );
        
        return row;
      };
      
      // Gửi embed trang đầu tiên
      const currentPage = 1;
      const embed = generateEmbed(currentPage);
      const buttons = createButtons(currentPage);
      
      const message = await interaction.editReply({
        embeds: [embed],
        components: totalPages > 1 ? [buttons] : []
      });
      
      // Xử lý sự kiện khi người dùng nhấn nút
      if (totalPages > 1) {
        // Tạo collector để lắng nghe sự kiện button
        const collector = message.createMessageComponentCollector({
          filter: i => i.user.id === interaction.user.id && i.customId.startsWith('bangluong_'),
          time: 300000 // 5 phút
        });
        
        collector.on('collect', async (i) => {
          // Lấy thông tin từ customId
          const [_, action, pageStr] = i.customId.split('_');
          let page = parseInt(pageStr);
          
          if (action === 'prev') {
            page--;
          } else if (action === 'next') {
            page++;
          }
          
          // Cập nhật embed và buttons
          const newEmbed = generateEmbed(page);
          const newButtons = createButtons(page);
          
          await i.update({
            embeds: [newEmbed],
            components: [newButtons]
          });
        });
        
        collector.on('end', () => {
          // Xóa các nút khi hết thời gian
          interaction.editReply({
            embeds: [embed],
            components: []
          }).catch(console.error);
        });
      }
    } catch (error) {
      console.error('Lỗi khi tạo bảng lương:', error);
      
      // Xử lý phản hồi lỗi
      if (interaction.deferred) {
        await interaction.editReply({
          content: `<:error:1383005371542798346> | Đã xảy ra lỗi: ${error.message}`,
          ephemeral: true
        });
      } else {
        await interaction.reply({
          content: `<:error:1383005371542798346> | Đã xảy ra lỗi: ${error.message}`,
          ephemeral: true
        });
      }
    }
  }
};