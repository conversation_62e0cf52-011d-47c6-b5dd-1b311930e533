const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('bank')
    .setDescription('<PERSON><PERSON><PERSON><PERSON> lý hệ thống thông báo giao dịch ngân hàng')
    .addSubcommand(subcommand =>
      subcommand
        .setName('test')
        .setDescription('Test gửi thông báo giao dịch')
        .addIntegerOption(option =>
          option.setName('amount')
            .setDescription('Số tiền (VND)')
            .setRequired(true))
        .addStringOption(option =>
          option.setName('sender')
            .setDescription('Tên người gửi')
            .setRequired(true))
        .addStringOption(option =>
          option.setName('description')
            .setDescription('Nội dung chuyển khoản')
            .setRequired(false)))
    .addSubcommand(subcommand =>
      subcommand
        .setName('status')
        .setDescription('Kiểm tra trạng thái hệ thống'))
    .addSubcommand(subcommand =>
      subcommand
        .setName('balance')
        .setDescription('Kiểm tra số dư tài khoản')
        .addStringOption(option =>
          option.setName('bank')
            .setDescription('Chọn ngân hàng')
            .setRequired(true)
            .addChoices(
              { name: 'Vietcombank', value: 'vcb' },
              { name: 'Techcombank', value: 'tcb' },
              { name: 'BIDV', value: 'bidv' }
            )))
    .addSubcommand(subcommand =>
      subcommand
        .setName('config')
        .setDescription('Cấu hình hệ thống')
        .addChannelOption(option =>
          option.setName('channel')
            .setDescription('Channel để gửi thông báo')
            .setRequired(false))
        .addRoleOption(option =>
          option.setName('role')
            .setDescription('Role để ping khi có giao dịch')
            .setRequired(false))),

  async execute(interaction) {
    // Kiểm tra quyền: Chỉ admin hoặc staff mới được sử dụng
    const hasAdminPermission = interaction.member.permissions.has(PermissionFlagsBits.Administrator);
    const hasStaffRole = interaction.member.roles.cache.has('1376884726232514620') || 
                        interaction.member.roles.cache.has('1376500798896214108');
    
    if (!hasAdminPermission && !hasStaffRole) {
      const embed = new EmbedBuilder()
        .setAuthor({ name: 'ACCESS DENIED' })
        .setDescription('<:error:1383005371542798346>  | Bạn không có quyền sử dụng lệnh này!')
        .setColor('#ff0000')
        .setTimestamp();
      return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    const subcommand = interaction.options.getSubcommand();

    try {
      switch (subcommand) {
        case 'test':
          await this.handleTestTransaction(interaction);
          break;
        case 'status':
          await this.handleStatus(interaction);
          break;
        case 'balance':
          await this.handleBalance(interaction);
          break;
        case 'config':
          await this.handleConfig(interaction);
          break;
        default:
          await interaction.reply({ 
            content: 'Subcommand không hợp lệ!', 
            ephemeral: true 
          });
      }
    } catch (error) {
      console.error('Lỗi khi xử lý bank command:', error);
      await interaction.reply({ 
        content: 'Đã xảy ra lỗi khi xử lý lệnh!', 
        ephemeral: true 
      });
    }
  },

  async handleTestTransaction(interaction) {
    const amount = interaction.options.getInteger('amount');
    const sender = interaction.options.getString('sender');
    const description = interaction.options.getString('description') || 'Test transaction';

    // Lấy bank notification system từ client
    const bankSystem = interaction.client.bankNotificationSystem;
    
    if (!bankSystem) {
      return interaction.reply({ 
        content: '❌ Hệ thống thông báo ngân hàng chưa được khởi tạo!', 
        ephemeral: true 
      });
    }

    await interaction.deferReply({ ephemeral: true });

    try {
      const transaction = await bankSystem.addTestTransaction(amount, sender, description);
      
      const embed = new EmbedBuilder()
        .setTitle('✅ TEST TRANSACTION THÀNH CÔNG')
        .setColor('#00ff00')
        .addFields(
          { name: '💰 Số tiền', value: `${amount.toLocaleString()} VND`, inline: true },
          { name: '👤 Người gửi', value: sender, inline: true },
          { name: '📝 Nội dung', value: description, inline: false },
          { name: '🆔 Transaction ID', value: transaction.id, inline: true },
          { name: '⏰ Thời gian', value: transaction.time, inline: true }
        )
        .setTimestamp();

      await interaction.editReply({ embeds: [embed] });
      
    } catch (error) {
      console.error('Lỗi khi test transaction:', error);
      await interaction.editReply({ 
        content: '❌ Lỗi khi gửi test transaction!' 
      });
    }
  },

  async handleStatus(interaction) {
    const bankSystem = interaction.client.bankNotificationSystem;
    
    const embed = new EmbedBuilder()
      .setTitle('📊 TRẠNG THÁI HỆ THỐNG NGÂN HÀNG')
      .setColor('#0099ff')
      .addFields(
        { 
          name: '🔄 Monitoring', 
          value: bankSystem ? '✅ Đang hoạt động' : '❌ Không hoạt động', 
          inline: true 
        },
        { 
          name: '📢 Notification Channel', 
          value: `<#${bankSystem?.notificationChannelId || 'N/A'}>`, 
          inline: true 
        },
        { 
          name: '👥 Staff Role', 
          value: `<@&${bankSystem?.staffRoleId || 'N/A'}>`, 
          inline: true 
        },
        { 
          name: '📈 Giao dịch đã xử lý', 
          value: bankSystem?.lastTransactionIds?.size?.toString() || '0', 
          inline: true 
        },
        { 
          name: '⏱️ Tần suất check', 
          value: '30 giây/lần', 
          inline: true 
        },
        { 
          name: '🕐 Cập nhật lúc', 
          value: new Date().toLocaleString('vi-VN'), 
          inline: true 
        }
      )
      .setTimestamp();

    await interaction.reply({ embeds: [embed], ephemeral: true });
  },

  async handleBalance(interaction) {
    const bank = interaction.options.getString('bank');
    const bankSystem = interaction.client.bankNotificationSystem;
    
    if (!bankSystem) {
      return interaction.reply({ 
        content: '❌ Hệ thống ngân hàng chưa được khởi tạo!', 
        ephemeral: true 
      });
    }

    await interaction.deferReply({ ephemeral: true });

    try {
      const result = await bankSystem.checkBalance(bank);
      
      const embed = new EmbedBuilder()
        .setTitle(`💰 SỐ DƯ ${bank.toUpperCase()}`)
        .setColor(result.success ? '#00ff00' : '#ff0000')
        .addFields(
          { name: '🏦 Ngân hàng', value: bank.toUpperCase(), inline: true },
          { name: '📊 Trạng thái', value: result.success ? '✅ Thành công' : '❌ Lỗi', inline: true },
          { name: '💵 Số dư', value: result.success ? `${result.balance?.toLocaleString() || 'N/A'} VND` : 'Không thể lấy', inline: true },
          { name: '⏰ Cập nhật', value: new Date().toLocaleString('vi-VN'), inline: false }
        );

      if (!result.success) {
        embed.addFields({ name: '❌ Lỗi', value: result.error, inline: false });
      }

      await interaction.editReply({ embeds: [embed] });
      
    } catch (error) {
      console.error('Lỗi khi check balance:', error);
      await interaction.editReply({ 
        content: '❌ Lỗi khi kiểm tra số dư!' 
      });
    }
  },

  async handleConfig(interaction) {
    const channel = interaction.options.getChannel('channel');
    const role = interaction.options.getRole('role');

    if (!channel && !role) {
      return interaction.reply({ 
        content: '❌ Vui lòng chọn ít nhất một option để cấu hình!', 
        ephemeral: true 
      });
    }

    // Chỉ admin mới được config
    if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
      return interaction.reply({ 
        content: '❌ Chỉ Administrator mới có thể cấu hình hệ thống!', 
        ephemeral: true 
      });
    }

    const embed = new EmbedBuilder()
      .setTitle('⚙️ CẤU HÌNH HỆ THỐNG')
      .setColor('#ffa500')
      .setDescription('Cấu hình đã được cập nhật:')
      .setTimestamp();

    if (channel) {
      embed.addFields({ name: '📢 Notification Channel', value: `${channel}`, inline: true });
      // Cập nhật channel ID trong hệ thống
      if (interaction.client.bankNotificationSystem) {
        interaction.client.bankNotificationSystem.notificationChannelId = channel.id;
      }
    }

    if (role) {
      embed.addFields({ name: '👥 Staff Role', value: `${role}`, inline: true });
      // Cập nhật role ID trong hệ thống
      if (interaction.client.bankNotificationSystem) {
        interaction.client.bankNotificationSystem.staffRoleId = role.id;
      }
    }

    await interaction.reply({ embeds: [embed], ephemeral: true });
  }
};
