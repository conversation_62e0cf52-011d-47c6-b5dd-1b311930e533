const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const cashDB = require('../data/cashDB');
const playerHistoryDB = require('../data/playerHistoryDB');
const userBookingHistoryDB = require('../data/userBookingHistoryDB');
const addOnDB = require('../data/addOnDB');
const serverStatsDB = require('../data/serverStatsDB');
const { formatNumber } = require('../utils/formatUtils');

const PLAYER_ROLE_ID = '1376905574519799900';

// Hàm chuyển đổi đơn vị tiền tệ 
function parseAmount(str) {
  if (typeof str === 'number') return str;
  str = str?.toLowerCase().replace(/,/g, '').replace(/đ|vnđ/g, '').trim();
  let match = str.match(/^(\d+(\.\d+)?)([km]?)$/);
  if (!match) return NaN;
  let num = parseFloat(match[1]);
  let unit = match[3];
  if (unit === 'k') num *= 1000;
  if (unit === 'm') num *= 1000000;
  return Math.round(num);
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName('bill')
    .setDescription('add bill booking')
    .addStringOption(option =>
      option.setName('type')
        .setDescription('Loại bill')
        .setRequired(true)
        .addChoices(
          { name: 'Book', value: 'book' },
          { name: 'Game', value: 'game' },
          { name: 'Oncam', value: 'oncam' }
        ))
    .addUserOption(option =>
      option.setName('khach')
        .setDescription('Khách hàng')
        .setRequired(true))
    .addUserOption(option =>
      option.setName('player')
        .setDescription('Player được book')
        .setRequired(true))
    .addIntegerOption(option =>
      option.setName('hours')
        .setDescription('Số giờ')
        .setRequired(true))
    .addStringOption(option =>
      option.setName('pay')
        .setDescription('Phương thức thanh toán')
        .setRequired(true)
        .addChoices(
          { name: 'Bank', value: 'bank' },
          { name: 'Cọc', value: 'cọc' }
        ))
    .addUserOption(option =>
      option.setName('player2')
        .setDescription('Player thứ 2 (tùy chọn)')
        .setRequired(false))
    .addUserOption(option =>
      option.setName('player3')
        .setDescription('Player thứ 3 (tùy chọn)')
        .setRequired(false))
    .addUserOption(option =>
      option.setName('player4')
        .setDescription('Player thứ 4 (tùy chọn)')
        .setRequired(false))
    .addUserOption(option =>
      option.setName('player5')
        .setDescription('Player thứ 5 (tùy chọn)')
        .setRequired(false))
    .addStringOption(option =>
      option.setName('prices')
        .setDescription('Giá mỗi giờ (chỉ dùng cho game/oncam)')
        .setRequired(false))
    .addBooleanOption(option =>
      option.setName('svr')
        .setDescription('Về server riêng (+10k)')
        .setRequired(false))
    .addStringOption(option =>
      option.setName('themnguoi')
        .setDescription('Thêm người (nhập số tiền)')
        .setRequired(false))
    .addBooleanOption(option =>
      option.setName('giadem')
        .setDescription('Giá đêm (+5k/giờ cho mỗi player)')
        .setRequired(false)),
  async execute(interaction) {
    try {
      // Kiểm tra quyền: Administrator hoặc role cụ thể
      const hasAdminPermission = interaction.member.permissions.has(PermissionFlagsBits.Administrator);
      const hasSpecialRole = interaction.member.roles.cache.has('1376500798896214108') ||
                            interaction.member.roles.cache.has('1376884726232514620');

      if (!hasAdminPermission && !hasSpecialRole) {
        return interaction.reply({
          content: '<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này.',
          ephemeral: true
        });
      }

      // Báo với Discord rằng bot đang xử lý
      await interaction.deferReply();
      
      const type = interaction.options.getString('type');
      const khach = interaction.options.getUser('khach');
      const hours = interaction.options.getInteger('hours');
      const pricesRaw = interaction.options.getString('prices');
      const pay = interaction.options.getString('pay');
      const svr = interaction.options.getBoolean('svr') || false;
      const giadem = interaction.options.getBoolean('giadem') || false;
      const themNguoiRaw = interaction.options.getString('themnguoi');
      
      // Xử lý số tiền thêm người
      let themNguoiAmount = 0;
      if (themNguoiRaw) {
        themNguoiAmount = parseAmount(themNguoiRaw);
        if (!themNguoiAmount || themNguoiAmount < 1000) {
          return interaction.editReply({ content: '<:error:1383005371542798346> | Số tiền thêm người không hợp lệ! Vui lòng nhập số tiền >= 1000.', ephemeral: true });
        }
      }
      
      // Lấy tất cả players
      const players = [];
      const player1 = interaction.options.getUser('player');
      const player2 = interaction.options.getUser('player2');
      const player3 = interaction.options.getUser('player3');
      const player4 = interaction.options.getUser('player4');
      const player5 = interaction.options.getUser('player5');
      
      if (player1) players.push(player1);
      if (player2) players.push(player2);
      if (player3) players.push(player3);
      if (player4) players.push(player4);
      if (player5) players.push(player5);
      
      // Kiểm tra và validate tất cả players
      const validPlayers = [];
      const invalidPlayers = [];
      
      for (const player of players) {
        // Fetch player member một cách an toàn
        const playerMember = await interaction.guild.members.fetch(player.id).catch(err => {
          console.error('Lỗi khi fetch member:', err);
          return null;
        });
        
        if (!playerMember) {
          invalidPlayers.push(`${player} (không tìm thấy thông tin)`);
          continue;
        }

        // Kiểm tra role player
        if (!playerMember.roles.cache.has(PLAYER_ROLE_ID)) {
          invalidPlayers.push(`${player} (không có role Player)`);
          continue;
        }
        
        validPlayers.push(player);
      }
      
      // Kiểm tra nếu không có player hợp lệ nào
      if (validPlayers.length === 0) {
        let errorMessage = '<:error:1383005371542798346> | Không có player hợp lệ nào để lên bill!';
        if (invalidPlayers.length > 0) {
          errorMessage += '\n\n**Players không hợp lệ:**\n' + invalidPlayers.join('\n');
        }
        return interaction.editReply({ content: errorMessage, ephemeral: true });
      }
      
      // Thông báo nếu có player bị bỏ qua
      if (invalidPlayers.length > 0) {
        console.log('⚠️ Các player bị bỏ qua:', invalidPlayers);
      }

      // Xác định giá mỗi giờ
      let pricePerHour = 25000;
      if (type === 'game' || type === 'oncam') {
        pricePerHour = parseAmount(pricesRaw);
        if (!pricePerHour || pricePerHour < 1000) {
          return interaction.editReply({ content: '<:error:1383005371542798346> | Bạn phải nhập giá mỗi giờ hợp lệ cho game/oncam!', ephemeral: true });
        }
      }

      // Tính tổng tiền cho tất cả players
      const totalPerPlayer = pricePerHour * hours;
      let totalAmount = totalPerPlayer * validPlayers.length;
      
      // Thêm phí server riêng nếu được chọn
      if (svr) {
        totalAmount += 10000;
      }

      // Thêm phí gia đêm nếu được chọn (5k/giờ cho mỗi player)
      if (giadem) {
        const giademAmount = 5000 * hours * validPlayers.length;
        totalAmount += giademAmount;
      }

      // Thêm phí thêm người nếu được chọn
      if (themNguoiAmount > 0) {
        totalAmount += themNguoiAmount;
      }
      
      const commissionPerPlayer = Math.round(totalPerPlayer * 0.2);
      const receivedPerPlayer = totalPerPlayer - commissionPerPlayer;

      // Nếu khách chọn thanh toán bằng cọc
      if (pay === 'cọc') {
        const khachCash = await cashDB.getCash(khach.id);
        if (!khachCash || khachCash.amount < totalAmount) {
          return interaction.editReply({ content: `<:error:1383005371542798346> | Số dư của khách không đủ! Cần: ${totalAmount.toLocaleString('vi-VN')} VNĐ, Số dư hiện tại: ${khachCash ? khachCash.amount.toLocaleString('vi-VN') : 0} VNĐ`, ephemeral: true });
        }
        // Trừ tiền cọc
        const oldCash = await cashDB.getCash(khach.id);
        await cashDB.subtractCash(khach.id, totalAmount);
        const newCash = await cashDB.getCash(khach.id);

        // Gửi log giao dịch đến kênh lịch sử
        const transactionLogChannel = await interaction.client.channels.fetch('1372934589105963079').catch(err => null);
        if (transactionLogChannel) {
          const transactionLogEmbed = new EmbedBuilder()
            .setColor(interaction.client.embedColor || '#0099ff')
            .setAuthor({
              name: 'TRANSACTION HISTORY',
              iconURL: interaction.client.user.displayAvatarURL() 
            })
            .setDescription(`**Loại giao dịch: Book bằng tiền cọc (${validPlayers.length} players)**\n\nNgười dùng: ${khach}\n\nSố tiền trừ: **-${formatNumber(totalAmount)}**đ\nSố dư cũ: **${formatNumber(oldCash.amount)}**đ\nSố dư mới: **${formatNumber(newCash.amount)}**đ`)
            .setTimestamp()
            .setFooter({ text: `BookID: ${interaction.id}` });
          
          await transactionLogChannel.send({ embeds: [transactionLogEmbed] });
        }
      }

      // Ghi lịch sử cho tất cả players
      for (const player of validPlayers) {
        try {
          console.log(`🔄 Đang ghi lịch sử cho player: ${player.tag} (${player.id})`);
          console.log(`📊 Dữ liệu:`, {
            playerId: player.id,
            customerId: khach.id,
            hours: hours,
            totalPerPlayer: totalPerPlayer,
            receivedPerPlayer: receivedPerPlayer,
            type: type,
            pricePerHour: pricePerHour,
            bookId: `${interaction.id || Date.now()}`
          });

        await playerHistoryDB.addHistory(
          player.id,
          khach.id,
          hours,
          totalPerPlayer,
          receivedPerPlayer,
          type,
          pricePerHour,
          `${interaction.id || Date.now()}`
        );
        console.log(`✅ Đã ghi lịch sử thành công cho player: ${player.tag}`);
        } catch (error) {
          console.error(`<:error:1383005371542798346> Lỗi khi ghi lịch sử cho player ${player.tag}:`, error);
          console.error(`📋 Chi tiết lỗi:`, error.stack);
        }
      }
      
      
      // Lưu thông tin add-on vào database
      const billId = `${interaction.id || Date.now()}`;
      const currentTime = Date.now();

      // Lưu add-on SVR nếu có
      if (svr) {
        for (const player of validPlayers) {
          try {
            await addOnDB.addAddOn({
              billId: billId,
              khachId: khach.id,
              playerId: player.id,
              type: 'svr',
              amount: Math.round(10000 / validPlayers.length), // Chia đều cho các player
              description: 'Server riêng (+10k)',
              time: currentTime
            });
          } catch (error) {
            console.error('Lỗi khi lưu add-on SVR:', error);
          }
        }
      }

      // Lưu add-on gia đêm nếu có
      if (giadem) {
        const giademPerPlayer = 5000 * hours; // 5k/giờ cho mỗi player
        for (const player of validPlayers) {
          try {
            await addOnDB.addAddOn({
              billId: billId,
              khachId: khach.id,
              playerId: player.id,
              type: 'giadem',
              amount: giademPerPlayer,
              description: `Gia đêm (+5k/h x ${hours}h = +${formatNumber(giademPerPlayer)})`,
              time: currentTime
            });
          } catch (error) {
            console.error('Lỗi khi lưu add-on gia đêm:', error);
          }
        }
      }

      // Lưu add-on thêm người nếu có
      if (themNguoiAmount > 0) {
        for (const player of validPlayers) {
          try {
            await addOnDB.addAddOn({
              billId: billId,
              khachId: khach.id,
              playerId: player.id,
              type: 'themnguoi',
              amount: Math.round(themNguoiAmount / validPlayers.length), // Chia đều cho các player
              description: `Thêm người (+${formatNumber(themNguoiAmount)})`,
              time: currentTime
            });
          } catch (error) {
            console.error('Lỗi khi lưu add-on thêm người:', error);
          }
        }
      }

      // Cập nhật thống kê server (không bị ảnh hưởng bởi reset lương)
      try {
        const originalBookingAmount = totalPerPlayer * validPlayers.length; // Doanh thu booking gốc (trước chiết khấu)
        await serverStatsDB.updateBookingStats(hours * validPlayers.length, originalBookingAmount, type);
        console.log(`✅ Đã cập nhật thống kê server: ${hours * validPlayers.length}h, ${originalBookingAmount} VNĐ, type: ${type}`);
      } catch (error) {
        console.error('Lỗi khi cập nhật thống kê server:', error);
      }

      // Cập nhật tổng giờ và tổng tiền đã book cho khách
      await userBookingHistoryDB.addUserBooking(khach.id, hours * validPlayers.length, totalAmount);

      // Tạo embed xác nhận
      const playersText = validPlayers.map(p => `${p}`).join('\n');
      
      // Tạo text hiển thị phụ phí
      let extraText = '';
      const extras = [];
      if (themNguoiAmount > 0) extras.push('thêm người');
      if (svr) extras.push('về server riêng');
      if (giadem) extras.push('gia đêm');

      if (extras.length > 0) {
        extraText = ` ( ${extras.join(', ')} )`;
      }
      
      const embed = new EmbedBuilder()
        .setColor(interaction.client.embedColor || '#0099ff')
        .setTitle('ADD BILL')
        .setThumbnail(interaction.client.user.displayAvatarURL())
        .setDescription(
          `• **Khách:** ${khach}\n\n` +
          `• **Players (${validPlayers.length}):**\n${playersText}\n\n` +
          `• **Số Giờ:** ${hours}h\n\n` +
          `• **${pay === 'cọc' ? 'Tổng cọc' : 'Tổng tiền'}:** ${totalAmount.toLocaleString('vi-VN')} VNĐ${extraText}`
        )
        .setFooter({ text: `Hôm nay lúc: ${new Date().toLocaleString('vi-VN')}` });

      await interaction.editReply({ embeds: [embed] });

      // Gửi log vào kênh log
      const logChannel = interaction.client.channels.cache.get('1342001327638581311');
      if (logChannel) {
        const playersLogText = validPlayers.map(p => `<@${p.id}>`).join(', ');
        
        // Tạo text log cho phụ phí
        let extraLogText = '';
        const logExtras = [];
        if (themNguoiAmount > 0) logExtras.push('thêm người');
        if (svr) logExtras.push('về server riêng');
        if (giadem) logExtras.push('gia đêm');

        if (logExtras.length > 0) {
          extraLogText = ` (${logExtras.join(', ')})`;
        }
        
        const logEmbed = new EmbedBuilder()
          .setColor(interaction.client.embedColor || '#0099ff')
          .setTitle('ADD BILL')
          .setThumbnail(interaction.client.user.displayAvatarURL())
          .setDescription(
            `${playersLogText} đã được book **${hours}h** bởi <@${khach.id}>. Khách thanh toán bằng **${pay}**.\n\n` +
            `**Tổng số players:** ${validPlayers.length}\n` +
            `**Tổng tiền:** ${totalAmount.toLocaleString('vi-VN')} VNĐ${extraLogText}`
          )
          .setFooter({ text: `BillID: ${interaction.id}` });
        await logChannel.send({ embeds: [logEmbed] });
      }
    } catch (error) {
      console.error('Lỗi khi thực hiện lệnh bill:', error);
      
      // Xử lý phản hồi lỗi
      if (interaction.deferred) {
        await interaction.editReply({
          content: `<:error:1383005371542798346> | Đã xảy ra lỗi khi thực hiện lệnh: ${error.message}`,
          ephemeral: true
        });
      } else {
        await interaction.reply({
          content: `<:error:1383005371542798346> | Đã xảy ra lỗi khi thực hiện lệnh: ${error.message}`,
          ephemeral: true
        });
      }
    }
  }
};
