const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const cashDB = require('../data/cashDB');
const donateHistoryDB = require('../data/donateHistoryDB');
const { formatNumber } = require('../utils/formatUtils');
const playerDonateSalaryDB = require('../data/playerDonateSalaryDB'); // Thêm dòng này
const serverStatsDB = require('../data/serverStatsDB');

const PLAYER_ROLE_ID = '1376905574519799900';

// Hàm chuyển đổi đơn vị tiền tệ 
function parseAmount(str) {
  if (typeof str === 'number') return str;
  str = str.toLowerCase().replace(/,/g, '').replace(/đ|vnđ/g, '').trim();
  let match = str.match(/^(\d+(\.\d+)?)([km]?)$/);
  if (!match) return NaN;
  let num = parseFloat(match[1]);
  let unit = match[3];
  if (unit === 'k') num *= 1000;
  if (unit === 'm') num *= 1000000;
  return Math.round(num);
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName('donate')
    .setDescription('Tạo bill donate cho player')
    .addUserOption(option =>
      option.setName('khach')
        .setDescription('Khách hàng')
        .setRequired(true))
    .addUserOption(option =>
      option.setName('player')
        .setDescription('Player nhận donate')
        .setRequired(true))
    .addStringOption(option =>
      option.setName('prices')
        .setDescription('Số tiền donate')
        .setRequired(true))
    .addStringOption(option =>
      option.setName('pay')
        .setDescription('Phương thức thanh toán')
        .setRequired(true)
        .addChoices(
          { name: 'Bank', value: 'bank' },
          { name: 'Cọc', value: 'cọc' }
        )),
  async execute(interaction) {
    try {
      // Kiểm tra quyền: Administrator hoặc role cụ thể
      const hasAdminPermission = interaction.member.permissions.has(PermissionFlagsBits.Administrator);
      const hasSpecialRole = interaction.member.roles.cache.has('1376500798896214108') ||
                            interaction.member.roles.cache.has('1376884726232514620');

      if (!hasAdminPermission && !hasSpecialRole) {
        return interaction.reply({
          content: '<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này.',
          ephemeral: true
        });
      }

      // Báo với Discord rằng bot đang xử lý
      await interaction.deferReply();
      
      const khach = interaction.options.getUser('khach');
      const player = interaction.options.getUser('player');
      const pricesRaw = interaction.options.getString('prices');
      const pay = interaction.options.getString('pay');
      
      // Fetch player member một cách an toàn
      const playerMember = await interaction.guild.members.fetch(player.id).catch(err => {
        console.error('Lỗi khi fetch member:', err);
        return null;
      });
      
      if (!playerMember) {
        return interaction.editReply({ content: '<:error:1383005371542798346> | Không thể tìm thấy thông tin của player!', ephemeral: true });
      }

      // Chuyển đổi đơn vị tiền tệ
      const prices = parseAmount(pricesRaw);
      if (!prices || prices < 1000) {
        return interaction.editReply({ content: '<:error:1383005371542798346> | Số tiền donate không hợp lệ!', ephemeral: true });
      }

      // Kiểm tra role player
      if (!playerMember.roles.cache.has(PLAYER_ROLE_ID)) {
        return interaction.editReply({ content: '<:error:1383005371542798346> | Người này không phải player, không thể nhận donate!', ephemeral: true });
      }

      // Tính chiết khấu 10%
      const commission = Math.round(prices * 0.1);
      const received = prices - commission;

      // Nếu khách chọn thanh toán bằng cọc
      if (pay === 'cọc') {
        const khachCash = await cashDB.getCash(khach.id);
        if (!khachCash || khachCash.amount < prices) {
          return interaction.editReply({ content: `<:error:1383005371542798346> | Số dư cọc của khách không đủ! Số dư hiện tại: ${khachCash ? formatNumber(khachCash.amount) : 0} VNĐ`, ephemeral: true });
        }
        // Trừ tiền cọc
        const oldCash = await cashDB.getCash(khach.id);
        await cashDB.addCash(khach.id, -prices);
        const newCash = await cashDB.getCash(khach.id);
        
        // Gửi log giao dịch đến kênh lịch sử
        const transactionLogChannel = await interaction.client.channels.fetch('1379553680294019082').catch(err => null);
        if (transactionLogChannel) {
          const transactionLogEmbed = new EmbedBuilder()
            .setColor(interaction.client.embedColor || '#0099ff')
            .setAuthor({
              name: 'TRANSACTION HISTORY',
              iconURL: interaction.client.user.displayAvatarURL() 
            })
            .setDescription(`**Loại giao dịch: Donate bằng tiền cọc**
Người dùng: ${khach}

Số tiền trừ: **-${formatNumber(prices)}**đ
Số dư cũ: **${formatNumber(oldCash.amount)}**đ
Số dư mới: **${formatNumber(newCash.amount)}**đ`)
            .setTimestamp()
            .setFooter({ text: `DonateID: ${interaction.id}` });
          
          await transactionLogChannel.send({ embeds: [transactionLogEmbed] });
        }
      }

      // Lưu lịch sử donate vào database
      await donateHistoryDB.addDonateHistory({
        khachId: khach.id,
        playerId: player.id,
        amount: prices,
        received: received,
        commission: commission,
        paymentMethod: pay,
        time: Date.now(),
        donateId: `${interaction.id}`
      });

      // Cập nhật tổng donate lương cho player
      await playerDonateSalaryDB.addPlayerDonateSalary(player.id, prices);

      // Cập nhật thống kê server (không bị ảnh hưởng bởi reset lương)
      try {
        await serverStatsDB.updateDonateStats(prices); // prices là doanh thu gốc (trước chiết khấu)
        console.log(`✅ Đã cập nhật thống kê donate server: ${prices} VNĐ`);
      } catch (error) {
        console.error('Lỗi khi cập nhật thống kê donate server:', error);
      }

      // Tạo embed xác nhận
      const embed = new EmbedBuilder()
        .setColor(interaction.client.embedColor || '#0099ff')
        .setTitle('DONATION')
        .setThumbnail(interaction.client.user.displayAvatarURL())
        .setDescription(
          `• **Khách:** ${khach}\n\n` +
          `• **Player:** ${player}\n\n` +
          `• **Số tiền:** ${formatNumber(prices)} VNĐ\n\n`
        )
        .setFooter({ text: `Hôm nay lúc: ${new Date().toLocaleString('vi-VN')}` });

      await interaction.editReply({ embeds: [embed] });

      // Gửi log vào kênh log
      const logChannel = interaction.client.channels.cache.get('1342001327638581311');
      if (logChannel) {
        const logEmbed = new EmbedBuilder()
          .setColor(interaction.client.embedColor || '#0099ff')
          .setTitle('DONATION')
          .setThumbnail(interaction.client.user.displayAvatarURL())
          .setDescription(
            `<@${player.id}> đã nhận donate **${formatNumber(prices)} VNĐ** từ <@${khach.id}>.`
          )
          .setFooter({ text: `DonateID: ${interaction.id}` });
        await logChannel.send({ embeds: [logEmbed] });
      }
    } catch (error) {
      console.error('Lỗi khi thực hiện lệnh donate:', error);
      
      // Xử lý phản hồi lỗi
      if (interaction.deferred) {
        await interaction.editReply({
          content: `<:error:1383005371542798346> | Đã xảy ra lỗi khi thực hiện lệnh: ${error.message}`,
          ephemeral: true
        });
      } else {
        await interaction.reply({
          content: `<:error:1383005371542798346> | Đã xảy ra lỗi khi thực hiện lệnh: ${error.message}`,
          ephemeral: true
        });
      }
    }
  }
};