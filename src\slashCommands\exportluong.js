const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const playerHistoryDB = require('../data/playerHistoryDB');
const donateHistoryDB = require('../data/donateHistoryDB');
const playerBonusSalaryDB = require('../data/playerBonusSalaryDB');
const ExcelJS = require('exceljs');
const fs = require('fs');
const path = require('path');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('exportluong')
    .setDescription('Xuất file Excel bảng lương của tất cả player')
    .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

  async execute(interaction) {
    try {
      await interaction.deferReply();

      const guild = interaction.guild;
      const members = await guild.members.fetch();

      const PLAYER_ROLE_ID = '1376905574519799900';
      const playersWithRole = members.filter(member => member.roles.cache.has(PLAYER_ROLE_ID));

      // Lấy danh sách playerId đã từng nhận donate
      const allDonateHistory = await donateHistoryDB.getAllDonateHistory();
      const donatePlayerIds = new Set(allDonateHistory.map(entry => entry.playerId));

      // Gộp danh sách playerId từ role và playerId từng nhận donate
      const allPlayerIds = new Set([
        ...playersWithRole.map(member => member.id),
        ...donatePlayerIds
      ]);

      const playerStats = [];

      for (const playerId of allPlayerIds) {
        const member = members.get(playerId);

        // Lấy lịch sử book
        const bookHistory = await playerHistoryDB.getPlayerHistory(playerId);

        // Phân loại và tính toán theo loại book
        let normalBookHours = 0;
        let normalBookTotal = 0;
        let gameBookHours = 0;
        let gameBookTotal = 0;
        let oncamBookHours = 0;
        let oncamBookTotal = 0;

        if (bookHistory && bookHistory.length > 0) {
          bookHistory.forEach(entry => {
            if (entry.type === 'book') {
              normalBookHours += entry.hours;
              normalBookTotal += entry.received;
            } else if (entry.type === 'game') {
              gameBookHours += entry.hours;
              gameBookTotal += entry.received;
            } else if (entry.type === 'oncam') {
              oncamBookHours += entry.hours;
              oncamBookTotal += entry.received;
            }
          });
        }

        // Tính tổng tiền book (đã trừ chiết khấu)
        const totalBookReceived = normalBookTotal + gameBookTotal + oncamBookTotal;

        // Lấy lịch sử donate
        const donateHistory = allDonateHistory.filter(entry => entry.playerId === playerId);
        const totalDonate = donateHistory.reduce((sum, entry) => sum + (entry.amount > 0 ? entry.received : 0), 0);

        // Tính số tiền đã bị trừ (từ lệnh /luong hoặc hoàn bill/donate)
        let deductedMoney = 0;
        donateHistory.forEach(entry => {
          if (entry.amount < 0) {
            deductedMoney += entry.amount; // amount đã là số âm
          }
        });

        // Lấy thưởng lương (cộng lương)
        const bonusSalary = await playerBonusSalaryDB.getPlayerBonusSalary(playerId);

        // Tính tổng thu nhập (book + donate + cộng lương + trừ lương)
        const totalEarned = totalBookReceived + totalDonate + bonusSalary + deductedMoney;

        // Nếu player không có book, donate, cộng lương và trừ lương thì bỏ qua
        if (
          totalBookReceived === 0 &&
          totalDonate === 0 &&
          bonusSalary === 0 &&
          deductedMoney === 0
        ) {
          continue;
        }

        playerStats.push({
          id: playerId,
          name: member ? member.user.username : `Unknown (${playerId})`,
          displayName: member ? member.displayName : `Unknown (${playerId})`,
          normalBookHours,
          normalBookTotal,
          gameBookHours,
          gameBookTotal,
          oncamBookHours,
          oncamBookTotal,
          totalBookReceived,
          totalDonate,
          bonusSalary,
          deductedMoney,
          totalEarned
        });
      }

      // --- Phần xuất Excel ---
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Bảng lương');

      worksheet.columns = [
        { header: 'ID', key: 'id', width: 20 },
        { header: 'Tên', key: 'displayName', width: 25 },
        { header: 'Book (giờ)', key: 'normalBookHours', width: 12 },
        { header: 'Book (VNĐ)', key: 'normalBookTotal', width: 15 },
        { header: 'Game (giờ)', key: 'gameBookHours', width: 12 },
        { header: 'Game (VNĐ)', key: 'gameBookTotal', width: 15 },
        { header: 'Oncam (giờ)', key: 'oncamBookHours', width: 12 },
        { header: 'Oncam (VNĐ)', key: 'oncamBookTotal', width: 15 },
        { header: 'Donate', key: 'totalDonate', width: 15 },
        { header: 'Cộng lương', key: 'bonusSalary', width: 15 },
        { header: 'Trừ lương', key: 'deductedMoney', width: 15 },
        { header: 'Tổng lương', key: 'totalEarned', width: 15 }
      ];

      playerStats.forEach(player => worksheet.addRow(player));

      const filePath = path.join(__dirname, '../../bangluong.xlsx');
      await workbook.xlsx.writeFile(filePath);

      await interaction.editReply({
        content: 'Dưới đây là file Excel bảng lương:',
        files: [filePath]
      });

      setTimeout(() => fs.unlinkSync(filePath), 10000);
    } catch (error) {
      console.error('Lỗi khi xuất file Excel:', error);
      await interaction.editReply({
        content: `<:error:1383005371542798346> | Đã xảy ra lỗi: ${error.message}`,
        ephemeral: true
      });
    }
  }
};