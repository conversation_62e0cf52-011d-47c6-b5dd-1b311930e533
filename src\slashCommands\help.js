const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('help')
    .setDescription('Hiển thị danh sách lệnh và cách sử dụng')
    .addStringOption(option =>
      option.setName('command')
        .setDescription('Tên lệnh cần xem chi tiết')
        .setRequired(false)
        .addChoices(
          { name: 'Giveaway', value: 'giveaway' },
          { name: 'Bill', value: 'bill' },
          { name: 'Bank', value: 'bank' },
          { name: '<PERSON><PERSON>', value: 'luong' },
          { name: 'React', value: 'react' },
          { name: 'Profile Commands', value: 'profile' }
        )),

  async execute(interaction) {
    const commandName = interaction.options.getString('command');
    
    // Nếu có lệnh cụ thể, hiển thị chi tiết
    if (commandName) {
      let detailEmbed;
      
      switch (commandName) {
        case 'giveaway':
          detailEmbed = new EmbedBuilder()
            .setColor(interaction.client.embedColor || '#0099ff')
            .setTitle('<:cham:1391893252029808682> Giveaway')
            .setDescription(
              'Hệ thống tổ chức giveaway tự động\n\n' +
              '**Slash Commands**\n' +
              '`/giveaway start` - Tạo giveaway mới\n' +
              '`/giveaway end` - Kết thúc sớm\n' +
              '`/giveaway reroll` - Roll lại\n' +
              '`/giveaway list` - Xem danh sách\n\n' +
              '**Prefix Commands**\n' +
              '`tga start <time> <winners> <prize>` - Tạo giveaway\n' +
              '`tga end <message_id>` - Kết thúc sớm\n' +
              '`tga reroll <message_id>` - Roll lại\n' +
              '`tga help` - Hướng dẫn\n\n' +
              '**Định dạng thời gian**\n' +
              '`30s` - 30 giây | `5m` - 5 phút | `2h` - 2 giờ | `1d` - 1 ngày\n\n' +
              '**Ví dụ**\n' +
              '`tga start 1h 2 iPhone 15`\n' +
              '`/giveaway start duration:30m winners:1 prize:Discord Nitro`'
            );
          break;
          
        case 'bill':
          detailEmbed = new EmbedBuilder()
            .setColor(interaction.client.embedColor || '#0099ff')
            .setTitle('<:cham:1391893252029808682> Bill System')
            .setDescription(
              'Hệ thống quản lý hóa đơn và thanh toán\n\n' +
              '**Slash Commands**\n' +
              '`/bill create` - Tạo bill mới\n' +
              '`/bill list` - Xem danh sách bill\n' +
              '`/bill pay` - Thanh toán bill\n\n' +
              '**Tính năng**\n' +
              'React để xem profile player\n' +
              'Menu dropdown tương tác\n' +
              'Theo dõi trạng thái thanh toán\n' +
              'Thông báo tự động'
            );
          break;
          
        case 'profile':
          detailEmbed = new EmbedBuilder()
            .setColor(interaction.client.embedColor || '#0099ff')
            .setTitle('<:cham:1391893252029808682> Profile Commands')
            .setDescription(
              'Hệ thống quản lý profile player\n\n' +
              '**Lệnh cơ bản**\n' +
              '`tes description` - Tạo profile mới\n' +
              '`tes editprofile` - Chỉnh sửa profile\n' +
              '`tes setimage` - Đặt ảnh profile\n' +
              '`tes repostprofiles` - Đăng lại profiles\n\n' +
              '**Lệnh admin**\n' +
              '`tes set <type> <number> | @user` - Gán profile cho user\n' +
              '`tes set list` - Xem danh sách mapping\n' +
              '`tes set remove @user` - Xóa mapping\n\n' +
              '**Ví dụ**\n' +
              '`tes description murph 1`\n' +
              '`tes set joseph 2 | @fiin`\n' +
              '`tes editprofile staff 1`'
            );
          break;
          
        case 'bank':
          detailEmbed = new EmbedBuilder()
            .setColor(interaction.client.embedColor || '#0099ff')
            .setTitle('<:cham:1391893252029808682> Hướng dẫn Bank System')
            .setDescription('Hệ thống ngân hàng và giao dịch')
            .addFields(
              { name: 'Slash Commands', value: '`/bank balance` - Xem số dư\n`/bank transfer` - Chuyển tiền\n`/bank history` - Lịch sử giao dịch', inline: false },
              { name: 'Thông báo', value: 'Hệ thống tự động thông báo khi có giao dịch mới', inline: false }
            );
          break;
          
        case 'luong':
          detailEmbed = new EmbedBuilder()
            .setColor(interaction.client.embedColor || '#0099ff')
            .setTitle('<:cham:1391893252029808682> Hướng dẫn Luong System')
            .setDescription('Hệ thống quản lý lương nhân viên')
            .addFields(
              { name: 'Slash Commands', value: '`/luong view` - Xem lương\n`/luong add` - Thêm lương\n`/luong reset` - Reset lương\n`/bangluong` - Bảng lương tổng', inline: false }
            );
          break;
          
        case 'react':
          detailEmbed = new EmbedBuilder()
            .setColor(interaction.client.embedColor || '#0099ff')
            .setTitle('<:cham:1391893252029808682> Hướng dẫn React System')
            .setDescription('Hệ thống react và tương tác')
            .addFields(
              { name: 'Slash Commands', value: '`/react create` - Tạo react message\n`/react manage` - Quản lý react', inline: false }
            );
          break;
          
        default:
          return interaction.reply({ content: '❌ | Lệnh không hợp lệ', ephemeral: true });
      }
      
      return interaction.reply({ embeds: [detailEmbed], ephemeral: true });
    }
    
    // Hiển thị danh sách tổng quan
    const isAdmin = interaction.member.permissions.has(PermissionFlagsBits.Administrator);
    
    const helpEmbed = new EmbedBuilder()
      .setColor(interaction.client.embedColor || '#0099ff')
      .setTitle('COMMANDS DIRECTORY')
      .setDescription('Chọn một lệnh cụ thể để xem hướng dẫn chi tiết')
      .setThumbnail(interaction.client.user.displayAvatarURL({ dynamic: true }))
      .setTimestamp();

    // Lệnh cơ bản
    helpEmbed.addFields({
      name: '<:cham:1391893252029808682> Lệnh Cơ Bản',
      value: '`/help` - Hiển thị hướng dẫn\n`tping` - Kiểm tra độ trễ\n`tprofile` - Xem profile',
      inline: false
    });

    // Hệ thống chính
    helpEmbed.addFields({
      name: '<:cham:1391893252029808682> Giveaway System',
      value: '`/giveaway` hoặc `tga` - Tổ chức giveaway\nSử dụng `/help command:Giveaway` để xem chi tiết',
      inline: false
    });

    helpEmbed.addFields({
      name: '<:cham:1391893252029808682> Profile System', 
      value: '`tes description` - Tạo profile\n`tes editprofile` - Chỉnh sửa\n`tes set` - Gán profile cho user\nSử dụng `/help command:Profile Commands` để xem chi tiết',
      inline: false
    });

    helpEmbed.addFields({
      name: '<:cham:1391893252029808682> Bill & Bank System',
      value: '`/bill` - Quản lý hóa đơn\n`/bank` - Hệ thống ngân hàng\nSử dụng `/help command:Bill` hoặc `/help command:Bank` để xem chi tiết',
      inline: false
    });

    // Lệnh admin
    if (isAdmin) {
      helpEmbed.addFields({
        name: '<:cham:1391893252029808682> Admin Commands',
        value: '`tban/tunban` - Cấm/bỏ cấm\n`tkick` - Đuổi user\n`tmute/tunmute` - Tắt tiếng\n`tlock/tunlock` - Khóa channel\n`tpurge` - Xóa tin nhắn',
        inline: false
      });
    }

    // Thông tin bot
    const totalUsers = interaction.client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0);
    const totalGuilds = interaction.client.guilds.cache.size;
    
    helpEmbed.addFields({
      name: '<:cham:1391893252029808682> Lưu Ý',
      value: '• Prefix commands: `t` (ví dụ: `tping`)\n• Slash commands: `/` (ví dụ: `/help`)\n• Một số lệnh yêu cầu quyền admin',
      inline: false
    });

    helpEmbed.setFooter({ 
      text: 'Powered by Fiin HUB • Sử dụng /help command:<tên> để xem chi tiết' 
    });

    await interaction.reply({ embeds: [helpEmbed], ephemeral: true });
  }
};
