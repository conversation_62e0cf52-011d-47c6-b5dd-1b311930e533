const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const cashDB = require('../data/cashDB');
const playerHistoryDB = require('../data/playerHistoryDB');
const userBookingHistoryDB = require('../data/userBookingHistoryDB');
const playerDonateSalaryDB = require('../data/playerDonateSalaryDB');
const donateHistoryDB = require('../data/donateHistoryDB');
const topStarDB = require('../data/topStarDB');
const serverStatsDB = require('../data/serverStatsDB');
const addOnDB = require('../data/addOnDB');
const { formatNumber } = require('../utils/formatUtils');

// Hàm chuyển đổi đơn vị tiền tệ
function parseAmount(str) {
  if (typeof str === 'number') return str;
  str = str?.toLowerCase().replace(/,/g, '').replace(/đ|vnđ/g, '').trim();
  let match = str.match(/^(\d+(\.\d+)?)([km]?)$/);
  if (!match) return NaN;
  let num = parseFloat(match[1]);
  let unit = match[3];
  if (unit === 'k') num *= 1000;
  if (unit === 'm') num *= 1000000;
  return Math.round(num);
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName('hoanbill')
    .setDescription('Hoàn lại bill cho player')
    .addStringOption(option =>
      option.setName('type')
        .setDescription('Loại bill')
        .setRequired(true)
        .addChoices(
          { name: 'Book', value: 'book' },
          { name: 'Game', value: 'game' },
          { name: 'Oncam', value: 'oncam' }
        ))
    .addUserOption(option =>
      option.setName('khach')
        .setDescription('Khách hàng')
        .setRequired(true))
    .addUserOption(option =>
      option.setName('player')
        .setDescription('Player')
        .setRequired(true))
    .addIntegerOption(option =>
      option.setName('hours')
        .setDescription('Số giờ')
        .setRequired(true))
    .addUserOption(option =>
      option.setName('player2')
        .setDescription('Player 2')
        .setRequired(false))
    .addUserOption(option =>
      option.setName('player3')
        .setDescription('Player 3')
        .setRequired(false))
    .addUserOption(option =>
      option.setName('player4')
        .setDescription('Player 4')
        .setRequired(false))
    .addUserOption(option =>
      option.setName('player5')
        .setDescription('Player 5')
        .setRequired(false))
    .addStringOption(option =>
      option.setName('prices')
        .setDescription('Giá mỗi giờ (chỉ dùng cho game/oncam)')
        .setRequired(false))
    .addBooleanOption(option =>
      option.setName('night')
        .setDescription('Bill đêm (có giá đêm +5k/giờ)')
        .setRequired(false)),
  async execute(interaction) {
    try {
      // Kiểm tra quyền: Administrator hoặc role cụ thể
      const hasAdminPermission = interaction.member.permissions.has(PermissionFlagsBits.Administrator);
      const hasSpecialRole = interaction.member.roles.cache.has('1376500798896214108') ||
                            interaction.member.roles.cache.has('1376884726232514620');

      if (!hasAdminPermission && !hasSpecialRole) {
        return interaction.reply({
          content: '❌ | Bạn không có quyền sử dụng lệnh này.',
          ephemeral: true
        });
      }

      // Báo với Discord rằng bot đang xử lý
      await interaction.deferReply();
      
      const type = interaction.options.getString('type');
      const khach = interaction.options.getUser('khach');
      const hours = interaction.options.getInteger('hours');
      const pricesRaw = interaction.options.getString('prices');
      const isNightBill = interaction.options.getBoolean('night') || false;

      // Lấy tất cả players
      const players = [];
      const player1 = interaction.options.getUser('player');
      const player2 = interaction.options.getUser('player2');
      const player3 = interaction.options.getUser('player3');
      const player4 = interaction.options.getUser('player4');
      const player5 = interaction.options.getUser('player5');
      
      if (player1) players.push(player1);
      if (player2) players.push(player2);
      if (player3) players.push(player3);
      if (player4) players.push(player4);
      if (player5) players.push(player5);

      if (players.length === 0) {
        return interaction.editReply({ content: '<:error:1383005371542798346> | Phải có ít nhất một player để hoàn bill!', ephemeral: true });
      }

      // Xử lý từng player
      const refundResults = [];
      let totalRefundAmount = 0;

      for (const player of players) {
        // Lấy lịch sử bill của player từ SQLite
        const history = await playerHistoryDB.getHistory(player.id);
        // Tìm bill phù hợp
        const bill = history.find(b =>
          b.khach === khach.id &&
          b.hours === hours &&
          (b.type ? b.type === type : true)
        );
        
        if (!bill && (type !== 'game' && type !== 'oncam')) {
          refundResults.push({
            player: player,
            success: false,
            error: 'Không tìm thấy bill phù hợp để hoàn'
          });
          continue;
        }

        // Xác định tổng tiền hoàn lại
        let total = bill ? bill.total : null;

        // Nếu có bill và là night bill, cần cộng thêm phần gia đêm
        if (bill && isNightBill) {
          try {
            // Tìm night add-on tương ứng với bill này
            const nightAddOns = await addOnDB.getPlayerNightBills(player.id);
            const matchingNightAddOn = nightAddOns.find(addon => addon.billId === bill.billId);
            if (matchingNightAddOn) {
              total += matchingNightAddOn.amount; // Cộng thêm tiền gia đêm
              console.log(`✅ Đã cộng thêm ${matchingNightAddOn.amount} VNĐ gia đêm cho bill ${bill.billId}`);
            }
          } catch (error) {
            console.error('Lỗi khi lấy thông tin night add-on:', error);
          }
        }

        if ((type === 'game' || type === 'oncam') && !bill) {
          let pricePerHour = pricesRaw ? parseAmount(pricesRaw) : null;
          if (!pricePerHour || pricePerHour < 1000) {
            refundResults.push({
              player: player,
              success: false,
              error: 'Giá mỗi giờ không hợp lệ cho game/oncam'
            });
            continue;
          }
          total = pricePerHour * hours;

          // Thêm giá đêm nếu là night bill
          if (isNightBill) {
            total += (5000 * hours); // +5k/giờ
          }
        } else if (!bill && isNightBill) {
          // Trường hợp book night không có bill trong DB
          total = (25000 * hours) + (5000 * hours); // 25k book + 5k night per hour
        }

        try {
          console.log(`🔍 DEBUG: Processing player ${player.id}, bill exists:`, !!bill);
          console.log(`🔍 DEBUG: Hours value:`, hours);

          if (bill) {
            console.log(`🔍 DEBUG: Entering bill branch for player ${player.id}`);

            // Xóa record booking gốc bằng billId (chính xác hơn)
            await playerHistoryDB.removeHistoryByBillId(player.id, bill.billId);

            // Đánh dấu bill đã hoàn
            await playerHistoryDB.updateHistoryRefunded(player.id, bill.billId, true);

            console.log(`🔍 DEBUG: About to subtract TopStar for player ${player.id}`);

            // Trừ giờ trong TopStar database
            try {
              console.log(`🔄 DEBUG: Bắt đầu trừ TopStar cho player ${player.id} với ${hours} giờ (hoàn bill)`);
              console.log(`🔍 DEBUG: Bill data:`, bill);
              const result = await topStarDB.subtractPlayerTopStar(player.id, hours);
              console.log(`✅ DEBUG: Đã trừ TopStar thành công cho player ${player.id}, result:`, result);
            } catch (topStarError) {
              console.error('❌ DEBUG: Lỗi khi trừ TopStar:', topStarError);
            }

            // Xử lý night bills nếu có
            if (isNightBill) {
              try {
                // Đánh dấu night bills đã hoàn
                await addOnDB.markPlayerNightBillsPaid(player.id);
                console.log(`✅ Đã đánh dấu night bills của player ${player.id} là đã hoàn`);
              } catch (nightError) {
                console.error('❌ Lỗi khi xử lý night bills:', nightError);
              }
            }
          } else {
            // Đối với trường hợp game/oncam không có bill trong DB
            // Ta cần tạo một record âm để trừ lương
            await playerHistoryDB.addHistory(
              player.id,
              khach.username,
              -hours,
              -total,
              -total,
              type,
              Math.round(total / hours),
              bill.billId
            );

            // Trừ giờ trong TopStar database cho trường hợp game/oncam
            try {
              console.log(`🔄 DEBUG: Bắt đầu trừ TopStar cho player ${player.id} với ${hours} giờ (hoàn game/oncam)`);
              const result = await topStarDB.subtractPlayerTopStar(player.id, hours);
              console.log(`✅ DEBUG: Đã trừ TopStar thành công cho player ${player.id}, result:`, result);
            } catch (topStarError) {
              console.error('❌ DEBUG: Lỗi khi trừ TopStar:', topStarError);
            }
          }

          refundResults.push({
            player: player,
            success: true,
            amount: total,
            billId: bill ? bill.billId : `refund_${Date.now()}_${player.id}`
          });
          
          totalRefundAmount += total;
        } catch (error) {
          console.error(`Lỗi khi xử lý hoàn bill cho player ${player.id}:`, error);
          refundResults.push({
            player: player,
            success: false,
            error: 'Lỗi hệ thống khi xử lý hoàn bill'
          });
        }
      }

      // Cộng lại tiền cho khách (tổng số tiền từ tất cả players)
      if (totalRefundAmount > 0) {
        await cashDB.addCash(khach.id, totalRefundAmount);

        // Cập nhật tổng số giờ và tiền booking của khách hàng
        const successfulRefunds = refundResults.filter(r => r.success);
        if (successfulRefunds.length > 0) {
          await userBookingHistoryDB.updateUserBookingHours(khach.id, -hours * successfulRefunds.length);

          // Trừ chính xác số tiền đã hoàn (totalRefundAmount)
          const currentHistory = await userBookingHistoryDB.getUserBookingHistory(khach.id);
          await userBookingHistoryDB.setTotalBookedMoney(khach.id, currentHistory.totalBookedMoney - totalRefundAmount);

          // Trừ thống kê server
          try {
            const totalHoursRefunded = hours * successfulRefunds.length;
            await serverStatsDB.subtractBookingStats(totalHoursRefunded, totalRefundAmount, type);
            console.log(`✅ Đã trừ thống kê server: -${totalHoursRefunded}h, -${totalRefundAmount} VNĐ, type: ${type}`);
          } catch (error) {
            console.error('Lỗi khi trừ thống kê server:', error);
          }
        }
      }

      // Gửi log giao dịch đến kênh cash-logs
      if (totalRefundAmount > 0) {
        const cashLogChannel = await interaction.client.channels.fetch('1379553680294019082').catch(err => null);
        if (cashLogChannel) {
          const cashLogEmbed = new EmbedBuilder()
            .setColor(interaction.client.embedColor || '#0099ff')
            .setAuthor({
              name: 'TRANSACTION HISTORY',
              iconURL: interaction.client.user.displayAvatarURL() 
            })
            .setDescription(`**Loại giao dịch: Hoàn bill (${players.length} players)**\n\nNgười dùng: ${khach}\n\nSố tiền cộng: **+${totalRefundAmount.toLocaleString('vi-VN')}**đ\nSố dư mới: **${(await cashDB.getCash(khach.id)).amount.toLocaleString('vi-VN')}**đ`)
            .setTimestamp()
            .setFooter({ text: `HoanBillID: ${interaction.id}` });
          
          await cashLogChannel.send({ embeds: [cashLogEmbed] });
        }
      }

      // Tạo embed thông báo hoàn bill
      const successfulRefunds = refundResults.filter(r => r.success);
      const failedRefunds = refundResults.filter(r => !r.success);
      
      let description = `• **Khách:** ${khach}\n\n`;
      
      if (successfulRefunds.length > 0) {
        description += `• **Players hoàn thành công:**\n`;
        successfulRefunds.forEach(result => {
          description += `  - ${result.player}: +${result.amount.toLocaleString('vi-VN')} VNĐ\n`;
        });
        description += `\n• **Số Giờ:** ${hours}h${isNightBill ? ' (Bill đêm)' : ''}\n\n`;
        description += `• **Tổng cọc hoàn:** +${totalRefundAmount.toLocaleString('vi-VN')} VNĐ`;
      }
      
      if (failedRefunds.length > 0) {
        description += `\n\n• **Players thất bại:**\n`;
        failedRefunds.forEach(result => {
          description += `  - ${result.player}: ${result.error}\n`;
        });
      }

      const embed = new EmbedBuilder()
        .setColor(interaction.client.embedColor || '#0099ff')
        .setTitle('REFUND BILL')
        .setThumbnail(interaction.client.user.displayAvatarURL())
        .setDescription(description)
        .setFooter({ text: `Hôm nay lúc: ${new Date().toLocaleString('vi-VN')}` });

      await interaction.editReply({ embeds: [embed] });

      // Gửi log vào kênh log cho từng player thành công
      const logChannel = interaction.client.channels.cache.get('1342001327638581311');
      if (logChannel && successfulRefunds.length > 0) {
        for (const result of successfulRefunds) {
          // Tìm lại log cũ
          const fetched = await logChannel.messages.fetch({ limit: 100 });
          const logMsg = fetched.find(msg =>
            msg.embeds.length &&
            msg.embeds[0].data.description &&
            msg.embeds[0].data.description.includes(`<@${result.player.id}> đã được book ${hours}h bởi <@${khach.id}>`)
          );
          
          const refundEmbed = new EmbedBuilder()
            .setColor(interaction.client.embedColor || '#0099ff')
            .setTitle('REFUND BILL')
            .setThumbnail(interaction.client.user.displayAvatarURL())
            .setDescription(
              `Đã hoàn **${hours}h** từ <@${result.player.id}> về cho <@${khach.id}>.`
            )
            .setFooter({ text: `BillID: ${result.billId}` });
            
          if (logMsg) {
            await logMsg.reply({ embeds: [refundEmbed] });
          } else {
            await logChannel.send({ embeds: [refundEmbed] });
          }
        }
      }
    } catch (error) {
      console.error('Lỗi trong lệnh hoanbill:', error);
      const errorMessage = interaction.replied || interaction.deferred 
        ? { content: '<:error:1383005371542798346> | Đã xảy ra lỗi khi xử lý lệnh hoàn bill!', ephemeral: true }
        : { content: '<:error:1383005371542798346> | Đã xảy ra lỗi khi xử lý lệnh hoàn bill!', ephemeral: true };
      
      if (interaction.replied || interaction.deferred) {
        await interaction.editReply(errorMessage);
      } else {
        await interaction.reply(errorMessage);
      }
    }
  }
};