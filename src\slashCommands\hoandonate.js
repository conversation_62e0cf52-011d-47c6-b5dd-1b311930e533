const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const cashDB = require('../data/cashDB');
const donateHistoryDB = require('../data/donateHistoryDB');
const playerDonateSalaryDB = require('../data/playerDonateSalaryDB'); // THÊM DÒNG NÀY

const PLAYER_ROLE_ID = '1376905574519799900';

// Hàm chuyển đổi đơn vị tiền tệ (10k => 10000, 1m => 1000000)
function parseAmount(str) {
  if (typeof str === 'number') return str;
  str = str?.toLowerCase().replace(/,/g, '').replace(/đ|vnđ/g, '').trim();
  let match = str.match(/^(\d+(\.\d+)?)([km]?)$/);
  if (!match) return NaN;
  let num = parseFloat(match[1]);
  let unit = match[3];
  if (unit === 'k') num *= 1000;
  if (unit === 'm') num *= 1000000;
  return Math.round(num);
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName('hoandonate')
    .setDescription('Hoàn lại tiền đã donate cho khách')
    .addUserOption(option =>
      option.setName('khach')
        .setDescription('Khách hàng nhận hoàn tiền')
        .setRequired(true))
    .addUserOption(option =>
      option.setName('player')
        .setDescription('Player đã nhận donate')
        .setRequired(true))
    .addStringOption(option =>
      option.setName('prices')
        .setDescription('Số tiền hoàn lại (vd: 10k, 100k, 1m)')
        .setRequired(true)),

  async execute(interaction) {
    try {
      // Kiểm tra quyền: Administrator hoặc role cụ thể
      const hasAdminPermission = interaction.member.permissions.has(PermissionFlagsBits.Administrator);
      const hasSpecialRole = interaction.member.roles.cache.has('1376500798896214108') ||
                            interaction.member.roles.cache.has('1376884726232514620');

      if (!hasAdminPermission && !hasSpecialRole) {
        return interaction.reply({
          content: '❌ | Bạn không có quyền sử dụng lệnh này.',
          ephemeral: true
        });
      }

      // Lấy thông tin từ các options
      const khach = interaction.options.getUser('khach');
      const player = interaction.options.getUser('player');
      const pricesRaw = interaction.options.getString('prices');
      
      // Lấy thông tin member của player
      const playerMember = await interaction.guild.members.fetch(player.id);

      // Chuyển đổi đơn vị tiền tệ
      const prices = parseAmount(pricesRaw);
      if (!prices || prices < 1000) {
        return interaction.reply({ content: '❌ | Số tiền hoàn lại không hợp lệ!', ephemeral: true });
      }

      // Kiểm tra role player
      if (!playerMember.roles.cache.has(PLAYER_ROLE_ID)) {
        return interaction.reply({ content: '❌ | Người này không phải player, không thể hoàn tiền donate!', ephemeral: true });
      }

      // Cộng tiền vào cọc của khách
      await cashDB.addCash(khach.id, prices);

      // THÊM DÒNG NÀY: Trừ số tiền donate từ playerDonateSalaryDB
      await playerDonateSalaryDB.subtractPlayerDonateSalary(player.id, prices);

      // Lấy số dư mới của khách
      const newKhachCash = await cashDB.getCash(khach.id);

      // Gửi log giao dịch đến kênh cash-logs
      const cashLogChannel = await interaction.client.channels.fetch('1379553680294019082').catch(err => null);
      if (cashLogChannel) {
        const cashLogEmbed = new EmbedBuilder()
          .setColor(interaction.client.embedColor || '#0099ff')
          .setAuthor({
            name: 'TRANSACTION HISTORY',
            iconURL: interaction.client.user.displayAvatarURL() 
          })
          .setDescription(`**Loại giao dịch: Hoàn donate**

Người dùng: ${khach}

Số tiền cộng: **+${prices.toLocaleString('vi-VN')}**đ
Số dư mới: **${newKhachCash.amount.toLocaleString('vi-VN')}**đ`)
          .setTimestamp()
          .setFooter({ text: `HoanDonateID: ${interaction.id}` });
        
        await cashLogChannel.send({ embeds: [cashLogEmbed] });
      }

      // Lưu lịch sử hoàn tiền donate (số âm để đánh dấu là hoàn tiền)
      // Điều này sẽ trừ số tiền donate trong file data của player
      await donateHistoryDB.addDonateHistory({
        khachId: khach.id,
        playerId: player.id,
        amount: -prices, // Số âm để đánh dấu là hoàn tiền và trừ vào lương
        received: 0,
        commission: 0,
        paymentMethod: 'hoandonate',
        time: Date.now(),
        donateId: `hoandonate_${interaction.id}`
      });

      // Tạo embed xác nhận
      const embed = new EmbedBuilder()
        .setColor(interaction.client.embedColor || '#0099ff')
        .setTitle('REFUND DONATE')
        .setThumbnail(interaction.client.user.displayAvatarURL())
        .setDescription(
            `• **Khách:** ${khach}\n\n` +
            `• **Player:** ${player}\n\n` +
            `• **Số tiền:** ${prices.toLocaleString('vi-VN')} VNĐ\n\n` 
        )
        .setTimestamp()
        .setFooter({ text: 'Booking Bot' });

      // Phản hồi với embed
      await interaction.reply({ embeds: [embed] });

      // Gửi log vào kênh log
      const logChannel = interaction.client.channels.cache.get('1342001327638581311');
      if (logChannel) {
        const logEmbed = new EmbedBuilder()
          .setColor(interaction.client.embedColor || '#0099ff')
          .setTitle('REFUND DONATE')
          .setThumbnail(interaction.client.user.displayAvatarURL())
          .setDescription(
            `<@${player.id}> đã hoàn lại **${prices.toLocaleString('vi-VN')} VNĐ** cho <@${khach.id}>.`
          )
          .setTimestamp()
          .setFooter({ text: `HoanDonateID: ${interaction.id}` });
        await logChannel.send({ embeds: [logEmbed] });
      }

    } catch (error) {
      console.error('Lỗi khi thực hiện lệnh hoandonate:', error);
      await interaction.reply({
        content: '<:error:1383005371542798346> | Đã xảy ra lỗi khi thực hiện lệnh. Vui lòng thử lại sau.',
        ephemeral: true
      });
    }
  }
};