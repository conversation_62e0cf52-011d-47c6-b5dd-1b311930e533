const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const cashDB = require('../data/cashDB');
const donateHistoryDB = require('../data/donateHistoryDB');
const playerBonusSalaryDB = require('../data/playerBonusSalaryDB');
const { formatNumber } = require('../utils/formatUtils');

// Hàm chuyển đổi đơn vị tiền tệ (10k => 10000, 1m => 1000000)
function parseAmount(str) {
  if (typeof str === 'number') return str;
  if (!str) return NaN;
  
  str = str.toLowerCase().replace(/,/g, '').replace(/đ|vnđ/g, '').trim();
  let match = str.match(/^(\d+(\.\d+)?)([km]?)$/);
  if (!match) return NaN;
  
  let num = parseFloat(match[1]);
  let unit = match[3];
  if (unit === 'k') num *= 1000;
  if (unit === 'm') num *= 1000000;
  
  return Math.round(num);
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName('luong')
    .setDescription('Cộng/trừ lương cho player')
    .addStringOption(option =>
      option.setName('type')
        .setDescription('Loại thao tác')
        .setRequired(true)
        .addChoices(
          { name: 'Cộng lương', value: 'add' },
          { name: 'Trừ lương', value: 'sub' }
        ))
    .addUserOption(option =>
      option.setName('player')
        .setDescription('Player cần cộng/trừ lương')
        .setRequired(true))
    .addStringOption(option =>
      option.setName('prices')
        .setDescription('Số tiền')
        .setRequired(true)),

  async execute(interaction) {
    try {
      await interaction.deferReply();

      // Chỉ admin mới được dùng lệnh
      const isAdmin = interaction.member.permissions.has(PermissionFlagsBits.Administrator);
      if (!isAdmin) {
        return interaction.editReply({
          content: '<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này! Chỉ admin mới có thể sử dụng.',
          ephemeral: true
        });
      }

      const PLAYER_ROLE_ID = '1376905574519799900';
      const type = interaction.options.getString('type');
      const player = interaction.options.getUser('player');
      const pricesRaw = interaction.options.getString('prices');

      // Fetch player member một cách an toàn
      const playerMember = await interaction.guild.members.fetch(player.id).catch(err => {
        console.error('Lỗi khi fetch member:', err);
        return null;
      });

      if (!playerMember) {
        return interaction.editReply({ content: '<:error:1383005371542798346> | Không thể tìm thấy thông tin của player!', ephemeral: true });
      }

      // Chỉ player mới bị trừ/cộng lương
      if (!playerMember.roles.cache.has(PLAYER_ROLE_ID)) {
        return interaction.editReply({ content: '<:error:1383005371542798346> | Người này không phải player, không thể cộng/trừ lương!', ephemeral: true });
      }

      // Chuyển đổi đơn vị tiền tệ
      const prices = parseAmount(pricesRaw);
      if (!prices || prices < 1000) {
        return interaction.editReply({ content: '<:error:1383005371542798346> | Số tiền không hợp lệ!', ephemeral: true });
      }
      
      // Cộng hoặc trừ lương (không thay đổi cash)
      let action;
      let amount;
      let received;

      if (type === 'add') {
        // CỘNG LƯƠNG: Lưu vào playerBonusSalaryDB
        action = 'cộng';
        amount = prices;
        received = amount; // KHÔNG trừ chiết khấu cho cộng lương

        // Cộng vào bảng thưởng lương
        await playerBonusSalaryDB.addPlayerBonusSalary(player.id, received);

      } else {
        // TRỪ LƯƠNG: Lưu vào donateHistoryDB như cũ
        action = 'trừ';
        amount = -prices; // Số âm cho trừ lương
        received = amount; // KHÔNG trừ chiết khấu cho trừ lương

        // Ghi vào lịch sử donate (với số âm cho trừ lương)
        await donateHistoryDB.addDonateHistory({
          khachId: interaction.user.id, // Người thực hiện lệnh
          playerId: player.id,
          amount: amount,
          received: received, // Không chiết khấu
          commission: 0, // Không có chiết khấu
          paymentMethod: 'luong',
          time: Date.now(),
          donateId: `luong_${interaction.id}`
        });
      }
      
      // Tạo embed thông báo
      const embed = new EmbedBuilder()
        .setColor(interaction.client.embedColor || '#0099ff')
        .setAuthor({
          name: type === 'add' ? 'CỘNG LƯƠNG' : 'TRỪ LƯƠNG',
          iconURL: interaction.client.user.displayAvatarURL() 
        })
        .setDescription(
          `Player: ${player}\n` +
          `Số tiền ${action}: **${type === 'add' ? '+' : ''}${formatNumber(amount)}**đ\n` 
        )
        .setTimestamp()
        .setFooter({ text: `Thực hiện bởi: ${interaction.user.tag}` });
      
      // Gửi thông báo đến kênh luong-logs
      const luongLogChannel = await interaction.client.channels.fetch('1377081349118365716').catch(err => null);
      if (luongLogChannel) {
        const luongLogEmbed = new EmbedBuilder()
          .setColor(interaction.client.embedColor || '#0099ff')
          .setAuthor({
            name: type === 'add' ? 'CỘNG LƯƠNG' : 'TRỪ LƯƠNG',
            iconURL: interaction.client.user.displayAvatarURL() 
          })
          .setDescription(`**Player:** ${player}
**Số tiền ${action}:** ${type === 'add' ? '+' : '-'}${formatNumber(prices)}đ`)
          .setTimestamp()
          .setFooter({ text: `Thực hiện bởi: ${interaction.user.tag}` });
        
        await luongLogChannel.send({ embeds: [luongLogEmbed] });
      }
      
      return interaction.editReply({ embeds: [embed] });
    } catch (error) {
      console.error('Lỗi khi cộng/trừ lương:', error);
      
      // Xử lý phản hồi lỗi
      if (interaction.deferred) {
        await interaction.editReply({
          content: `<:error:1383005371542798346> | Đã xảy ra lỗi: ${error.message}`,
          ephemeral: true
        });
      } else {
        await interaction.reply({
          content: `<:error:1383005371542798346> | Đã xảy ra lỗi: ${error.message}`,
          ephemeral: true
        });
      }
    }
  },
};