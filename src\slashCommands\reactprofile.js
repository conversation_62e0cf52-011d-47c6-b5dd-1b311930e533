const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, PermissionFlagsBits } = require('discord.js');
const ReactBillDB = require('../data/reactBillDB');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('react')
    .setDescription('Tạo react bill với profile')
    .addSubcommand(subcommand =>
      subcommand
        .setName('profile')
        .setDescription('Tạo react bill cho profile')
        .addStringOption(option =>
          option.setName('role')
            .setDescription('Chọn role cần ping')
            .setRequired(true)
            .addChoices(
              { name: 'Murph', value: '1376906613138391185' },
              { name: '<PERSON>', value: '1376906686115352587' },
              { name: '<PERSON>&<PERSON>', value: '1376905574519799900' },
              { name: 'test', value: '1387787470011105311'}
            ))
        .addStringOption(option =>
          option.setName('title')
            .setDescription('Nội dung cho react bill')
            .setRequired(true))),

  async execute(interaction) {
    // <PERSON>ểm tra quyền: Admin hoặc role cụ thể
    const hasAdminPermission = interaction.member.permissions.has(PermissionFlagsBits.Administrator);
    const hasSpecialRole = interaction.member.roles.cache.has('1379140917587349645') ||
                          interaction.member.roles.cache.has('1376500798896214108') ||
                          interaction.member.roles.cache.has('1376884726232514620');
    
    if (!hasAdminPermission && !hasSpecialRole) {
      const embed = new EmbedBuilder()
        .setAuthor({ name: 'ACTION FAILED' })
        .setDescription('<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!')
        .setColor(interaction.client.embedColor || '#0099ff')
        .setTimestamp();
      return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    // Lấy các tham số từ slash command
    const selectedRoleId = interaction.options.getString('role');
    const title = interaction.options.getString('title');

    // Lấy role object từ ID
    const selectedRole = interaction.guild.roles.cache.get(selectedRoleId);
    
    if (!selectedRole) {
      return interaction.reply({
        content: '<:error:1383005371542798346> | Không thể tìm thấy role được chọn!',
        ephemeral: true
      });
    }

    // Tạo nội dung với role mention và title
    const content = `${selectedRole} ${title}`;

    // Tạo các nút
    const row = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId('react_bill')
        .setLabel('React')
        .setStyle(ButtonStyle.Primary),
      new ButtonBuilder()
        .setCustomId('cancel_react')
        .setLabel('Cancel')
        .setStyle(ButtonStyle.Danger),
      new ButtonBuilder()
        .setCustomId('stop_react')
        .setLabel('Stop react')
        .setStyle(ButtonStyle.Secondary)
    );

    // Tạo embed
    const embed = new EmbedBuilder()
      .setAuthor({
        name: 'REACT BILL',
        iconURL: interaction.client.user.displayAvatarURL()
      })
      .setDescription('Danh sách React:')
      .setColor(interaction.client.embedColor || '#0099ff') 
      .setTimestamp()
      .setFooter({ 
        text: `${interaction.user.tag}`, 
        iconURL: interaction.user.displayAvatarURL() 
      });

    // Lấy kênh cố định với ID 1376967884953096344
    const targetChannel = interaction.client.channels.cache.get('1376967884953096344');
    
    if (!targetChannel) {
      return interaction.reply({ 
        content: 'Không thể tìm thấy kênh đích!', 
        ephemeral: true 
      });
    }

    try {
      // Gửi tin nhắn đến kênh cố định
      // Sau khi gửi tin nhắn (khoảng dòng 110)
      const sentMessage = await targetChannel.send({
        content: content,
        embeds: [embed],
        components: [row]
      });
      
      // Lưu thông tin kênh gốc (chưa có originalMessageId)
      ReactBillDB.saveOriginalChannel(sentMessage.id, interaction.channel.id, null);

      // Phản hồi cho người dùng
      await interaction.reply({ 
        content: `<:done:1383009630581424250> | Đã gửi react bill đến <#1376967884953096344>`, 
        ephemeral: true 
      });
    } catch (error) {
      console.error('Lỗi khi gửi react bill:', error);
      await interaction.reply({ 
        content: '<:error:1383005371542798346> | Có lỗi xảy ra khi gửi react bill!', 
        ephemeral: true 
      });
    }
  }
};