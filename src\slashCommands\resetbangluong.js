const { <PERSON>lash<PERSON><PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, PermissionFlagsBits, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const playerHistoryDB = require('../data/playerHistoryDB');
const donateHistoryDB = require('../data/donateHistoryDB');
const playerDonateSalaryDB = require('../data/playerDonateSalaryDB');
const addOnDB = require('../data/addOnDB');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('resetbangluong')
    .setDescription('Đặt lại toàn bộ bảng lương của tất cả players về 0')
    .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

  async execute(interaction) {
    try {
      // Kiểm tra quyền hạn (chỉ admin mới được sử dụng)
      if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
        return interaction.reply({ 
          content: '<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!', 
          ephemeral: true 
        });
      }

      // Tạo embed xác nhận
      const confirmEmbed = new EmbedBuilder()
        .setColor(interaction.client.embedColor || '#0099ff')
        .setTitle('⚠️ XÁC NHẬN RESET BẢNG LƯƠNG')
        .setThumbnail(interaction.client.user.displayAvatarURL())
        .setDescription(
          '**CẢNH BÁO: Hành động này sẽ xóa toàn bộ dữ liệu lương của TẤT CẢ players!**\n\n' +
          '📋 **Dữ liệu sẽ bị xóa:**\n' +
          '• Toàn bộ lịch sử book của tất cả players\n' +
          '• Toàn bộ lương từ donate của tất cả players\n' +
          '• Toàn bộ lịch sử trừ lương\n' +
          '• Toàn bộ night bills (gia đêm)\n\n' +
          '❗ **Hành động này KHÔNG THỂ HOÀN TÁC!**\n\n' +
          'Bạn có chắc chắn muốn tiếp tục?'
        )
        .setTimestamp()
        .setFooter({ text: 'INTERSTELLAR BOOKING' });

      // Tạo buttons xác nhận
      const confirmButton = new ButtonBuilder()
        .setCustomId('confirm_reset_bangluong')
        .setLabel('<:done:1383009630581424250> XÁC NHẬN RESET')
        .setStyle(ButtonStyle.Danger);

      const cancelButton = new ButtonBuilder()
        .setCustomId('cancel_reset_bangluong')
        .setLabel('<:error:1383005371542798346> HỦY BỎ')
        .setStyle(ButtonStyle.Secondary);

      const row = new ActionRowBuilder()
        .addComponents(confirmButton, cancelButton);

      // Gửi tin nhắn xác nhận
      const response = await interaction.reply({ 
        embeds: [confirmEmbed], 
        components: [row],
        ephemeral: true
      });

      // Tạo collector cho buttons
      const collectorFilter = i => {
        return i.user.id === interaction.user.id && 
               (i.customId === 'confirm_reset_bangluong' || i.customId === 'cancel_reset_bangluong');
      };

      try {
        const confirmation = await response.awaitMessageComponent({ 
          filter: collectorFilter, 
          time: 30000 
        });

        if (confirmation.customId === 'confirm_reset_bangluong') {
          // Thực hiện reset
          await confirmation.deferUpdate();

          // Lấy danh sách tất cả players có role Player
          const PLAYER_ROLE_ID = '1376905574519799900';
          const guild = interaction.guild;
          const members = await guild.members.fetch();
          const playersWithRole = members.filter(member => member.roles.cache.has(PLAYER_ROLE_ID));

          // Lấy danh sách playerId đã từng nhận donate
          const allDonateHistory = await donateHistoryDB.getAllDonateHistory();
          const donatePlayerIds = new Set(allDonateHistory.map(entry => entry.playerId));

          // Gộp danh sách playerId từ role và playerId từng nhận donate
          const allPlayerIds = new Set([
            ...playersWithRole.map(member => member.id),
            ...donatePlayerIds
          ]);

          let resetCount = 0;
          const errors = [];

          // Reset từng player
          for (const playerId of allPlayerIds) {
            try {
              // Xóa toàn bộ lịch sử book của player
              if (typeof playerHistoryDB.removeAllHistoryOfPlayer === 'function') {
                await playerHistoryDB.removeAllHistoryOfPlayer(playerId);
              }

              // Đặt lại lương từ book
              await playerHistoryDB.resetPlayerSalary(playerId);
              
              // Đặt lại lương từ donate
              await playerDonateSalaryDB.resetPlayerDonateSalary(playerId);

              // Reset night bills (gia đêm)
              await addOnDB.markPlayerNightBillsPaid(playerId);

              resetCount++;
            } catch (error) {
              console.error(`Lỗi khi reset player ${playerId}:`, error);
              errors.push(`Player ${playerId}: ${error.message}`);
            }
          }

          // Tạo embed kết quả
          const resultEmbed = new EmbedBuilder()
            .setColor(errors.length > 0 ? '#ff9900' : '#00ff00')
            .setTitle('<:done:1383009630581424250> RESET BẢNG LƯƠNG HOÀN TẤT')
            .setThumbnail(interaction.client.user.displayAvatarURL())
            .setDescription(
              `📊 **Kết quả reset:**\n` +
              `• Đã reset thành công: **${resetCount}** players\n` +
              `• Lỗi: **${errors.length}** players\n\n` +
              (errors.length > 0 ? `<:error:1383005371542798346> **Lỗi chi tiết:**\n${errors.slice(0, 5).map(err => `• ${err}`).join('\n')}${errors.length > 5 ? `\n• ... và ${errors.length - 5} lỗi khác` : ''}` : '✅ **Tất cả players đã được reset thành công!**')
            )
            .setTimestamp()
            .setFooter({ text: 'INTERSTELLAR BOOKING' });

          await confirmation.editReply({ 
            embeds: [resultEmbed], 
            components: [] 
          });

          // Gửi log vào kênh log
          const logChannel = interaction.client.channels.cache.get('1377081349118365716');
          if (logChannel) {
            const logEmbed = new EmbedBuilder()
              .setColor(interaction.client.embedColor || '#0099ff')
              .setTitle('RESET BẢNG LƯƠNG TỔNG')
              .setThumbnail(interaction.client.user.displayAvatarURL())
              .setDescription(
                `<@${interaction.user.id}> đã reset toàn bộ bảng lương.\n\n` +
                `📊 **Kết quả:**\n` +
                `• Reset thành công: **${resetCount}** players\n` +
                `• Lỗi: **${errors.length}** players`
              )
              .setTimestamp()
              .setFooter({ text: `ResetID: ${interaction.id}` });
            await logChannel.send({ embeds: [logEmbed] });
          }

        } else if (confirmation.customId === 'cancel_reset_bangluong') {
          // Hủy bỏ
          const cancelEmbed = new EmbedBuilder()
            .setColor(interaction.client.embedColor || '#0099ff')
            .setTitle('<:error:1383005371542798346> ĐÃ HỦY RESET')
            .setDescription('Reset bảng lương đã được hủy bỏ.')
            .setTimestamp();

          await confirmation.update({ 
            embeds: [cancelEmbed], 
            components: [] 
          });
        }

      } catch (error) {
        // Timeout hoặc lỗi collector
        const timeoutEmbed = new EmbedBuilder()
          .setColor(interaction.client.embedColor || '#0099ff')
          .setTitle('HẾT THỜI GIAN')
          .setDescription('Đã hết thời gian xác nhận. Reset bảng lương đã được hủy bỏ.')
          .setTimestamp();

        await interaction.editReply({ 
          embeds: [timeoutEmbed], 
          components: [] 
        });
      }

    } catch (error) {
      console.error('Lỗi khi thực hiện lệnh resetbangluong:', error);
      
      const errorEmbed = new EmbedBuilder()
        .setColor(interaction.client.embedColor || '#0099ff')
        .setTitle('<:error:1383005371542798346> LỖI HỆ THỐNG')
        .setDescription('Đã xảy ra lỗi khi thực hiện lệnh. Vui lòng thử lại sau.')
        .setTimestamp();

      if (interaction.replied || interaction.deferred) {
        await interaction.editReply({ embeds: [errorEmbed], components: [] });
      } else {
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
      }
    }
  }
};