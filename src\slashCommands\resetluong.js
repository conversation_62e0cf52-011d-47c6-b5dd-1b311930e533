const { Slash<PERSON><PERSON>mandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const playerHistoryDB = require('../data/playerHistoryDB');
const donateHistoryDB = require('../data/donateHistoryDB');
const totalSalaryDB = require('../data/totalSalaryDB'); // Thêm dòng này
const playerDonateSalaryDB = require('../data/playerDonateSalaryDB');
const playerBonusSalaryDB = require('../data/playerBonusSalaryDB');
const addOnDB = require('../data/addOnDB');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('resetluong')
    .setDescription('Đặt lại lương của player về 0')
    .addUserOption(option =>
      option.setName('player')
        .setDescription('Player cần reset lương')
        .setRequired(true))
    .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

  async execute(interaction) {
    try {
      // Lấy thông tin player
      const player = interaction.options.getUser('player');
      
      // Kiểm tra quyền hạn (chỉ admin mới được sử dụng)
      if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
        return interaction.reply({ 
          content: '<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!', 
          ephemeral: true 
        });
      }

      // Xóa toàn bộ lịch sử book của player
      if (typeof playerHistoryDB.removeAllHistoryOfPlayer === 'function') {
        await playerHistoryDB.removeAllHistoryOfPlayer(player.id);
      }

      // Đặt lại lương từ book
      await playerHistoryDB.resetPlayerSalary(player.id);
      
      // Đặt lại lương từ donate (chỉ reset số tiền lương, không xóa lịch sử)
      await playerDonateSalaryDB.resetPlayerDonateSalary(player.id);

      // Reset phần "Cộng lương" (playerBonusSalaryDB)
      await playerBonusSalaryDB.resetPlayerBonusSalary(player.id);

      // Reset phần "Trừ lương" - xóa các record có paymentMethod = 'luong'
      await donateHistoryDB.deleteLuongDonateByPlayer(player.id);

      // Reset night bills (gia đêm)
      await addOnDB.markPlayerNightBillsPaid(player.id);

      // Tạo embed thông báo
      const embed = new EmbedBuilder()
        .setColor(interaction.client.embedColor || '#0099ff')
        .setTitle('RESET LƯƠNG')
        .setThumbnail(interaction.client.user.displayAvatarURL())
        .setDescription(
          `Đã reset toàn bộ lương của ${player}.`
        )
        .setTimestamp()
        .setFooter({ text: '𝐓𝐡𝐞 𝐈𝐧𝐭𝐞𝐫𝐬𝐭𝐞𝐥𝐥𝐚𝐫 𝐁𝐨𝐨𝐤𝐢𝐧𝐠' });

      // Phản hồi với embed
      await interaction.reply({ embeds: [embed] });

      // Gửi log vào kênh log
      const logChannel = interaction.client.channels.cache.get('1377081349118365716');
      if (logChannel) {
        const logEmbed = new EmbedBuilder()
          .setColor(interaction.client.embedColor || '#0099ff')
          .setTitle('RESET LƯƠNG')
          .setThumbnail(interaction.client.user.displayAvatarURL())
          .setDescription(
            `<@${interaction.user.id}> đã đặt lại toàn bộ lương của <@${player.id}> về 0.\n\n` +
            `Reset: Bills, Donate, Cộng lương, Trừ lương, Night Bills`
          )
          .setTimestamp()
          .setFooter({ text: `ResetID: ${interaction.id}` });
        await logChannel.send({ embeds: [logEmbed] });
      }

    } catch (error) {
      console.error('Lỗi khi thực hiện lệnh resetluong:', error);
      await interaction.reply({
        content: '<:error:1383005371542798346> | Đã xảy ra lỗi khi thực hiện lệnh. Vui lòng thử lại sau.',
        ephemeral: true
      });
    }
  }
};