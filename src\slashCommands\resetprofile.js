const { SlashCommandBuilder, Embed<PERSON>uilder, PermissionFlagsBits } = require('discord.js');
const playerHistoryDB = require('../data/playerHistoryDB');
const donateHistoryDB = require('../data/donateHistoryDB');
const userBookingHistoryDB = require('../data/userBookingHistoryDB');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('resetprofile')
    .setDescription('Reset lại profile của một người dùng')
    .addUserOption(option =>
      option.setName('user')
        .setDescription('Người dùng cần reset profile')
        .setRequired(true))
    .addStringOption(option =>
      option.setName('type')
        .setDescription('Loại dữ liệu cần reset')
        .setRequired(true)
        .addChoices(
          { name: 'Tất cả', value: 'all' },
          { name: 'Chỉ lịch sử book', value: 'book' },
          { name: 'Chỉ lịch sử donate', value: 'donate' }
        ))
    .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

  async execute(interaction) {
    try {
      // Kiểm tra quyền admin
      if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
        return interaction.reply({
          content: '<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!',
          ephemeral: true
        });
      }

      await interaction.deferReply();

      const targetUser = interaction.options.getUser('user');
      const resetType = interaction.options.getString('type');

      // Thực hiện reset dữ liệu
      let bookReset = false;
      let donateReset = false;

      if (resetType === 'all' || resetType === 'book') {
        // Reset lịch sử book
        try {
          // Xóa lịch sử book của khách hàng
          const bookHistory = await playerHistoryDB.getCustomerHistory(targetUser.id);
          for (const entry of bookHistory) {
            await playerHistoryDB.removeHistory(entry.playerId, {
              khach: targetUser.id,
              hours: entry.hours,
              total: entry.total,
              type: entry.type,
              billId: entry.billId
            });
          }

          // Reset số giờ đã book
          if (userBookingHistoryDB && typeof userBookingHistoryDB.updateUserBookingHours === 'function') {
            const userHistory = await userBookingHistoryDB.getUserBookingHistory(targetUser.id);
            const totalBookMoney = userHistory.totalBookedMoney || 0;

            if (userHistory && userHistory.totalHours) {
              await userBookingHistoryDB.updateUserBookingHours(targetUser.id, -userHistory.totalHours);
            }
            // Reset tổng tiền đã book về 0
            if (typeof userBookingHistoryDB.getUserBookingHistory === 'function') {
              await userBookingHistoryDB.setTotalBookedMoney(targetUser.id, 0);
            }
          }
          
          bookReset = true;
        } catch (error) {
          console.error('Lỗi khi reset lịch sử book:', error);
        }
      }

      if (resetType === 'all' || resetType === 'donate') {
        // Reset lịch sử donate
        try {
          await donateHistoryDB.resetKhachDonateHistory(targetUser.id);
          donateReset = true;
        } catch (error) {
          console.error('Lỗi khi reset lịch sử donate:', error);
        }
      }

      // Tạo embed thông báo
      const embed = new EmbedBuilder()
        .setColor(interaction.client.embedColor || '#0099ff')
        .setTitle('RESET PROFILE')
        .setDescription(
          `Đã reset profile của ${targetUser}:\n\n` +
          `${resetType === 'all' || resetType === 'book' ? '✅' : '❌'} Lịch sử book\n` +
          `${resetType === 'all' || resetType === 'donate' ? '✅' : '❌'} Lịch sử donate`
        )
        .setTimestamp()
        .setFooter({ text: 'INTERSTELLAR BOOKING' });

      return interaction.editReply({ embeds: [embed] });
    } catch (error) {
      console.error('Lỗi khi reset profile:', error);
      return interaction.editReply({
        content: `<:error:1383005371542798346> | Đã xảy ra lỗi: ${error.message}`,
        ephemeral: true
      });
    }
  }
};