const { SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
const fs = require('fs');
const path = require('path');
const configPath = path.join(__dirname, '..', 'config.json');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('setcolor')
    .setDescription('Thay đổi màu mặc định của embed bot (chỉ admin)')
    .addStringOption(option =>
      option.setName('color')
        .setDescription('Mã màu HEX, ví dụ: #00ff00')
        .setRequired(true)
    )
    .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),
  async execute(interaction) {
    const color = interaction.options.getString('color');
    if (!/^#([0-9A-F]{6})$/i.test(color)) {
      return interaction.reply({ content: 'Vui lòng nhập mã màu HEX hợp lệ, ví dụ: #00ff00', ephemeral: true });
    }

    // Đọc config hiện tại
    let config = require(configPath);
    config.embedColor = color;
    // Ghi lại file config
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));

    // Cập nhật vào client
    interaction.client.embedColor = color;
    await interaction.reply(`Đã đổi màu embed mặc định thành ${color}`);
  },
};