const { SlashCommandBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('setprefix')
    .setDescription('Thay đổi prefix của bot')
    .addStringOption(option =>
      option.setName('prefix')
        .setDescription('Prefix mới')
        .setRequired(true)
    ),
  async execute(interaction) {
    const newPrefix = interaction.options.getString('prefix');
    // Lưu prefix mới vào biến môi trường runtime (chỉ có tác dụng khi bot đang chạy)
    interaction.client.prefix = newPrefix;

    // Ghi vào file .env
    const envPath = path.join(__dirname, '../../.env');
    let envContent = '';
    if (fs.existsSync(envPath)) {
      envContent = fs.readFileSync(envPath, 'utf8');
      if (/^PREFIX=/m.test(envContent)) {
        // Thay thế dòng PREFIX cũ
        envContent = envContent.replace(/^PREFIX=.*$/m, `PREFIX=${newPrefix}`);
      } else {
        // Thêm dòng PREFIX mới
        envContent += `\nPREFIX=${newPrefix}`;
      }
    } else {
      envContent = `PREFIX=${newPrefix}`;
    }
    fs.writeFileSync(envPath, envContent);

    // Ghi vào file config.json
    const configPath = path.join(__dirname, '../config.json');
    if (fs.existsSync(configPath)) {
      const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
      config.prefix = newPrefix;
      fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
    }

    await interaction.reply(`Prefix đã được thay đổi thành: \`${newPrefix}\``);
  }
};