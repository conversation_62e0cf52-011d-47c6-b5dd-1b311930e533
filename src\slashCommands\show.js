const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const playerHistoryDB = require('../data/playerHistoryDB');
const donateHistoryDB = require('../data/donateHistoryDB');
const addOnDB = require('../data/addOnDB');
const serverStatsDB = require('../data/serverStatsDB');
const { formatNumber } = require('../utils/formatUtils');

// Hàm parse ngày từ string dd/mm/yyyy sang timestamp UTC+7
function parseVietnameseDate(dateString) {
  if (!dateString) return null;

  const parts = dateString.split('/');
  if (parts.length !== 3) return null;

  const day = parseInt(parts[0], 10);
  const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed
  const year = parseInt(parts[2], 10);

  if (isNaN(day) || isNaN(month) || isNaN(year)) return null;
  if (day < 1 || day > 31 || month < 0 || month > 11 || year < 2020 || year > 2030) return null;

  // Tạo date object theo múi giờ UTC+7
  const date = new Date(year, month, day);

  // Kiểm tra ngày hợp lệ
  if (date.getDate() !== day || date.getMonth() !== month || date.getFullYear() !== year) {
    return null;
  }

  return date;
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName('show')
    .setDescription('Hiển thị tổng doanh thu của server')
    .addBooleanOption(option =>
      option.setName('today')
        .setDescription('Hiển thị doanh thu hôm nay (UTC+7)')
        .setRequired(false))
    .addStringOption(option =>
      option.setName('from_date')
        .setDescription('Ngày bắt đầu (dd/mm/yyyy) - UTC+7')
        .setRequired(false))
    .addStringOption(option =>
      option.setName('to_date')
        .setDescription('Ngày kết thúc (dd/mm/yyyy) - UTC+7')
        .setRequired(false))
    .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

  async execute(interaction) {
    try {
      // Kiểm tra quyền: Administrator hoặc role cụ thể
      const hasAdminPermission = interaction.member.permissions.has(PermissionFlagsBits.Administrator);
      const hasSpecialRole = interaction.member.roles.cache.has('1376500798896214108') || 
                            interaction.member.roles.cache.has('1376884726232514620');
      
      if (!hasAdminPermission && !hasSpecialRole) {
        return interaction.reply({
          content: '<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này.',
          ephemeral: true
        });
      }

      await interaction.deferReply();

      // Lấy options
      const showToday = interaction.options.getBoolean('today') || false;
      const fromDateStr = interaction.options.getString('from_date');
      const toDateStr = interaction.options.getString('to_date');

      // Kiểm tra conflict options
      if (showToday && (fromDateStr || toDateStr)) {
        return interaction.editReply({
          content: '<:error:1383005371542798346> | Không thể sử dụng `today` cùng với `from_date`/`to_date`!',
        });
      }

      if ((fromDateStr && !toDateStr) || (!fromDateStr && toDateStr)) {
        return interaction.editReply({
          content: '<:error:1383005371542798346> | Phải cung cấp cả `from_date` và `to_date`!',
        });
      }

      // Tính toán thời gian
      let startTime = null;
      let endTime = null;
      let dateRangeText = '';

      if (showToday) {
        // Logic hôm nay
        const now = new Date();
        const utcPlus7 = new Date(now.getTime() + (7 * 60 * 60 * 1000));

        const startOfDay = new Date(utcPlus7);
        startOfDay.setUTCHours(0, 0, 0, 0);
        startTime = startOfDay.getTime() - (7 * 60 * 60 * 1000);

        const endOfDay = new Date(utcPlus7);
        endOfDay.setUTCHours(23, 59, 59, 999);
        endTime = endOfDay.getTime() - (7 * 60 * 60 * 1000);

        dateRangeText = 'HÔM NAY';
      } else if (fromDateStr && toDateStr) {
        // Logic date range
        const fromDate = parseVietnameseDate(fromDateStr);
        const toDate = parseVietnameseDate(toDateStr);

        if (!fromDate) {
          return interaction.editReply({
            content: '<:error:1383005371542798346> | Ngày bắt đầu không hợp lệ! Định dạng: dd/mm/yyyy (ví dụ: 01/07/2025)',
          });
        }

        if (!toDate) {
          return interaction.editReply({
            content: '<:error:1383005371542798346> | Ngày kết thúc không hợp lệ! Định dạng: dd/mm/yyyy (ví dụ: 30/07/2025)',
          });
        }

        if (fromDate > toDate) {
          return interaction.editReply({
            content: '<:error:1383005371542798346> | Ngày bắt đầu phải nhỏ hơn hoặc bằng ngày kết thúc!',
          });
        }

        // Tạo timestamp cho từ 00:00:00 ngày bắt đầu đến 23:59:59 ngày kết thúc (UTC+7)
        fromDate.setHours(0, 0, 0, 0);
        startTime = fromDate.getTime() - (7 * 60 * 60 * 1000); // Chuyển về UTC

        toDate.setHours(23, 59, 59, 999);
        endTime = toDate.getTime() - (7 * 60 * 60 * 1000); // Chuyển về UTC

        dateRangeText = `${fromDateStr} - ${toDateStr}`;
      }

      // Lấy thống kê từ database riêng (không bị ảnh hưởng bởi reset lương)
      const bookingStats = await serverStatsDB.getBookingStats();
      const donateStats = await serverStatsDB.getDonateStats();

      let totalBookRevenue = 0;
      let totalBookHours = 0;
      let totalDonateRevenue = 0;

      if (showToday || (fromDateStr && toDateStr)) {
        // Lấy dữ liệu theo khoảng thời gian từ database chính
        console.log(`Lấy dữ liệu từ ${new Date(startTime)} đến ${new Date(endTime)}`);

        // Lấy booking history trong khoảng thời gian
        const allBookHistory = await playerHistoryDB.getAllHistory();
        const filteredBookHistory = allBookHistory.filter(entry =>
          entry.time >= startTime && entry.time <= endTime
        );

        // Tính tổng booking
        filteredBookHistory.forEach(entry => {
          const originalAmount = Math.round(entry.received / 0.8);
          totalBookRevenue += originalAmount;
          totalBookHours += entry.hours;
        });

        // Lấy donate history trong khoảng thời gian
        const allDonateHistory = await donateHistoryDB.getAllDonateHistory();
        const filteredDonateHistory = allDonateHistory.filter(entry =>
          entry.time >= startTime && entry.time <= endTime &&
          entry.amount > 0 && entry.paymentMethod !== 'luong'
        );

        // Tính tổng donate
        filteredDonateHistory.forEach(entry => {
          const originalAmount = Math.round(entry.received / 0.9);
          totalDonateRevenue += originalAmount;
        });

      } else {
        // Sử dụng dữ liệu tổng từ serverStatsDB
        if (bookingStats.totalRevenue === 0 && donateStats.totalRevenue === 0) {
          console.log('Đồng bộ dữ liệu lần đầu cho serverStatsDB...');
          const allBookHistory = await playerHistoryDB.getAllHistory();
          const allDonateHistory = await donateHistoryDB.getAllDonateHistory();
          await serverStatsDB.syncFromExistingData(allBookHistory, allDonateHistory);

          // Lấy lại sau khi đồng bộ
          const updatedBookingStats = await serverStatsDB.getBookingStats();
          const updatedDonateStats = await serverStatsDB.getDonateStats();

          totalBookRevenue = updatedBookingStats.totalRevenue || 0;
          totalBookHours = updatedBookingStats.totalHours || 0;
          totalDonateRevenue = updatedDonateStats.totalRevenue || 0;
        } else {
          // Sử dụng dữ liệu từ serverStatsDB
          totalBookRevenue = bookingStats.totalRevenue || 0;
          totalBookHours = bookingStats.totalHours || 0;
          totalDonateRevenue = donateStats.totalRevenue || 0;
        }
      }

      // Lấy thông tin add-on
      let totalAddOnRevenue = 0;

      if (showToday || (fromDateStr && toDateStr)) {
        // Lấy add-on trong khoảng thời gian
        const allAddOns = await addOnDB.getAllAddOns();
        const filteredAddOns = allAddOns.filter(entry =>
          entry.time >= startTime && entry.time <= endTime
        );
        totalAddOnRevenue = filteredAddOns.reduce((sum, entry) => sum + entry.amount, 0);
      } else {
        // Lấy tổng add-on
        const addOnData = await addOnDB.getTotalAddOns();
        totalAddOnRevenue = addOnData.totalAmount || 0;
      }

      // Tính tổng doanh thu và lợi nhuận
      const totalRevenue = totalBookRevenue + totalDonateRevenue; // KHÔNG cộng add-on vào doanh thu
      const totalBookCommission = totalBookRevenue - Math.round(totalBookRevenue * 0.8);
      const totalDonateCommission = totalDonateRevenue - Math.round(totalDonateRevenue * 0.9);
      const totalPlayerSalary = totalRevenue - totalBookCommission - totalDonateCommission; // Tổng lương trả cho player (không bao gồm add-on)
      const totalProfit = totalBookCommission + totalDonateCommission + totalAddOnRevenue; // Lợi nhuận = chiết khấu + add-on

      // Tạo embed hiển thị theo format mới
      const embed = new EmbedBuilder()
        .setColor(interaction.client.embedColor || '#0099ff')
        .setAuthor({
          name: dateRangeText ? `THỐNG KÊ ${dateRangeText}` : 'THỐNG KÊ',
          iconURL: interaction.client.user.displayAvatarURL()
        })
        .setThumbnail(interaction.client.user.displayAvatarURL())
        .setDescription([
          `<:cham:1391893252029808682> **Tổng Giờ**`,
          `${totalBookHours}h ( ${formatNumber(totalBookRevenue)} VNĐ )`,
          `• Server: ${formatNumber(totalBookCommission)} VNĐ`,
          ``,
          `<:cham:1391893252029808682> **Tổng Donate**`,
          `${formatNumber(totalDonateRevenue)} VNĐ`,
          `• Server: ${formatNumber(totalDonateCommission)} VNĐ`,
          ``,
          `<:cham:1391893252029808682> **Add on**`,
          `${formatNumber(totalAddOnRevenue)} VNĐ`,
          ``,
          `<:cham:1391893252029808682> **Doanh Thu**`,
          `${formatNumber(totalRevenue + totalAddOnRevenue)} VNĐ`,
          `<:cham:1391893252029808682> **Tổng Lương**`,
          `${formatNumber(totalPlayerSalary)} VNĐ`,
          `<:cham:1391893252029808682> **Lợi Nhuận**`,
          `${formatNumber(totalProfit)} VNĐ`
        ].join('\n'))
        .setFooter({
          text: dateRangeText ?
            `${dateRangeText} lúc ${new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit', timeZone: 'Asia/Ho_Chi_Minh' })}` :
            `Tổng tất cả lúc ${new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}`,
        });

      await interaction.editReply({ embeds: [embed] });

    } catch (error) {
      console.error('Lỗi khi thực hiện lệnh show:', error);
      
      const errorEmbed = new EmbedBuilder()
        .setColor('#ff0000')
        .setTitle('<:error:1383005371542798346> | LỖI')
        .setDescription(`Đã xảy ra lỗi khi tính toán doanh thu: ${error.message}`)
        .setTimestamp();

      if (interaction.deferred) {
        await interaction.editReply({ embeds: [errorEmbed] });
      } else {
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
      }
    }
  }
};
