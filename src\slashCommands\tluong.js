const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const playerHistoryDB = require('../data/playerHistoryDB');
const donateHistoryDB = require('../data/donateHistoryDB');
const playerDonateSalaryDB = require('../data/playerDonateSalaryDB');
const playerBonusSalaryDB = require('../data/playerBonusSalaryDB');
const addOnDB = require('../data/addOnDB');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('tluong')
    .setDescription('Tính lương và hiển thị bảng lương')
    .addUserOption(option =>
      option.setName('user')
        .setDescription('Người dùng cần xem bảng lương')
        .setRequired(true)),

  async execute(interaction) {
    try {
      // Ki<PERSON>m tra quyền
      const member = interaction.member;
      const hasAdminPermission = member.permissions.has('Administrator');
      const hasRequiredRole = member.roles.cache.has('1376500798896214108');
      
      if (!hasAdminPermission && !hasRequiredRole) {
        return await interaction.reply({
          content: '❌ | Bạn không có quyền sử dụng lệnh này!',
          ephemeral: true
        });
      }
      
      // Báo với Discord rằng bot đang xử lý
      await interaction.deferReply();
      
      // Lấy thông tin người dùng
      const targetUser = interaction.options.getUser('user');
      const playerId = targetUser.id;
      
      // Lấy lịch sử book của player
      const bookHistory = await playerHistoryDB.getHistory(playerId);
      
      // Lấy tổng số tiền khách đã donate
      const donateSalaryRaw = await playerDonateSalaryDB.getPlayerDonateSalary(playerId) || 0;
      // Trừ chiết khấu 10%
      const donateSalary = Math.round(donateSalaryRaw * 0.9);
      
      // Lấy night bills để loại trừ khỏi Bills thường
      const nightBills = await addOnDB.getPlayerNightBills(playerId);
      const nightBillIds = new Set(nightBills.map(nb => nb.billId));

      // Tính toán số giờ và tiền cho từng loại book (loại trừ night bills)
      let bookHours = 0;
      let bookMoney = 0;
      let gameHours = 0;
      let gameMoney = 0;
      let oncamHours = 0;
      let oncamMoney = 0;

      // Tính toán từ lịch sử book
      bookHistory.forEach(entry => {
        // Bỏ qua các bill có night option
        if (nightBillIds.has(entry.billId)) {
          return;
        }

        if (entry.type === 'book') {
          bookHours += entry.hours;
          bookMoney += entry.received;
        } else if (entry.type === 'game') {
          gameHours += entry.hours;
          gameMoney += entry.received;
        } else if (entry.type === 'oncam') {
          oncamHours += entry.hours;
          oncamMoney += entry.received;
        }
      });
      
      // Tính tổng số giờ và tiền từ book
      const totalHours = bookHours + gameHours + oncamHours;
      const totalBookMoney = bookMoney + gameMoney + oncamMoney;
      
      // Tính số tiền đã bị trừ (từ lệnh /luong hoặc hoàn bill/donate)
      let deductedMoney = 0;
      const luongHistory = await donateHistoryDB.getHistory(playerId);
      if (Array.isArray(luongHistory)) {
        deductedMoney = luongHistory
          .filter(entry => entry.paymentMethod === 'luong' && entry.amount < 0)
          .reduce((sum, entry) => sum + entry.amount, 0); // KHÔNG Math.abs
      }

      // Lấy thưởng lương (cộng lương)
      const bonusSalary = await playerBonusSalaryDB.getPlayerBonusSalary(playerId);

      // Tính tổng giờ và tiền từ night bills
      let nightHours = 0;
      let nightMoney = 0;

      for (const nightBill of nightBills) {
        try {
          const billInfo = await playerHistoryDB.getBillInfo(nightBill.billId);
          if (billInfo && (billInfo.isPaid === 0 || billInfo.isPaid === null)) {
            // Lấy số giờ từ bill gốc
            nightHours += billInfo.hours || 0;
            // Tính tổng tiền bill = tiền book gốc + tiền gia đêm
            const bookAmount = billInfo.total; // Tiền book gốc
            const nightAmount = nightBill.amount; // Tiền gia đêm (5k × giờ)
            const totalBillAmount = bookAmount + nightAmount; // Tổng bill
            nightMoney += Math.round(totalBillAmount * 0.8); // 20% chiết khấu
          }
        } catch (error) {
          console.error('Lỗi khi lấy thông tin bill cho night:', error);
        }
      }

      // Tính tổng lương (bao gồm cả night)
      const totalSalary = totalBookMoney + donateSalary + bonusSalary + deductedMoney + nightMoney;
      
      // Tạo embed bảng lương
      const embed = new EmbedBuilder()
        .setColor(interaction.client.embedColor || '#0099ff')
        .setTitle('BẢNG LƯONG')
        .setThumbnail(interaction.client.user.displayAvatarURL())
        .setDescription(
          `**Bills**\n` +
          `Book: ${bookHours}h - ${bookMoney.toLocaleString('vi-VN')} VNĐ\n` +
          `Game: ${gameHours}h - ${gameMoney.toLocaleString('vi-VN')} VNĐ\n` +
          `Oncam: ${oncamHours}h - ${oncamMoney.toLocaleString('vi-VN')} VNĐ\n\n` +
          `**Night**\n` +
          `Bill: ${nightHours}h - ${nightMoney.toLocaleString('vi-VN')} VNĐ\n\n` +
          `Tổng giờ: ${(totalHours + nightHours)}h\n` +
          `Tổng tiền: ${(totalBookMoney + nightMoney).toLocaleString('vi-VN')} VNĐ\n` +
          `Donate: ${donateSalary.toLocaleString('vi-VN')} VNĐ\n\n` +
          `Cộng lương: ${bonusSalary.toLocaleString('vi-VN')} VNĐ\n\n` +
          `Trừ lương: ${deductedMoney !== 0 ? '-' + Math.abs(deductedMoney).toLocaleString('vi-VN') : '0'} VNĐ\n\n` +
          `Tổng lương: ${totalSalary.toLocaleString('vi-VN')} VNĐ`
        )
        .setTimestamp()
        .setFooter({ text: '𝐓𝐡𝐞 𝐈𝐧𝐭𝐞𝐫𝐬𝐭𝐞𝐥𝐥𝐚𝐫 𝐁𝐨𝐨𝐤𝐢𝐧𝐠' });
      
      // Gửi embed
      await interaction.editReply({
        content: `${targetUser}, đây là bảng lương của bạn:`,
        embeds: [embed]
      });
      
    } catch (error) {
      console.error('Lỗi khi thực hiện lệnh tluong:', error);
      
      // Xử lý phản hồi lỗi
      if (interaction.deferred) {
        await interaction.editReply({
          content: `❌ | Đã xảy ra lỗi: ${error.message}`,
          ephemeral: true
        });
      } else {
        await interaction.reply({
          content: `❌ | Đã xảy ra lỗi: ${error.message}`,
          ephemeral: true
        });
      }
    }
  }
};