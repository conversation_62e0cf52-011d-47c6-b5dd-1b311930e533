const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const topStarDB = require('../data/topStarDB');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('topstar')
    .setDescription('Quản lý bảng TopStar')
    .addStringOption(option =>
      option.setName('type')
        .setDescription('Loại thao tác')
        .setRequired(true)
        .addChoices(
          { name: 'Reset All - Xóa toàn bộ bảng TopStar', value: 'reset_all' },
          { name: 'Delete Player - Xóa một player khỏi TopStar', value: 'delete_player' }
        ))
    .addUserOption(option =>
      option.setName('player')
        .setDescription('Player cần xóa khỏi TopStar (chỉ dùng khi chọn Delete Player)')
        .setRequired(false))
    .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

  async execute(interaction) {
    try {
      // <PERSON><PERSON><PERSON> tra quyền hạn (chỉ admin mới đượ<PERSON> sử dụng)
      if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
        return interaction.reply({ 
          content: '<:error:1383005371542798346> | Bạn không có quyền sử dụng lệnh này!', 
          ephemeral: true 
        });
      }

      const type = interaction.options.getString('type');
      const player = interaction.options.getUser('player');

      if (type === 'reset_all') {
        // Reset toàn bộ bảng TopStar
        await interaction.deferReply();
        
        try {
          await topStarDB.resetAllTopStar();
          
          const embed = new EmbedBuilder()
            .setColor(interaction.client.embedColor || '#0099ff')
            .setTitle('RESET TOPSTAR')
            .setThumbnail(interaction.client.user.displayAvatarURL())
            .setDescription(
              '<:done:1383009630581424250> **Đã reset toàn bộ bảng TopStar thành công!**\n\n' +
              '📊 Tất cả dữ liệu TopStar đã được xóa khỏi hệ thống.'
            )
            .setTimestamp()
            .setFooter({ text: 'INTERSTELLAR BOOKING' });

          await interaction.editReply({ embeds: [embed] });

          // Gửi log vào kênh log
          const logChannel = interaction.client.channels.cache.get('1377081349118365716');
          if (logChannel) {
            const logEmbed = new EmbedBuilder()
              .setColor('#ff9900')
              .setTitle('🔄 RESET TOPSTAR')
              .setThumbnail(interaction.client.user.displayAvatarURL())
              .setDescription(
                `<@${interaction.user.id}> đã reset toàn bộ bảng TopStar.`
              )
              .setTimestamp()
              .setFooter({ text: `ActionID: ${interaction.id}` });
            await logChannel.send({ embeds: [logEmbed] });
          }

        } catch (error) {
          console.error('Lỗi khi reset TopStar:', error);
          const errorEmbed = new EmbedBuilder()
            .setColor('#ff0000')
            .setTitle('<:error:1383005371542798346> LỖI')
            .setDescription('Đã xảy ra lỗi khi reset bảng TopStar. Vui lòng thử lại sau.')
            .setTimestamp();
          
          await interaction.editReply({ embeds: [errorEmbed] });
        }

      } else if (type === 'delete_player') {
        // Xóa một player khỏi TopStar
        if (!player) {
          return interaction.reply({
            content: '<:error:1383005371542798346> | Bạn cần chọn player để xóa khỏi TopStar!',
            ephemeral: true
          });
        }

        await interaction.deferReply();
        
        try {
          const result = await topStarDB.resetPlayerTopStar(player.id);
          
          if (result) {
            const embed = new EmbedBuilder()
              .setColor(interaction.client.embedColor || '#0099ff')
              .setTitle('XÓA PLAYER KHỎI TOPSTAR')
              .setThumbnail(interaction.client.user.displayAvatarURL())
              .setDescription(
                `<:done:1383009630581424250> **Đã xóa ${player} khỏi bảng TopStar thành công!**\n\n` +
                '📊 Tất cả dữ liệu TopStar của player này đã được xóa.'
              )
              .setTimestamp()
              .setFooter({ text: 'INTERSTELLAR BOOKING' });

            await interaction.editReply({ embeds: [embed] });

            // Gửi log vào kênh log
            const logChannel = interaction.client.channels.cache.get('1377081349118365716');
            if (logChannel) {
              const logEmbed = new EmbedBuilder()
                .setColor('#ff9900')
                .setTitle('XÓA PLAYER KHỎI TOPSTAR')
                .setThumbnail(interaction.client.user.displayAvatarURL())
                .setDescription(
                  `<@${interaction.user.id}> đã xóa <@${player.id}> khỏi bảng TopStar.`
                )
                .setTimestamp()
                .setFooter({ text: `ActionID: ${interaction.id}` });
              await logChannel.send({ embeds: [logEmbed] });
            }

          } else {
            const embed = new EmbedBuilder()
              .setColor('#ff9900')
              .setTitle('⚠️ THÔNG BÁO')
              .setDescription(
                `Player ${player} không tồn tại trong bảng TopStar hoặc đã được xóa trước đó.`
              )
              .setTimestamp();
            
            await interaction.editReply({ embeds: [embed] });
          }

        } catch (error) {
          console.error('Lỗi khi xóa player khỏi TopStar:', error);
          const errorEmbed = new EmbedBuilder()
            .setColor('#ff0000')
            .setTitle('<:error:1383005371542798346> LỖI')
            .setDescription('Đã xảy ra lỗi khi xóa player khỏi TopStar. Vui lòng thử lại sau.')
            .setTimestamp();
          
          await interaction.editReply({ embeds: [errorEmbed] });
        }
      }

    } catch (error) {
      console.error('Lỗi khi thực hiện lệnh topstar:', error);
      
      const errorEmbed = new EmbedBuilder()
        .setColor('#ff0000')
        .setTitle('<:error:1383005371542798346> LỖI HỆ THỐNG')
        .setDescription('Đã xảy ra lỗi khi thực hiện lệnh. Vui lòng thử lại sau.')
        .setTimestamp();

      if (interaction.replied || interaction.deferred) {
        await interaction.editReply({ embeds: [errorEmbed] });
      } else {
        await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
      }
    }
  }
};