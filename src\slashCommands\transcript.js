const { SlashCommandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const TranscriptManager = require('../utils/transcriptManager');
const transcriptManager = new TranscriptManager();

module.exports = {
    data: new SlashCommandBuilder()
        .setName('transcript')
        .setDescription('Tạo transcript của ticket hiện tại')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageChannels),
    
    async execute(interaction) {
        try {
            // Kiểm tra xem có phải ticket channel không
            if (!interaction.channel.name.startsWith('ticket-')) {
                return await interaction.reply({
                    content: '❌ Lệnh này chỉ có thể sử dụng trong ticket channel!',
                    ephemeral: true
                });
            }

            // Defer reply vì tạo transcript có thể mất thời gian
            await interaction.deferReply({ ephemeral: true });

            // Tạo ticket data từ channel name
            const channelName = interaction.channel.name;
            const ticketData = {
                ticketChannel: channelName,
                ticketType: 'Manual Transcript',
                ticketOwner: interaction.channel.topic || 'Unknown',
                createdAt: interaction.channel.createdAt,
                transcriptCreator: interaction.user.id
            };

            // Tạo transcript
            const transcriptFile = await transcriptManager.createTranscript(interaction.channel, ticketData);

            if (transcriptFile) {
                // Tạo embed thông báo thành công
                const successEmbed = new EmbedBuilder()
                    .setColor('#00ff00')
                    .setTitle('✅ Transcript đã được tạo thành công!')
                    .setDescription(`**File:** ${transcriptFile.fileName}\n**Kích thước:** ${(transcriptFile.attachment.attachment.length / 1024).toFixed(2)} KB`)
                    .addFields(
                        { name: '📋 Channel', value: channelName, inline: true },
                        { name: '👤 Người tạo', value: `<@${interaction.user.id}>`, inline: true },
                        { name: '⏰ Thời gian', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: false }
                    )
                    .setTimestamp();

                // Gửi transcript file kèm embed
                await interaction.editReply({
                    embeds: [successEmbed],
                    files: [transcriptFile.attachment]
                });

                console.log(`✅ Transcript được tạo thủ công: ${transcriptFile.fileName} bởi ${interaction.user.tag}`);
            } else {
                // Thông báo lỗi
                const errorEmbed = new EmbedBuilder()
                    .setColor('#ff0000')
                    .setTitle('❌ Lỗi tạo transcript')
                    .setDescription('Không thể tạo transcript. Vui lòng thử lại sau.')
                    .setTimestamp();

                await interaction.editReply({
                    embeds: [errorEmbed]
                });
            }

        } catch (error) {
            console.error('Lỗi trong lệnh transcript:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Lỗi hệ thống')
                .setDescription('Đã xảy ra lỗi khi thực hiện lệnh. Vui lòng thử lại sau.')
                .setTimestamp();

            if (interaction.deferred) {
                await interaction.editReply({
                    embeds: [errorEmbed]
                });
            } else {
                await interaction.reply({
                    embeds: [errorEmbed],
                    ephemeral: true
                });
            }
        }
    }
};