const { EmbedBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');

class BankNotificationSystem {
  constructor(client) {
    this.client = client;
    this.notificationChannelId = '1388072567901913152'; // Channel ID để gửi thông báo
    this.staffRoleId = '1376884726232514620'; // Role ID của staff
    this.transactionHistoryPath = path.join(__dirname, '../data/transactions.json');
    this.lastTransactionIds = new Set();
    
    // Load transaction history
    this.loadTransactionHistory();
    
    // Start monitoring (check every 30 seconds)
    this.startMonitoring();
  }

  loadTransactionHistory() {
    try {
      if (fs.existsSync(this.transactionHistoryPath)) {
        const data = JSON.parse(fs.readFileSync(this.transactionHistoryPath, 'utf8'));
        this.lastTransactionIds = new Set(data.processedTransactions || []);
        console.log(`Đã load ${this.lastTransactionIds.size} giao dịch đã xử lý`);
      }
    } catch (error) {
      console.error('Lỗi khi load transaction history:', error);
      this.lastTransactionIds = new Set();
    }
  }

  saveTransactionHistory() {
    try {
      const data = {
        processedTransactions: Array.from(this.lastTransactionIds),
        lastUpdate: new Date().toISOString()
      };
      fs.writeFileSync(this.transactionHistoryPath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('Lỗi khi save transaction history:', error);
    }
  }

  startMonitoring() {
    console.log('🏦 Bắt đầu monitoring giao dịch ngân hàng...');
    
    // Check every 30 seconds
    setInterval(async () => {
      await this.checkForNewTransactions();
    }, 30000);
    
    // Initial check
    setTimeout(() => {
      this.checkForNewTransactions();
    }, 5000);
  }

  async checkForNewTransactions() {
    try {
      // Giả lập việc lấy giao dịch từ API ngân hàng
      // Trong thực tế, bạn sẽ gọi API của ngân hàng ở đây
      const newTransactions = await this.fetchTransactionsFromBank();
      
      for (const transaction of newTransactions) {
        if (!this.lastTransactionIds.has(transaction.id)) {
          await this.sendTransactionNotification(transaction);
          this.lastTransactionIds.add(transaction.id);
        }
      }
      
      // Save processed transaction IDs
      if (newTransactions.length > 0) {
        this.saveTransactionHistory();
      }
      
    } catch (error) {
      console.error('Lỗi khi check giao dịch:', error);
    }
  }

  async fetchTransactionsFromBank() {
    // Đây là hàm giả lập - trong thực tế bạn sẽ gọi API ngân hàng
    // Ví dụ: Vietcombank API, Techcombank API, etc.
    
    try {
      // Giả lập API response
      const mockTransactions = [
        // Uncomment để test
        // {
        //   id: `txn_${Date.now()}`,
        //   amount: 500000,
        //   sender: 'NGUYEN VAN A',
        //   description: 'Chuyen tien booking',
        //   bank: 'Vietcombank',
        //   time: new Date().toLocaleString('vi-VN'),
        //   accountNumber: '**********'
        // }
      ];
      
      return mockTransactions;
      
    } catch (error) {
      console.error('Lỗi khi fetch transactions:', error);
      return [];
    }
  }

  async sendTransactionNotification(transactionData) {
    const channel = this.client.channels.cache.get(this.notificationChannelId);
    if (!channel) {
      console.error('Không tìm thấy notification channel');
      return;
    }

    const embed = new EmbedBuilder()
      .setTitle('💸 GIAO DỊCH MỚI')
      .setColor('#00ff00')
      .addFields(
        { name: '💰 Số tiền', value: `${transactionData.amount.toLocaleString()} VND`, inline: true },
        { name: '👤 Người gửi', value: transactionData.sender, inline: true },
        { name: '🏦 Ngân hàng', value: transactionData.bank, inline: true },
        { name: '📝 Nội dung CK', value: transactionData.description || 'Không có', inline: false },
        { name: '🔢 Số TK', value: transactionData.accountNumber || 'N/A', inline: true },
        { name: '⏰ Thời gian', value: transactionData.time, inline: true }
      )
      .setTimestamp()
      .setFooter({ text: `Transaction ID: ${transactionData.id}` });

    try {
      await channel.send({ 
        content: `<@&${this.staffRoleId}> **THÔNG BÁO GIAO DỊCH MỚI**`, 
        embeds: [embed] 
      });
      
      console.log(`✅ Đã gửi thông báo giao dịch: ${transactionData.amount.toLocaleString()} VND`);
      
    } catch (error) {
      console.error('Lỗi khi gửi thông báo:', error);
    }
  }

  // Hàm để manually add transaction (để test)
  async addTestTransaction(amount, sender, description) {
    const testTransaction = {
      id: `test_${Date.now()}`,
      amount: amount,
      sender: sender,
      description: description,
      bank: 'Test Bank',
      time: new Date().toLocaleString('vi-VN'),
      accountNumber: '**********'
    };

    await this.sendTransactionNotification(testTransaction);
    this.lastTransactionIds.add(testTransaction.id);
    this.saveTransactionHistory();
    
    return testTransaction;
  }

  // Hàm để check balance (có thể mở rộng sau)
  async checkBalance(bankCode) {
    try {
      // Implement API call tùy theo ngân hàng
      const balance = await this.callBankAPI(bankCode);
      return {
        success: true,
        balance: balance,
        lastUpdate: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  async callBankAPI(bankCode) {
    // Implement API call tùy theo ngân hàng
    // Lưu ý: Cần có API key và permissions từ ngân hàng
    
    switch(bankCode) {
      case 'vcb':
        // Vietcombank API
        // return await this.callVietcombankAPI();
        throw new Error('Vietcombank API chưa được implement');
        
      case 'tcb':
        // Techcombank API
        // return await this.callTechcombankAPI();
        throw new Error('Techcombank API chưa được implement');
        
      case 'bidv':
        // BIDV API
        // return await this.callBIDVAPI();
        throw new Error('BIDV API chưa được implement');
        
      default:
        throw new Error('Ngân hàng không được hỗ trợ');
    }
  }

  // Hàm để stop monitoring
  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      console.log('🛑 Đã dừng monitoring giao dịch');
    }
  }
}

module.exports = BankNotificationSystem;
