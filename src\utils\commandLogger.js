const { EmbedBuilder } = require('discord.js');

const COMMAND_LOG_CHANNEL_ID = '1398161748275757137';

/**
 * Log command usage to specified channel
 * @param {Object} client - Discord client
 * @param {Object} message - Message object (for prefix commands)
 * @param {Object} interaction - Interaction object (for slash commands)
 * @param {string} commandName - Name of the command
 * @param {Array} args - Command arguments
 * @param {string} commandType - Type: 'prefix' or 'slash'
 */
async function logCommand(client, message = null, interaction = null, commandName, args = [], commandType = 'prefix') {
  try {
    const logChannel = await client.channels.fetch(COMMAND_LOG_CHANNEL_ID).catch(() => null);
    if (!logChannel) {
      console.log('Command log channel not found');
      return;
    }

    let user, channel, guild, content;
    
    if (commandType === 'prefix' && message) {
      user = message.author;
      channel = message.channel;
      guild = message.guild;
      content = args.length > 0 ? args.join(' ') : 'Không có tham số';
    } else if (commandType === 'slash' && interaction) {
      user = interaction.user;
      channel = interaction.channel;
      guild = interaction.guild;
      
      // Tạo content từ options của slash command
      const options = [];
      if (interaction.options) {
        interaction.options.data.forEach(option => {
          if (option.type === 6) { // USER type
            options.push(`${option.name}: <@${option.value}>`);
          } else if (option.type === 7) { // CHANNEL type
            options.push(`${option.name}: <#${option.value}>`);
          } else if (option.type === 8) { // ROLE type
            options.push(`${option.name}: <@&${option.value}>`);
          } else {
            options.push(`${option.name}: ${option.value}`);
          }
        });
      }
      content = options.length > 0 ? options.join(', ') : 'Không có tham số';
    } else {
      return; // Invalid parameters
    }

    // Tạo embed log theo format mới
    const logEmbed = new EmbedBuilder()
      .setColor('#5865F2')
      .setAuthor({
        name: 'COMMAND HISTORY',
        iconURL: client.user.displayAvatarURL()
      })
      .setDescription(
        `<@${user.id}> **${user.username}**\n` +
        `**${commandType === 'slash' ? '/' : ''}${commandName}** ${commandType === 'slash' ? '/' : '#'} ${content}`
      )
      .addFields([
        {
          name: `${user.username}`,
          value:
            `**ID:** ${user.id} • **Hôm nay lúc** ${new Date().toLocaleTimeString('vi-VN', {
              hour: '2-digit',
              minute: '2-digit',
              timeZone: 'Asia/Ho_Chi_Minh'
            })}`,
          inline: false
        }
      ])
      .setTimestamp()
      .setThumbnail(user.displayAvatarURL());

    await logChannel.send({ embeds: [logEmbed] });
    
  } catch (error) {
    console.error('Error logging command:', error);
  }
}

/**
 * Log prefix command
 * @param {Object} client - Discord client
 * @param {Object} message - Message object
 * @param {string} commandName - Command name
 * @param {Array} args - Command arguments
 */
async function logPrefixCommand(client, message, commandName, args = []) {
  await logCommand(client, message, null, commandName, args, 'prefix');
}

/**
 * Log slash command
 * @param {Object} client - Discord client
 * @param {Object} interaction - Interaction object
 * @param {string} commandName - Command name
 */
async function logSlashCommand(client, interaction, commandName) {
  await logCommand(client, null, interaction, commandName, [], 'slash');
}

module.exports = {
  logCommand,
  logPrefixCommand,
  logSlashCommand
};
