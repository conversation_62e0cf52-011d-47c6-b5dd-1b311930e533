const { EmbedBuilder } = require('discord.js');
const giveawayDB = require('../data/giveawayDB');

/**
 * Hàm để chọn người thắng ngẫu nhiên từ danh sách người tham gia
 * @param {Array} participants - <PERSON><PERSON> sách ID người tham gia
 * @param {number} winnerCount - <PERSON><PERSON> người thắng
 * @returns {Array} Danh sách ID người thắng
 */
function selectRandomWinners(participants, winnerCount) {
  const winners = [];
  const participantsCopy = [...participants];
  for (let i = 0; i < Math.min(winnerCount, participantsCopy.length); i++) {
    const randomIndex = Math.floor(Math.random() * participantsCopy.length);
    winners.push(participantsCopy[randomIndex]);
    participantsCopy.splice(randomIndex, 1);
  }
  return winners;
}

/**
 * Hàm để hiển thị kết quả giveaway
 * @param {Channel} channel - Channel để gửi kết quả
 * @param {Object} giveaway - Thông tin giveaway
 * @param {Array} winners - <PERSON><PERSON> sách người thắng
 */
async function displayGiveawayResults(channel, giveaway, winners) {
  const winnerMentions = winners.length > 0
    ? winners.map(id => `<@${id}>`).join(', ')
    : 'Không có người thắng cuộc (không có người tham gia)';
  
  try {
    const giveawayMessage = await channel.messages.fetch(giveaway.messageId);
    await giveawayMessage.reply({
      content: `🎉 | Xin chúc mừng, **${winnerMentions}** đã trúng giveaway **${giveaway.prize}** được tổ chức bởi <@${giveaway.createdBy}>!`
    });
    console.log(`✅ Đã thông báo kết quả giveaway cho tin nhắn ID: ${giveaway.messageId}`);
  } catch (error) {
    console.error('❌ Lỗi khi thông báo kết quả giveaway:', error);
  }
}

/**
 * Hàm để kết thúc giveaway
 * @param {Client} client - Discord client
 * @param {string} messageId - ID tin nhắn giveaway
 */
async function endGiveaway(client, messageId) {
  console.log(`🎉 Bắt đầu kết thúc giveaway: ${messageId}`);

  const giveaway = giveawayDB.findGiveaway(messageId);
  if (!giveaway) {
    console.log(`❌ Không tìm thấy giveaway với ID: ${messageId}`);
    return;
  }

  if (giveaway.ended) {
    console.log(`⚠️ Giveaway ${messageId} đã kết thúc rồi`);
    return;
  }

  console.log(`📝 Đánh dấu giveaway ${messageId} đã kết thúc`);
  // Đánh dấu giveaway đã kết thúc
  giveawayDB.updateGiveaway(messageId, { ended: true });

  const guild = client.guilds.cache.get(giveaway.guildId);
  if (!guild) {
    console.log(`❌ Không tìm thấy guild: ${giveaway.guildId}`);
    return;
  }

  const channel = guild.channels.cache.get(giveaway.channelId);
  if (!channel) {
    console.log(`❌ Không tìm thấy channel: ${giveaway.channelId}`);
    return;
  }

  try {
    console.log(`📨 Đang fetch tin nhắn giveaway: ${giveaway.messageId}`);
    const giveawayMessage = await channel.messages.fetch(giveaway.messageId);

    console.log(`🎭 Đang kiểm tra reactions...`);
    const reaction = giveawayMessage.reactions.cache.get('🎉');

    let participants = [];
    if (reaction) {
      console.log(`👥 Tìm thấy ${reaction.count} reactions, đang fetch users...`);
      const users = await reaction.users.fetch();
      participants = users
        .filter(user => !user.bot)
        .map(user => user.id);

      console.log(`✅ Có ${participants.length} người tham gia: ${participants.join(', ')}`);

      // Cập nhật danh sách người tham gia
      giveawayDB.updateGiveaway(messageId, { participants });
    } else {
      console.log(`⚠️ Không tìm thấy reaction 🎉`);
    }

    console.log(`🎲 Đang chọn ${giveaway.winnerCount} người thắng từ ${participants.length} người tham gia...`);
    const winners = selectRandomWinners(participants, giveaway.winnerCount);
    console.log(`🏆 Người thắng: ${winners.join(', ')}`);

    const winnerMentions = winners.length > 0
      ? winners.map(id => `<@${id}>`).join(', ')
      : 'Không có người thắng cuộc (không có người tham gia)';

    console.log(`📝 Đang cập nhật embed...`);
    // Tạo embed kết thúc
    const endedEmbed = EmbedBuilder.from(giveawayMessage.embeds[0])
      .setColor('#203354')
      .setTitle('🎉 GIVEAWAY KẾT THÚC 🎉')
      .setDescription(`**${giveaway.prize}**\n\nNgười thắng: ${winnerMentions}\nNgười tổ chức: <@${giveaway.createdBy}>`)
      .spliceFields(0, 3)
      .setFooter({ text: `Giveaway kết thúc: ${new Date().toLocaleString('vi-VN')}` });

    console.log(`✏️ Đang edit tin nhắn giveaway...`);
    await giveawayMessage.edit({ embeds: [endedEmbed] });

    console.log(`📢 Đang gửi thông báo kết quả...`);
    await displayGiveawayResults(channel, giveaway, winners);

    console.log(`✅ Đã kết thúc giveaway: ${giveaway.prize}`);
  } catch (error) {
    console.error('❌ Lỗi khi kết thúc giveaway:', error);
  }
}

/**
 * Tạo giveaway mới
 * @param {Object} options - Tùy chọn giveaway
 * @returns {Object} Thông tin giveaway đã tạo
 */
function createGiveaway(options) {
  const { message, prize, winnerCount, duration, endTime } = options;
  
  const giveaway = {
    messageId: null, // Sẽ được set sau khi gửi tin nhắn
    channelId: message.channel.id,
    guildId: message.guild.id,
    prize: prize,
    winnerCount: winnerCount,
    endTime: endTime,
    participants: [],
    ended: false,
    createdBy: message.author.id,
    createdAt: Date.now()
  };

  return giveaway;
}

/**
 * Tạo embed cho giveaway
 * @param {Object} giveaway - Thông tin giveaway
 * @param {User} author - Người tạo giveaway
 * @returns {EmbedBuilder} Embed giveaway
 */
function createGiveawayEmbed(giveaway, author) {
  return new EmbedBuilder()
    .setColor('#203354')
    .setTitle('🎉 GIVEAWAY 🎉')
    .setDescription(
      `**${giveaway.prize}**\n\n` +
      `Nhấn 🎉 để tham gia!`
    )
    .addFields(
      { 
        name: '\u200B', 
        value: `Kết thúc sau: <t:${Math.floor(giveaway.endTime / 1000)}:R>\nTổ chức bởi: ${author.toString()}` 
      }
    )
    .setTimestamp()
    .setFooter({ 
      text: `Giveaway với ${giveaway.winnerCount} giải • Kết thúc lúc: ${new Date(giveaway.endTime).toLocaleString('vi-VN')}` 
    })
    .setThumbnail(author.displayAvatarURL({ dynamic: true }));
}

/**
 * Parse thời gian từ string
 * @param {string} durationArg - Chuỗi thời gian (vd: 1h, 30m, 2d)
 * @returns {number} Thời gian tính bằng milliseconds
 */
function parseDuration(durationArg) {
  const duration = durationArg.toLowerCase();
  const value = parseInt(duration);
  
  if (isNaN(value) || value <= 0) {
    throw new Error('Thời gian phải là số dương');
  }
  
  if (duration.endsWith('s')) {
    return value * 1000;
  } else if (duration.endsWith('m')) {
    return value * 60 * 1000;
  } else if (duration.endsWith('h')) {
    return value * 60 * 60 * 1000;
  } else if (duration.endsWith('d')) {
    return value * 24 * 60 * 60 * 1000;
  } else {
    throw new Error('Định dạng thời gian không hợp lệ. Sử dụng: s (giây), m (phút), h (giờ), d (ngày)');
  }
}

/**
 * Khởi tạo giveaway timers khi bot khởi động
 * @param {Client} client - Discord client
 */
function initializeGiveawayTimers(client) {
  const activeGiveaways = giveawayDB.getActiveGiveaways();
  const now = Date.now();

  console.log(`🎉 Khởi tạo ${activeGiveaways.length} giveaway timers...`);

  activeGiveaways.forEach(giveaway => {
    const timeLeft = giveaway.endTime - now;
    if (timeLeft <= 0) {
      // Giveaway đã hết hạn, kết thúc ngay
      endGiveaway(client, giveaway.messageId);
    } else {
      // Đặt timer cho giveaway
      setTimeout(() => {
        endGiveaway(client, giveaway.messageId);
      }, timeLeft);

      console.log(`⏰ Đặt timer cho giveaway: ${giveaway.prize} (còn ${Math.round(timeLeft / 1000)}s)`);
    }
  });
}

module.exports = {
  selectRandomWinners,
  displayGiveawayResults,
  endGiveaway,
  createGiveaway,
  createGiveawayEmbed,
  parseDuration,
  initializeGiveawayTimers
};
