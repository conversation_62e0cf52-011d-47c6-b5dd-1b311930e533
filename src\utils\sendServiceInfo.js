const { EmbedBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');

const idFilePath = path.join(__dirname, 'service_message_id.txt');

async function sendServiceInfo(client) {
  const channel = await client.channels.fetch('1341446703580188682');

  // Đọc ID tin nhắn đã gửi từ file (nếu có)
  let lastSentMessageId = null;
  if (fs.existsSync(idFilePath)) {
    lastSentMessageId = fs.readFileSync(idFilePath, 'utf8').trim();
  }

  // Kiểm tra xem đã gửi tin nhắn chưa
  if (lastSentMessageId) {
    try {
      const oldMsg = await channel.messages.fetch(lastSentMessageId);
      if (oldMsg) {
        // Nếu tin nhắn vẫn còn, không gửi lại nữa
        return;
      }
    } catch (e) {
      // Nếu không tì<PERSON> th<PERSON><PERSON> tin nhắ<PERSON> c<PERSON> (bị xóa), tiế<PERSON> tụ<PERSON> gử<PERSON> mới
    }
  }

  const embed = new EmbedBuilder()
    .setColor(client.embedColor || '#0099ff')
    .setDescription(
`# <a:dia:1376865720888987648> **VIP**

**Earth  <a:arrow:1376863038413738064>  70H**
<a:white:1376852619632316517> Đ<PERSON><PERSON>c quyền gửi ảnh tại <#1341447664666939412>
<a:white:1376852619632316517> Voucher free 3H book

**Wormhole  <a:arrow:1376863038413738064>   140H**
<a:white:1376852619632316517> Tặng 1 nhẫn \`Amora\`
<a:white:1376852619632316517> Voucher free 6H book

**Miller  <a:arrow:1376863038413738064>  280H**
<a:white:1376852619632316517> Voucher free 10H
<a:white:1376852619632316517> Nicho boost 1 tháng
<a:white:1376852619632316517> Role riêng custom

**Gargantua  <a:arrow:1376863038413738064>  560H** 
<a:white:1376852619632316517> Private room full quyền
<a:white:1376852619632316517> Autosponds riêng
<a:white:1376852619632316517> Voucher free 14H book

**Amelia  <a:arrow:1376863038413738064>  999H**
<a:white:1376852619632316517> Voucher free 18H book
<a:white:1376852619632316517> Nicho boost 3 tháng
<a:white:1376852619632316517> Profile vip độc quyền

**Mann  <a:arrow:1376863038413738064>  1668H**
<a:white:1376852619632316517> Free vé sv, thêm người (vĩnh viễn)
<a:white:1376852619632316517> Voucher free 24H book

**Edmunds  <a:arrow:1376863038413738064>  3883H**
<a:white:1376852619632316517> Voucher free 38H book
<a:white:1376852619632316517> Sale off 50% mọi item trong shop vĩnh viễn
<a:white:1376852619632316517> Bot nhạc riêng`
    );

  const sentMsg = await channel.send({ embeds: [embed] });
  // Lưu lại ID tin nhắn vừa gửi vào file
  fs.writeFileSync(idFilePath, sentMsg.id, 'utf8');

  // Gửi thêm embed PRIVATE ROLE/ROOM
  const embedPrivateRole = new EmbedBuilder()
  .setColor(client.embedColor || '#0099ff')
  .setDescription(
`## <a:emohi:1376865722789269545> **PRIVATE ROLE/ROOM** <a:emohi:1376865722789269545>


<a:Item_CyanStars:1376861935294742598> Role gửi ảnh tại <#1341447664666939412> : 50K
<a:Item_CyanStars:1376861935294742598> Role riêng custom (4 slot): 200K


<a:Item_CyanStars:1376861935294742598> Role riêng **có quyền** : 400K
<a:arrow:1376863038413738064> Bao gồm: gửi ảnh, dịch chuyển thành viên, mute mic, kick khỏi room voice.


<a:Item_CyanStars:1376861935294742598> Room riêng full quyền : 400K 


<a:arrow:1376863038413738064> Đối với những khách có nhu cầu hiển thị role riêng khi đã có role vip, vui lòng liên hệ Support để được hỗ trợ.`
);

await channel.send({ embeds: [embedPrivateRole] });
}

function getVipEmbed(client) {
  return new EmbedBuilder()
    .setColor(client.embedColor || '#0099ff')
    .setDescription(
`# <a:dia:1376865720888987648> **VIP**

**Earth  <a:arrow:1376863038413738064>  70H**
<a:white:1376852619632316517> Được quyền gửi ảnh tại <#1341447664666939412>
<a:white:1376852619632316517> Voucher free 3H book

**Wormhole  <a:arrow:1376863038413738064>   140H**
<a:white:1376852619632316517> Tặng 1 nhẫn \`Amora\`
<a:white:1376852619632316517> Voucher free 6H book

**Miller  <a:arrow:1376863038413738064>  280H**
<a:white:1376852619632316517> Voucher free 10H
<a:white:1376852619632316517> Nicho boost 1 tháng
<a:white:1376852619632316517> Role riêng custom

**Gargantua  <a:arrow:1376863038413738064>  560H** 
<a:white:1376852619632316517> Private room full quyền
<a:white:1376852619632316517> Autosponds riêng
<a:white:1376852619632316517> Voucher free 14H book

**Amelia  <a:arrow:1376863038413738064>  999H**
<a:white:1376852619632316517> Voucher free 18H book
<a:white:1376852619632316517> Nicho boost 3 tháng
<a:white:1376852619632316517> Profile vip độc quyền

**Mann  <a:arrow:1376863038413738064>  1668H**
<a:white:1376852619632316517> Free vé sv, thêm người (vĩnh viễn)
<a:white:1376852619632316517> Voucher free 24H book

**Edmunds  <a:arrow:1376863038413738064>  3883H**
<a:white:1376852619632316517> Voucher free 38H book
<a:white:1376852619632316517> Sale off 50% mọi item trong shop vĩnh viễn
<a:white:1376852619632316517> Bot nhạc riêng`
    );
}

module.exports = { sendServiceInfo, getVipEmbed };