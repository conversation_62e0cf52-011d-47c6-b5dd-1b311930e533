const { EmbedBuilder } = require('discord.js');

async function sendShopRings(client) {

  console.log('📍 sendShopRings: Đã disable - Shop rings hiện tại sử dụng menu embed');
  return;
}

function getShopRingsEmbed(client) {
  return new EmbedBuilder()
  .setAuthor({ name: '𝐓𝐡𝐞 𝐈𝐧𝐭𝐞𝐫𝐬𝐭𝐞𝐥𝐥𝐚𝐫', iconURL: client.user.displayAvatarURL() })
  .setTitle('**𝐓𝐡𝐞 𝐈𝐧𝐭𝐞𝐫𝐬𝐭𝐞𝐥𝐥𝐚𝐫 Shop Rings**')
  .setDescription(
    `\`01.\` <:firstdate:1395218976115789925> **First date**\n• Giá: 100.000 VNĐ\n\n` +
    `\`02.\` <:eternia:1395218967681040527> **Eternia**\n• Giá: 200.000 VNĐ\n\n` +
    `\`03.\` <:amora:1395218962815651924> **Amora**\n• Giá: 300.000 VNĐ\n\n` +
    `\`04.\` <:serenity:1395218985401716867> **Serenity**\n• Giá: 400.000 VNĐ\n\n` +
    `\`05.\` <:unity:1395218993220026418> **Unity**\n• Giá: 500.000 VNĐ`
   )
  .setFooter({ text: 'Hôm nay lúc ' + new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' }) })
  .setColor(client.embedColor || '#0099ff');
}

module.exports = { sendShopRings, getShopRingsEmbed  };