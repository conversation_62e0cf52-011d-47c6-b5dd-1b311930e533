const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle } = require('discord.js');
const fs = require('fs');
const path = require('path');

async function sendTicketPanel(client) {
  const channelId = '1341447407933722635';
  const channel = await client.channels.fetch(channelId);
  const idPath = path.join(__dirname, 'ticket_message_id.txt');
  let panelMsgId = null;
  if (fs.existsSync(idPath)) {
    panelMsgId = fs.readFileSync(idPath, 'utf8').trim();
  }
  if (panelMsgId) {
    try {
      const msg = await channel.messages.fetch(panelMsgId);
      if (msg) return; // Panel đã tồn tại, không gửi lại
    } catch (e) {
      // Tin nhắn không còn tồn tại, tiếp tục gửi mới
    }
  }
  const embed = new EmbedBuilder()
    .setTitle('**𝐓𝐡𝐞 𝐈𝐧𝐭𝐞𝐫𝐬𝐭𝐞𝐥𝐥𝐚𝐫**')
    .setColor(client.embedColor || '#0099ff')
    .setThumbnail(client.user.displayAvatarURL({ dynamic: true }))
    .setDescription(
      '<a:caarrow:1376861195000217673> Nhấn vào **Booking** để có thể gặp được các bạn <#1341447431107117128> và <#1341447441345548373> bạn ưng ý nhé\n\n' +
      '<a:caarrow:1376861195000217673> Nhấn vào **Tuyển dụng** nếu bạn muốn tham gia vào đại gia đình **𝐓𝐡𝐞 𝐈𝐧𝐭𝐞𝐫𝐬𝐭𝐞𝐥𝐥𝐚𝐫** nhé\n\n' +
      '<a:caarrow:1376861195000217673> Nhấn vào **Support** nếu bạn muốn khiếu nại hay cần hỗ trợ. Staff và Support sẽ luôn có mặt 24/24 để hỗ trợ bạn nè\n\n' 
    );
  const row = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId('ticket_booking')
      .setLabel('Booking')
      .setEmoji('<a:LovelyTalkingToYou:1379336681735327744>')
      .setStyle(ButtonStyle.Secondary),
    new ButtonBuilder()
      .setCustomId('ticket_support')
      .setLabel('Support')
      .setEmoji('<a:RIngingTelephone:1379335521024872589>')
      .setStyle(ButtonStyle.Secondary),
    new ButtonBuilder()
      .setCustomId('ticket_recruitment')
      .setLabel('Tuyển dụng')
      .setEmoji('<a:CrystalHeart:1379336466148360343>')
      .setStyle(ButtonStyle.Secondary),
  );
  const sentMsg = await channel.send({ embeds: [embed], components: [row] });
  fs.writeFileSync(idPath, sentMsg.id, 'utf8');
}

module.exports = sendTicketPanel;