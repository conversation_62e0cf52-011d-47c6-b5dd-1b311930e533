const { Embed<PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

class TicketAutoReply {
  constructor() {
    this.pendingTickets = new Map(); // Map để lưu trữ các ticket đang chờ
    this.staffRoles = ['1376884726232514620', '1376500798896214108']; // Role IDs của staff
  }

  /**
   * Bắt đầu theo dõi ticket mới được tạo
   * @param {Object} ticketInfo - Thông tin ticket
   */
  startTracking(ticketInfo) {
    const { threadId, userId, topic, createdAt } = ticketInfo;
    
    console.log(`🎫 Bắt đầu theo dõi ticket: ${threadId} (${topic})`);
    
    // Lưu thông tin ticket
    this.pendingTickets.set(threadId, {
      userId,
      topic,
      createdAt,
      hasStaffResponse: false,
      timeoutId: null
    });

    // Tạo timeout 3 phút
    const timeoutId = setTimeout(() => {
      this.checkAndSendAutoReply(threadId);
    }, 1 * 60 * 1000); // 3 phút = 180,000ms

    // Lưu timeout ID để có thể cancel nếu cần
    this.pendingTickets.get(threadId).timeoutId = timeoutId;
  }

  /**
   * Đánh dấu ticket đã có staff phản hồi
   * @param {string} threadId - ID của thread ticket
   * @param {string} userId - ID của user phản hồi
   */
  markStaffResponse(threadId, userId) {
    const ticketInfo = this.pendingTickets.get(threadId);
    if (!ticketInfo) return;

    // Kiểm tra xem user có phải staff không
    const isStaff = this.isStaffMember(userId);
    if (!isStaff) return;

    console.log(`✅ Staff đã phản hồi ticket: ${threadId}`);
    
    // Đánh dấu đã có staff response
    ticketInfo.hasStaffResponse = true;
    
    // Hủy timeout
    if (ticketInfo.timeoutId) {
      clearTimeout(ticketInfo.timeoutId);
      ticketInfo.timeoutId = null;
    }

    // Xóa khỏi danh sách pending
    this.pendingTickets.delete(threadId);
  }

  /**
   * Kiểm tra và gửi auto reply nếu cần
   * @param {string} threadId - ID của thread ticket
   */
  async checkAndSendAutoReply(threadId) {
    const ticketInfo = this.pendingTickets.get(threadId);
    if (!ticketInfo) return;

    // Nếu đã có staff response thì không gửi auto reply
    if (ticketInfo.hasStaffResponse) {
      this.pendingTickets.delete(threadId);
      return;
    }

    console.log(`⏰ Gửi auto reply cho ticket: ${threadId}`);
    
    try {
      // Lấy thread channel
      const thread = await this.getThreadById(threadId);
      if (!thread) {
        console.error(`❌ Không tìm thấy thread: ${threadId}`);
        this.pendingTickets.delete(threadId);
        return;
      }

      // Kiểm tra lại xem có tin nhắn từ staff trong 3 phút qua không
      const hasRecentStaffMessage = await this.checkRecentStaffMessages(thread);
      if (hasRecentStaffMessage) {
        console.log(`✅ Đã có tin nhắn từ staff trong thread: ${threadId}`);
        this.pendingTickets.delete(threadId);
        return;
      }

      // Gửi auto reply
      await this.sendAutoReply(thread, ticketInfo);
      
      // Xóa khỏi danh sách pending
      this.pendingTickets.delete(threadId);

    } catch (error) {
      console.error(`❌ Lỗi khi gửi auto reply cho ticket ${threadId}:`, error);
      this.pendingTickets.delete(threadId);
    }
  }

  /**
   * Kiểm tra xem có tin nhắn từ staff trong thời gian gần đây không
   * @param {Object} thread - Thread channel
   * @returns {boolean}
   */
  async checkRecentStaffMessages(thread) {
    try {
      // Lấy 50 tin nhắn gần nhất
      const messages = await thread.messages.fetch({ limit: 50 });
      
      // Kiểm tra từng tin nhắn
      for (const message of messages.values()) {
        // Bỏ qua tin nhắn từ bot
        if (message.author.bot) continue;
        
        // Kiểm tra xem author có phải staff không
        const member = message.member;
        if (!member) continue;
        
        const isStaff = member.roles.cache.some(role => this.staffRoles.includes(role.id));
        if (isStaff) {
          return true;
        }
      }
      
      return false;
    } catch (error) {
      console.error('Lỗi khi kiểm tra tin nhắn staff:', error);
      return false;
    }
  }

  /**
   * Gửi auto reply message
   * @param {Object} thread - Thread channel
   * @param {Object} ticketInfo - Thông tin ticket
   */
  async sendAutoReply(thread, ticketInfo) {
    // Tạo embed (sẽ được customize sau)
    const autoReplyEmbed = new EmbedBuilder()
      .setColor(this.client?.embedColor || '#ffaa00')
      .setAuthor({
            name: 'AUTO REPLY',
            iconURL: this.client?.user?.displayAvatarURL() || 'https://cdn.discordapp.com/embed/avatars/0.png'
          })
      .setDescription(
        `Xin chào! Mình thấy bạn đã tạo ticket **${ticketInfo.topic}** nhưng chưa nhận được phản hồi. Hãy để mình hỗ trợ bạn nhé!\n\n` +
        `Hãy cho mình biết bạn cần hỗ trợ gì?\n` +
        `• **Available** - Ping danh sách những player đang online có thể nhận bill bây giờ.\n` +
        `• **Rent** - Thuê player được chỉnh định.\n` +
        `• **Support** - Ping Staff và Support để hỗ trợ những vấn đề khác.`
      )
      .setTimestamp()
      .setFooter({ text: 'Tin nhắn tự động • Chúng tôi sẽ phản hồi sớm nhất có thể' });

    // Tạo action row với các nút hỗ trợ
    const supportRow = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId('ticket_available')
        .setLabel('Available')
        .setEmoji('<a:emoji_117:1376849763332390933>')
        .setStyle(ButtonStyle.Success),
      new ButtonBuilder()
        .setCustomId('ticket_rent')
        .setLabel('Rent')
        .setEmoji('<a:emoji_99:1378452250321752095>')
        .setStyle(ButtonStyle.Primary),
      new ButtonBuilder()
        .setCustomId('ticket_call_support')
        .setLabel('Support')
        .setEmoji('<a:support:1400748127514001489>')
        .setStyle(ButtonStyle.Secondary)
    );

    // Gửi tin nhắn
    await thread.send({
      embeds: [autoReplyEmbed],
      components: [supportRow]
    });

    console.log(`✅ Đã gửi auto reply cho ticket: ${thread.id}`);
  }

  /**
   * Kiểm tra xem user có phải staff không
   * @param {string} userId - ID của user
   * @returns {boolean}
   */
  isStaffMember(userId) {
    // Tạm thời return false, sẽ được implement khi có guild member
    // Cần access vào guild member để check roles
    return false;
  }

  /**
   * Lấy thread channel theo ID
   * @param {string} threadId - ID của thread
   * @returns {Object|null}
   */
  async getThreadById(threadId) {
    try {
      // Cần access vào client để fetch channel
      // Sẽ được inject từ bên ngoài
      if (this.client) {
        return await this.client.channels.fetch(threadId);
      }
      return null;
    } catch (error) {
      console.error('Lỗi khi lấy thread:', error);
      return null;
    }
  }

  /**
   * Set client instance
   * @param {Object} client - Discord client
   */
  setClient(client) {
    this.client = client;
  }

  /**
   * Xóa ticket khỏi tracking khi đóng
   * @param {string} threadId - ID của thread
   */
  stopTracking(threadId) {
    const ticketInfo = this.pendingTickets.get(threadId);
    if (!ticketInfo) return;

    console.log(`🛑 Dừng theo dõi ticket: ${threadId}`);

    // Hủy timeout nếu có
    if (ticketInfo.timeoutId) {
      clearTimeout(ticketInfo.timeoutId);
    }

    // Xóa khỏi danh sách
    this.pendingTickets.delete(threadId);
  }

  /**
   * Lấy thống kê tickets đang pending
   * @returns {Object}
   */
  getStats() {
    return {
      pendingTickets: this.pendingTickets.size,
      tickets: Array.from(this.pendingTickets.entries()).map(([threadId, info]) => ({
        threadId,
        topic: info.topic,
        createdAt: info.createdAt,
        hasStaffResponse: info.hasStaffResponse
      }))
    };
  }

  /**
   * Xử lý khi có tin nhắn mới trong ticket
   * @param {Object} message - Discord message object
   */
  handleMessage(message) {
    // Bỏ qua nếu không phải trong thread
    if (!message.channel.isThread()) return;
    
    // Bỏ qua tin nhắn từ bot
    if (message.author.bot) return;

    const threadId = message.channel.id;
    
    // Kiểm tra xem có đang track ticket này không
    if (!this.pendingTickets.has(threadId)) return;

    // Kiểm tra xem user có phải staff không
    const member = message.member;
    if (!member) return;

    const isStaff = member.roles.cache.some(role => this.staffRoles.includes(role.id));
    if (isStaff) {
      this.markStaffResponse(threadId, message.author.id);
    }
  }
}

// Tạo instance singleton
const ticketAutoReply = new TicketAutoReply();

module.exports = ticketAutoReply;
