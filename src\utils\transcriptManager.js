const fs = require('fs').promises;
const path = require('path');
const { AttachmentBuilder } = require('discord.js');

class TranscriptManager {
    constructor() {
        this.transcriptsDir = path.join(__dirname, '..', 'transcripts');
        this.ensureTranscriptsDir();
    }

    async ensureTranscriptsDir() {
        try {
            await fs.access(this.transcriptsDir);
        } catch {
            await fs.mkdir(this.transcriptsDir, { recursive: true });
        }
    }

    async createTranscript(channel, ticketData) {
        try {
            // L<PERSON>y tất cả tin nhắn trong channel
            const messages = await this.fetchAllMessages(channel);
            
            // Tạo nội dung transcript
            const transcriptContent = this.formatTranscript(messages, ticketData);
            
            // Tạo tên file
            const fileName = `ticket-${channel.name}-${Date.now()}.html`;
            const filePath = path.join(this.transcriptsDir, fileName);
            
            // Lưu file
            await fs.writeFile(filePath, transcriptContent, 'utf8');
            
            return {
                filePath,
                fileName,
                attachment: new AttachmentBuilder(filePath, { name: fileName })
            };
        } catch (error) {
            console.error('Lỗi khi tạo transcript:', error);
            throw error;
        }
    }

    async fetchAllMessages(channel) {
        const messages = [];
        let lastMessageId;

        while (true) {
            const options = { limit: 100 };
            if (lastMessageId) {
                options.before = lastMessageId;
            }

            const batch = await channel.messages.fetch(options);
            if (batch.size === 0) break;

            messages.push(...batch.values());
            lastMessageId = batch.last().id;
        }

        return messages.reverse(); // Sắp xếp theo thứ tự thời gian
    }

    formatTranscript(messages, ticketData) {
        const formatDate = (date) => {
            return new Intl.DateTimeFormat('vi-VN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                timeZone: 'Asia/Ho_Chi_Minh'
            }).format(date);
        };

        let html = `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ticket Transcript - ${ticketData?.ticketChannel || 'Unknown'}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #36393f;
            color: #dcddde;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #2f3136;
            border-radius: 8px;
            padding: 20px;
        }
        .header {
            border-bottom: 2px solid #4f545c;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        .ticket-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .info-item {
            background-color: #40444b;
            padding: 15px;
            border-radius: 6px;
        }
        .info-label {
            font-weight: bold;
            color: #7289da;
            margin-bottom: 5px;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-left: 4px solid #7289da;
            background-color: #40444b;
            border-radius: 0 6px 6px 0;
        }
        .message-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        .author {
            font-weight: bold;
            color: #ffffff;
            margin-right: 10px;
        }
        .timestamp {
            color: #72767d;
            font-size: 0.875rem;
        }
        .message-content {
            line-height: 1.4;
            word-wrap: break-word;
        }
        .attachment {
            background-color: #2f3136;
            border: 1px solid #4f545c;
            border-radius: 4px;
            padding: 8px;
            margin-top: 8px;
            color: #7289da;
        }
        .bot-message {
            border-left-color: #faa61a;
        }
        .system-message {
            border-left-color: #43b581;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 Ticket Transcript</h1>
            <div class="ticket-info">
                <div class="info-item">
                    <div class="info-label">Kênh Ticket:</div>
                    <div>${ticketData?.ticketChannel || 'Unknown'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Người tạo:</div>
                    <div>${ticketData?.creatorTag || 'Unknown'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Loại ticket:</div>
                    <div>${ticketData?.ticketType || 'Unknown'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Thời gian tạo:</div>
                    <div>${ticketData?.createdAt ? formatDate(new Date(ticketData.createdAt * 1000)) : 'Unknown'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Thời gian đóng:</div>
                    <div>${formatDate(new Date())}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Tổng tin nhắn:</div>
                    <div>${messages.length}</div>
                </div>
            </div>
        </div>
        
        <div class="messages">
`;

        messages.forEach(message => {
            const messageClass = message.author.bot ? 'message bot-message' : 
                               message.system ? 'message system-message' : 'message';
            
            html += `
            <div class="${messageClass}">
                <div class="message-header">
                    <span class="author">${message.author.displayName || message.author.username}</span>
                    <span class="timestamp">${formatDate(message.createdAt)}</span>
                </div>
                <div class="message-content">${this.escapeHtml(message.content || '[Tin nhắn trống]')}</div>
`;
            
            // Thêm attachments nếu có
            if (message.attachments.size > 0) {
                message.attachments.forEach(attachment => {
                    html += `
                    <div class="attachment">
                        📎 <a href="${attachment.url}" target="_blank">${attachment.name}</a>
                        (${this.formatFileSize(attachment.size)})
                    </div>
`;
                });
            }
            
            html += `            </div>
`;
        });

        html += `
        </div>
    </div>
</body>
</html>
`;

        return html;
    }

    escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, m => map[m]);
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async cleanupOldTranscripts(daysOld = 30) {
        try {
            const files = await fs.readdir(this.transcriptsDir);
            const cutoffTime = Date.now() - (daysOld * 24 * 60 * 60 * 1000);
            
            for (const file of files) {
                const filePath = path.join(this.transcriptsDir, file);
                const stats = await fs.stat(filePath);
                
                if (stats.mtime.getTime() < cutoffTime) {
                    await fs.unlink(filePath);
                    console.log(`Đã xóa transcript cũ: ${file}`);
                }
            }
        } catch (error) {
            console.error('Lỗi khi dọn dẹp transcript cũ:', error);
        }
    }
}

module.exports = TranscriptManager;