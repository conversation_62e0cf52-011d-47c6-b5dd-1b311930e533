const cron = require('node-cron');
const weeklyMessageDB = require('../data/weeklyMessageDB');

/**
 * Khởi tạo scheduler để tự động reset dữ liệu tuần
 */
function initWeeklyScheduler() {
  // Chạy vào 00:00 thứ 2 hàng tuần (reset tuần mới)
  // Cron format: giây phút giờ ngày tháng thứ_trong_tuần
  // 0 0 0 * * 1 = 00:00:00 thứ 2 hàng tuần
  cron.schedule('0 0 0 * * 1', async () => {
    try {
      console.log('🔄 Bắt đầu reset dữ liệu tuần cũ...');
      
      const { weekStart, weekEnd } = weeklyMessageDB.getCurrentWeekRange();
      console.log(`📅 Tuần mới: ${weekStart} đến ${weekEnd}`);
      
      // Reset dữ liệu tuần cũ
      await weeklyMessageDB.resetOldWeekData();
      
      console.log('✅ Đã reset dữ liệu tuần cũ thành công!');
      
    } catch (error) {
      console.error('❌ Lỗi khi reset dữ liệu tuần:', error);
    }
  }, {
    scheduled: true,
    timezone: "Asia/Ho_Chi_Minh" // Múi giờ Việt Nam
  });

  // Chạy backup dữ liệu vào 23:59 chủ nhật (trước khi reset)
  cron.schedule('59 59 23 * * 0', async () => {
    try {
      console.log('💾 Bắt đầu backup dữ liệu tuần...');
      
      // Có thể thêm logic backup ở đây nếu cần
      // Ví dụ: export dữ liệu ra file, gửi thống kê tổng kết tuần...
      
      console.log('✅ Backup dữ liệu tuần hoàn tất!');
      
    } catch (error) {
      console.error('❌ Lỗi khi backup dữ liệu tuần:', error);
    }
  }, {
    scheduled: true,
    timezone: "Asia/Ho_Chi_Minh"
  });

  console.log('⏰ Weekly scheduler đã được khởi tạo:');
  console.log('   • Reset dữ liệu: 00:00 thứ 2 hàng tuần');
  console.log('   • Backup dữ liệu: 23:59 chủ nhật hàng tuần');
  console.log('   • Múi giờ: Asia/Ho_Chi_Minh');
}

/**
 * Kiểm tra trạng thái scheduler
 */
function getSchedulerStatus() {
  const { weekStart, weekEnd } = weeklyMessageDB.getCurrentWeekRange();
  
  return {
    currentWeek: `${weekStart} đến ${weekEnd}`,
    nextReset: getNextMondayTime(),
    timezone: 'Asia/Ho_Chi_Minh',
    isActive: true
  };
}

/**
 * Tính thời gian thứ 2 tiếp theo
 */
function getNextMondayTime() {
  const now = new Date();
  const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, ...
  
  // Tính số ngày đến thứ 2 tiếp theo
  let daysUntilMonday;
  if (dayOfWeek === 0) { // Chủ nhật
    daysUntilMonday = 1;
  } else if (dayOfWeek === 1) { // Thứ 2
    daysUntilMonday = 7; // Thứ 2 tuần sau
  } else {
    daysUntilMonday = 8 - dayOfWeek; // Thứ 2 tuần sau
  }
  
  const nextMonday = new Date(now);
  nextMonday.setDate(now.getDate() + daysUntilMonday);
  nextMonday.setHours(0, 0, 0, 0);
  
  return nextMonday.toLocaleString('vi-VN', {
    timeZone: 'Asia/Ho_Chi_Minh',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * Test function để kiểm tra scheduler (chỉ dùng cho development)
 */
async function testWeeklyReset() {
  try {
    console.log('🧪 Testing weekly reset...');
    
    const { weekStart, weekEnd } = weeklyMessageDB.getCurrentWeekRange();
    console.log(`📅 Tuần hiện tại: ${weekStart} đến ${weekEnd}`);
    
    // Không thực sự reset, chỉ log
    console.log('✅ Test completed successfully!');
    
    return {
      success: true,
      currentWeek: `${weekStart} đến ${weekEnd}`,
      message: 'Test reset function working properly'
    };
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = {
  initWeeklyScheduler,
  getSchedulerStatus,
  testWeeklyReset
};
