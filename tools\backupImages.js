const fs = require('fs');
const path = require('path');
const fetch = require('node-fetch');

const keywordsPath = path.join(__dirname, '../src/data/keywords.json');
const imagesDir = 'D:/Discord BOT Status/Interstellar/images';

if (!fs.existsSync(imagesDir)) fs.mkdirSync(imagesDir, { recursive: true });

async function downloadImage(url, filePath) {
  try {
    const res = await fetch(url);
    if (!res.ok) throw new Error(`HTTP ${res.status}`);
    const dest = fs.createWriteStream(filePath);
    await new Promise((resolve, reject) => {
      res.body.pipe(dest);
      res.body.on('error', reject);
      dest.on('finish', resolve);
    });
    console.log(`Đã backup: ${filePath}`);
  } catch (e) {
    console.log(`Lỗi backup ${url}: ${e.message}`);
  }
}

async function backupAllImages() {
  const keywords = JSON.parse(fs.readFileSync(keywordsPath, 'utf8'));
  for (const item of keywords) {
    if (item.image && typeof item.image === 'string' && item.image.startsWith('http')) {
      const ext = path.extname(item.image).split('?')[0] || '.jpg';
      const safeKeyword = item.keyword.replace(/[\\/:*?"<>|]/g, '_');
      const fileName = `${safeKeyword}${ext}`;
      const filePath = path.join(imagesDir, fileName);
      if (!fs.existsSync(filePath)) {
        await downloadImage(item.image, filePath);
      } else {
        console.log(`Đã tồn tại: ${fileName}`);
      }
    }
  }
}

backupAllImages();